﻿using WSA.Retail.Integration.Manage.EventProcessing;
using WSA.Retail.Integration.Models.Manufacturers;

namespace WSA.Retail.Integration.Manage.Models.Manufacturers;

public class ManufacturerAdapter : IEventHubEntityAdapter<ManufacturerEventHubEvent, Manufacturer>
{
    public Manufacturer ToDomainModel(
        ManufacturerEventHubEvent source, 
        string externalSystemCode)
    {
        return new Manufacturer
        {
            ExternalSystemCode = externalSystemCode,
            ExternalCode = source.Id?.ToString(),
            Code = source.FaxNumber ?? "",
            Name = source.Name
        };
    }
}
