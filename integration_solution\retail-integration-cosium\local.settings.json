{"IsEncrypted": false, "Values": {"apiBaseUrl": "********************************************/v1.0", "AzureWebJobsStorage": "UseDevelopmentStorage=true", "EventGridAccessKey": "DJf3bXqcYJahAUKcLIsiseftCceXRtO0FgF2xjuzv0kbBxhLzIIIJQQJ99AKAC5T7U2XJ3w3AAABAZEGfeCl", "EventGridEndpoint": "https://wsa-retail-integration-frprod.francecentral-1.eventgrid.azure.net/api/events", "ExternalSystemCode": "COSIUM", "FUNCTIONS_WORKER_RUNTIME": "dotnet-isolated", "Schedule": "0 */5 * * * *", "SqlConnectionString": "Server=tcp:retail-integration-fr.database.windows.net,1433;Initial Catalog=fr_prod;Persist Security Info=False;User ID=cosium;Password=*********;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;", "SubscriptionKey": "9d707c18817f478686b310c3b7a5d6cc"}}