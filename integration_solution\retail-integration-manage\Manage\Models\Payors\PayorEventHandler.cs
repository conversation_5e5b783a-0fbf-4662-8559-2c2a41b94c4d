﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.Functions.Worker;
using WSA.Retail.Integration.Events;
using WSA.Retail.Integration.Manage.EventProcessing;

namespace WSA.Retail.Integration.Manage.Models.Payors;

public class PayorEventHandler(
    PayorFromEventHandler fromEventHandler,
    PayorGetByQueryHandler queryHandler) : IEventHandler
{
    protected readonly PayorFromEventHandler _fromEventHandler = fromEventHandler;
    protected readonly PayorGetByQueryHandler _queryHandler = queryHandler;

    public async Task<bool> HandleFromQueueAsync(EventHubMessage message)
    {
        return await _fromEventHandler.HandleFromQueueAsync(message);
    }

    public async Task<bool> HandleToQueueAsync(Event ev)
    {
        return await Task.FromResult(true);
    }

    [Function("PayorGetByQuery")]
    public async Task<IActionResult> GetByQueryAsync([HttpTrigger(AuthorizationLevel.Function, "get", Route = "payors")] HttpRequest req)
    {
        return await _queryHandler.GetByQueryInternalAsync(req);
    }
}