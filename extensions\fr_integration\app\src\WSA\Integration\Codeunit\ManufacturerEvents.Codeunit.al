namespace WSA.Integration;

using Microsoft.Inventory.Item.Catalog;

codeunit 50104 "Manufacturer Events"
{
    [EventSubscriber(ObjectType::Table, Database::Manufacturer, OnAfterInsertEvent, '', true, true)]
    local procedure SendEventOnAfterInsertEvent(
        var Rec: Record Manufacturer;
        RunTrigger: Boolean)

    begin
        if Rec.IsTemporary then
            exit;

        if RunTrigger then
            SendManufacturerToIntegrationAPI(Rec);
    end;


    [EventSubscriber(ObjectType::Table, Database::Manufacturer, OnAfterModifyEvent, '', true, true)]
    local procedure SendEventOnAfterModifyEvent(
        var Rec: Record Manufacturer;
        var xRec: Record Manufacturer;
        RunTrigger: Boolean)

    begin
        if Rec.IsTemporary then
            exit;

        if RunTrigger then
            SendManufacturerToIntegrationAPI(Rec);
    end;


    local procedure SendManufacturerToIntegrationAPI(Manufacturer: Record Manufacturer)
    var
        RetailIntegrationSetup: Record "Retail Integration Setup";
        IntegrationManagement: Codeunit "Integration Management";
        JObject: JsonObject;

    begin
        RetailIntegrationSetup.SafeGet();
        if not RetailIntegrationSetup.Enabled then
            exit;

        if Manufacturer.Code = '' then
            exit;

        if Manufacturer.Name = '' then
            exit;

        if IsNullGuid(Manufacturer.SystemId) then
            exit;

        JObject.Add('code', Manufacturer.Code);
        JObject.Add('externalCode', Format(Manufacturer.SystemId).TrimStart('{').TrimEnd('}'));
        JObject.Add('name', Manufacturer.Name);
        JObject.Add('address', Manufacturer.Address);
        JObject.Add('address2', Manufacturer."Address 2");
        JObject.Add('city', Manufacturer.City);
        JObject.Add('region', Manufacturer.County);
        JObject.Add('country', Manufacturer."Country/Region Code");
        JObject.Add('postalCode', Manufacturer."Post Code");
        JObject.Add('phone', Manufacturer."Phone No.");
        JObject.Add('email', Manufacturer."E-Mail");

        if not IntegrationManagement.TrySendToApi(JObject, 'manufacturers') then
            exit;
    end;
}