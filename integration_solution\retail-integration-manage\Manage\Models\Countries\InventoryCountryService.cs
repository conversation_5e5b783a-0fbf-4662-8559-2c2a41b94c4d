﻿using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using WSA.Retail.Integration.Configuration;
using WSA.Retail.Integration.Logging;
using WSA.Retail.Integration.Manage.Data.Repositories.Interfaces;

namespace WSA.Retail.Integration.Manage.Models.Countries;

public class InventoryCountryService(
    IOptions<AppSettings> options,
    ILogger<InventoryCountryService> logger,
    IInventoryCountryRepository inventoryCountryRepository)
    : IInventoryCountryService
{
    private readonly AppSettings _appSettings = options.Value;
    private readonly ILogger<InventoryCountryService> _logger = logger;
    private readonly IInventoryCountryRepository _inventoryCountryRepository = inventoryCountryRepository ??
        throw new ArgumentNullException(nameof(inventoryCountryRepository));

    public async Task<InventoryCountry> GetCountryByNameAsync(string name)
    {
        _logger.LogMethodStart(_appSettings.AppName);

        if (string.IsNullOrWhiteSpace(name))
            throw new ArgumentException("Name cannot be null or empty", nameof(name));

        var country = await _inventoryCountryRepository.GetByNameAsync(name);

        if (country == null)
            throw new KeyNotFoundException($"Country with name '{name}' not found");

        return country;
    }

    public async Task<InventoryCountry> GetCountryByCodeAsync(string code)
    {
        _logger.LogMethodStart(_appSettings.AppName);

        if (string.IsNullOrWhiteSpace(code))
            throw new ArgumentException("Code cannot be null or empty", nameof(code));

        var country = await _inventoryCountryRepository.GetByCodeAsync(code);

        if (country == null)
            throw new KeyNotFoundException($"Country with code '{code}' not found");

        return country;
    }
}