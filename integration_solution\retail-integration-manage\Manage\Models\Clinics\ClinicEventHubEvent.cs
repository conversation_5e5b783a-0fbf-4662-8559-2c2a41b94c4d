﻿namespace WSA.Retail.Integration.Manage.Models.Clinics;

public class ClinicEventHubEvent
{
    public Guid? ParentLocationID { get; set; }
    public string? ParentLocationCode { get; set; }
    public Guid? LocationID { get; set; }
    public string? LocationCode { get; set; } = string.Empty;
    public string? AltLocationCode { get; set; }
    public string? Name { get; set; } = string.Empty;
    public string? FullName { get; set; } = string.Empty;
    public bool IsActive { get; set; }
    public string? PhoneNumber { get; set; }
    public string? Email { get; set; }
    public string? FaxNumber { get; set; }
    public string? Address1 { get; set; }
    public string? Address2 { get; set; }
    public string? Address3 { get; set; }
    public string? City { get; set; }
    public string? Country { get; set; }
    public string? ZipCode { get; set; }
    public bool? NhsEnabled { get; set; }
    public Guid? TimeZoneId { get; set; }
    public string? TimeZoneCode { get; set; } = string.Empty;
    public string? HspSiteID { get; set; } = string.Empty;
    public bool? IsValidatedByAvalara { get; set; }
    public string? AvalaraCompanyCode { get; set; }
}