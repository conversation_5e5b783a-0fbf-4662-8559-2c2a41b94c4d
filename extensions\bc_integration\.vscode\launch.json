{"version": "0.2.0", "configurations": [{"name": "Your own server", "request": "launch", "type": "al", "environmentType": "OnPrem", "server": "http://bcserver", "serverInstance": "BC", "authentication": "UserPassword", "startupObjectId": 22, "startupObjectType": "Page", "breakOnError": "All", "launchBrowser": true, "enableLongRunningSqlStatements": true, "enableSqlInformationDebugger": true, "tenant": "default", "usePublicURLFromServer": true}, {"name": "FR_UAT", "request": "launch", "type": "al", "tenant": "1a41b96d-457d-41ac-94ef-22d1901a7556", "environmentType": "Sandbox", "environmentName": "fr_uat", "breakOnError": "ExcludeTry", "launchBrowser": true, "enableLongRunningSqlStatements": true, "enableSqlInformationDebugger": true, "schemaUpdateMode": "ForceSync"}]}