﻿DECLARE @categories TABLE (
        Code NVARCHAR(20),
        Name NVARCHAR(100),
        ParentCode NVARCHAR(20))

INSERT INTO @categories (Code, Name, ParentCode) VALUES
('1000', 'WSA products', NULL),
('1100', 'WSA hearing aids', '1000'),
('1110', 'WSA BTE hearing aids', '1100'),
('1130', 'WSA INS heaing aids', '1100'),
('1140', 'WSA ITE hearing aids', '1100'),
('1160', 'WSA RIC hearing aids', '1100'),
('1190', 'WSA other hearing aids', '1100'),
('1200', 'WSA serialized products', '1000'),
('1210', 'WSA earmolds', '1200'),
('1220', 'WSA chargers', '1200'),
('1290', 'WSA other serialized products', '1200'),
('1300', 'WSA non-serialized products', '1000'),
('1310', 'WSA receivers', '1300'),
('1390', 'WSA other non-serialized products', '1300'),
('1400', 'WSA consumables', '1000'),
('1410', 'WSA Batteries', '1400'),
('1490', 'WSA other consumables', '1400'),
('2000', 'WSA services', NULL),
('2100', 'WSA repairs', '2000'),
('2200', 'WSA warranties', '2000'),
('2800', 'WSA other services and fees', '2000'),
('6000', 'Third party products', NULL),
('6100', 'Third party hearing aids', '6000'),
('6110', 'Third party BTE hearing aids', '6100'),
('6130', 'Third party INS hearing aids', '6100'),
('6140', 'Third party ITE hearing aids', '6100'),
('6160', 'Third party RIC hearing aids', '6100'),
('6190', 'Third party other hearing aids', '6100'),
('6200', 'Third party serialized products', '6000'),
('6210', 'Third party earmolds', '6200'),
('6220', 'Third party chargers', '6200'),
('6290', 'Third party other serialized products', '6200'),
('6300', 'Third party non-serialized accessories', '6000'),
('6310', 'Third party receivers', '6300'),
('6390', 'Third party other non-serialized products', '6300'),
('6400', 'Third party consumables', '6000'),
('6410', 'Third party batteries', '6400'),
('6490', 'Third party other consumables', '6400'),
('7000', 'Third party services', NULL),
('7100', 'Third party repairs', '7000'),
('7200', 'Third party warranties', '7000'),
('7800', 'Third party other services and fees', '7000'),
('9000', 'Other', NULL)


 MERGE dbo.ProductCategory AS Target
 USING @categories AS Source
    ON Source.Code = Target.Code

  WHEN NOT MATCHED BY Target THEN
INSERT (Code,
       [Name])

VALUES (Source.Code,
       Source.[Name])
  
;

UPDATE Target
   SET ParentId = (SELECT TOP 1 Id FROM dbo.ProductCategory AS c1 WHERE c1.Code = Source.ParentCode)
  FROM dbo.ProductCategory AS Target

 INNER JOIN @categories AS Source
    ON Source.Code = Target.Code