namespace WSA.Integration.API.V1;

using Microsoft.Integration.Graph;
using Microsoft.Inventory.Item;
using Microsoft.Inventory.Ledger;
using Microsoft.Purchases.Document;
using Microsoft.Purchases.Vendor;
using Microsoft.Finance.VAT.Setup;
using Microsoft.Inventory.Item.Catalog;
using Microsoft.Sales.Document;
using System.Utilities;
using WSA.Integration;


codeunit 50119 "WSA Product Handler" implements "WSA Integration Request"
{
    TableNo = "WSA Integration Request Log";

    trigger OnRun()
    begin
        Code(Rec);
    end;


    procedure HandleRequest(var Request: Record "WSA Integration Request Log")
    begin
        if not Codeunit.Run(Codeunit::"WSA Product Handler", Request) then begin
            Common.SetErrorResponse(Request, '');
        end;
    end;


    local procedure Code(var Request: Record "WSA Integration Request Log")
    var
        json: JsonObject;

    begin
        case Request.Method of
            Request.Method::post:
                HandlePost(Request);
            Request.Method::get:
                HandleGet(Request);
        end;
    end;


    local procedure HandlePost(var Request: Record "WSA Integration Request Log")
    var
        item: Record Item;

    begin
        if TryHandlePost(Request, item) then
            Common.SetCreatedResponse(Request, Common.ProductToJson(item))
        else
            Common.SetErrorResponse(Request, '');
    end;


    local procedure HandleGet(var Request: Record "WSA Integration Request Log")
    var
        item: Record Item;

    begin
        if not TryHandleGet(Request, item) then
            Common.SetErrorResponse(Request, '');
    end;


    [TryFunction]
    local procedure TryHandlePost(
        var Request: Record "WSA Integration Request Log";
        var Item: Record Item)

    var
        category: Record "Item Category";
        recRef: RecordRef;
        json: JsonObject;
        categoryCode: Code[20];

    begin
        Request.TestField("Request Content");
        json := Common.GetJsonFromBlob(Request);

        if not PreValidateRequest(json) then
            tempErrorMessage.ThrowError();

        tempErrorMessage.ClearLog();
        if not Item.Get(Common.GetJsonValue(json, '$.code').AsCode()) then begin
            Item.Init();
            Item."No." := Common.GetJsonValue(json, '$.code').AsCode();
            Item.Insert(false);
        end;

        if ItemHasBeenUsed(Item."No.") then
            Common.RegisterFieldSet(Database::Item, Item.FieldNo(Type), TempFieldSet);

        categoryCode := GetCategoryCodeFromRequest(json);
        if categoryCode <> '' then begin
            Item.Validate("Item Category Code", categoryCode);
            Common.RegisterFieldSet(Database::Item, Item.FieldNo("Item Category Code"), TempFieldSet);
        end;

        recRef.GetTable(Item);
        Common.ValidateFieldFromJson(json, '$.name', recRef, Item.FieldNo(Description), TempFieldSet);
        Common.ValidateFieldFromJson(json, '$.pimProductId', recRef, Item.FieldNo("WSA PIM Product No."), TempFieldSet);
        Common.ValidateFieldFromJson(json, '$.vendor.code', recRef, Item.FieldNo("Vendor No."), TempFieldSet);
        Common.ValidateFieldFromJson(json, '$.manufacturer.code', recRef, Item.FieldNo("Manufacturer Code"), TempFieldSet);
        Common.ValidateFieldFromJson(json, '$.color.code', recRef, Item.FieldNo("WSA Color"), TempFieldSet);
        Common.ValidateFieldFromJson(json, '$.taxGroup.code', recRef, Item.FieldNo("VAT Prod. Posting Group"), TempFieldSet);
        Common.ValidateFieldFromJson(json, '$.vendorItemNo', recRef, Item.FieldNo("Vendor Item No."), TempFieldSet);
        Common.ValidateFieldFromJson(json, '$.gtin', recRef, Item.FieldNo(GTIN), TempFieldSet);
        Common.ValidateFieldFromJson(json, '$.imageUrl', recRef, Item.FieldNo("WSA External No."), TempFieldSet);

        GraphMgtGeneralTools.ProcessNewRecordFromAPI(recRef, TempFieldSet, CurrentDateTime());

        recRef.SetTable(Item);
        Item.Modify();

        Request."Product No." := Item."No.";
        Request.Modify();
    end;


    [TryFunction]
    local procedure TryHandleGet(
        var Request: Record "WSA Integration Request Log";
        var Item: Record Item)

    var
        recRef: RecordRef;
        json: JsonObject;

    begin
        Request.TestField("Request Content");
        json := Common.GetJsonFromBlob(Request);

        if not (Common.GetJsonValue(json, '$.id').IsNull) then begin
            if Item.GetBySystemId(Common.GetJsonValue(json, '$.id').AsText()) then begin
                Request."Product No." := Item."No.";
                Request.Modify();
                Common.SetOkResponse(Request, Common.ProductToJson(Item));
                exit;
            end;
        end;

        if not (Common.GetJsonValue(json, '$.code').IsNull) then begin
            Item.SetRange("No.", Common.GetJsonValue(json, '$.code').AsCode());
            if Item.FindFirst() then begin
                Request."Product No." := Item."No.";
                Request.Modify();
                Common.SetOkResponse(Request, Common.ProductToJson(Item));
                exit;
            end
        end;

        if not (Common.GetJsonValue(json, '$.name').IsNull) then begin
            Item.SetRange(Description, Common.GetJsonValue(json, '$.name').AsCode());
            if Item.FindFirst() then begin
                Request."Product No." := Item."No.";
                Request.Modify();
                Common.SetOkResponse(Request, Common.ProductToJson(Item));
                exit;
            end
        end;

        Common.SetNoContentResponse(Request);
    end;


    local procedure PreValidateRequest(json: JsonObject): Boolean
    var
        ItemCategory: Record "Item Category";
        Vendor: Record Vendor;
        Manufacturer: Record Manufacturer;
        Color: Record Color;
        VATProdPostingGroup: Record "VAT Product Posting Group";
        CategoryCode: Code[20];
        CodeValue: JsonValue;
        NameValue: JsonValue;
        VendorValue: JsonValue;
        ManufacturerValue: JsonValue;
        ColorValue: JsonValue;
        TaxGroupValue: JsonValue;

    begin
        TempErrorMessage.ClearLog();

        CodeValue := Common.GetJsonValue(json, '$.code');
        if (CodeValue.IsNull()) or (CodeValue.AsCode() = '') then
            tempErrorMessage.LogSimpleMessage(tempErrorMessage."Message Type"::Error, 'Code is required');

        NameValue := Common.GetJsonValue(json, '$.name');
        if (NameValue.IsNull()) or (NameValue.AsText() = '') then
            tempErrorMessage.LogSimpleMessage(tempErrorMessage."Message Type"::Error, 'Name is required');

        CategoryCode := GetCategoryCodeFromRequest(json);
        if (CategoryCode <> '') and not (ItemCategory.Get(CategoryCode)) then
            tempErrorMessage.LogSimpleMessage(tempErrorMessage."Message Type"::Error,
                StrSubstNo('%1=%2 cannot be found in table %3', ItemCategory.FieldCaption("Code"), CategoryCode, ItemCategory.TableCaption));

        VendorValue := Common.GetJsonValue(json, '$.vendor.code');
        if not (VendorValue.IsNull()) then
            if not (Vendor.Get(VendorValue.AsCode())) then
                tempErrorMessage.LogSimpleMessage(tempErrorMessage."Message Type"::Error,
                    StrSubstNo('%1=%2 cannot be found in table %3', Vendor.FieldCaption("No."), VendorValue.AsCode(), Vendor.TableCaption));

        ManufacturerValue := Common.GetJsonValue(json, '$.manufacturer.code');
        if not ManufacturerValue.IsNull() then
            if not (Manufacturer.Get(ManufacturerValue.AsCode())) then
                tempErrorMessage.LogSimpleMessage(tempErrorMessage."Message Type"::Error,
                    StrSubstNo('%1=%2 cannot be found in table %3', Manufacturer.FieldCaption(Code), ManufacturerValue.AsCode(), Manufacturer.TableCaption));

        ColorValue := Common.GetJsonValue(json, '$.color.code');
        if not (ColorValue.IsNull()) then
            if not (Color.Get(ColorValue.AsCode())) then
                tempErrorMessage.LogSimpleMessage(tempErrorMessage."Message Type"::Error,
                    StrSubstNo('%1=%2 cannot be found in table %3', Color.FieldCaption(Code), ColorValue.AsCode(), Color.TableCaption));

        TaxGroupValue := Common.GetJsonValue(json, '$.taxGroup.code');
        if not (TaxGroupValue.IsNull()) then
            if not (VATProdPostingGroup.Get(TaxGroupValue.AsCode())) then
                tempErrorMessage.LogSimpleMessage(tempErrorMessage."Message Type"::Error,
                    StrSubstNo('%1=%2 cannot be found in table %3', VATProdPostingGroup.FieldCaption(Code), TaxGroupValue.AsCode(), VATProdPostingGroup.TableCaption));

        if (tempErrorMessage.HasErrors(false)) then begin
            Session.LogMessage('b104b612-c11f-4b06-954c-86dc1279785b', tempErrorMessage.ToString(), Verbosity::Error, DataClassification::SystemMetadata, TelemetryScope::ExtensionPublisher, '', '');
            exit(false);
        end;

        exit(true);
    end;

    local procedure GetCategoryCodeFromRequest(json: JsonObject): Code[20]
    var
        categoryValue: JsonValue;
        subcategoryValue: JsonValue;

    begin
        categoryValue := Common.GetJsonValue(json, '$.category.code');
        subcategoryValue := Common.GetJsonValue(json, '$.subcategory.code');

        if (subcategoryValue.IsNull) and not (categoryValue.IsNull) then
            exit(categoryValue.AsCode());

        if not (subcategoryValue.IsNull) then
            exit(subcategoryValue.AsCode());

        exit('');
    end;

    local procedure ItemHasBeenUsed(ItemNo: Code[20]): Boolean
    var
        ItemLedgerEntry: Record "Item Ledger Entry";
        SalesLine: Record "Sales Line";
        PurchaseLine: Record "Purchase Line";

    begin
        ItemLedgerEntry.SetRange("Item No.", ItemNo);
        if not (ItemLedgerEntry.IsEmpty()) then
            exit(true);

        SalesLine.SetRange(Type, SalesLine.Type::Item);
        SalesLine.SetRange("No.", ItemNo);
        if not (SalesLine.IsEmpty()) then
            exit(true);

        PurchaseLine.SetRange(Type, purchaseLine.Type::Item);
        PurchaseLine.SetRange("No.", ItemNo);
        if not PurchaseLine.IsEmpty() then
            exit(true);

        exit(false);
    end;


    var
        tempFieldSet: Record 2000000041 temporary;
        tempErrorMessage: Record "Error Message" temporary;
        GraphMgtGeneralTools: Codeunit "Graph Mgt - General Tools";
        Common: Codeunit "WSA Common";
}
