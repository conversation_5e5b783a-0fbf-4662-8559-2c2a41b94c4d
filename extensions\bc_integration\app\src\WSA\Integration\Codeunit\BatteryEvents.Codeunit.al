namespace WSA.Integration;
using Microsoft.Inventory.Item;

codeunit 50131 "Battery Events"
{
    TableNo = "Battery";

    trigger OnRun()
    begin
        RaiseEvent(Rec);
    end;


    [EventSubscriber(ObjectType::Table, Database::"Battery", OnAfterInsertEvent, '', true, true)]
    local procedure SendEventOnAfterInsertEvent(
        var Rec: Record "Battery";
        RunTrigger: Boolean)

    var
        RetailIntegrationSetup: Record "Retail Integration Setup";

    begin
        RetailIntegrationSetup.SafeGet();
        if not RetailIntegrationSetup.Enabled then
            exit;

        if Rec.IsTemporary then
            exit;

        if RunTrigger then begin
            if RetailIntegrationSetup."Handle Events Asyncronously" then
                RaiseEventAsync(Rec)
            else
                RaiseEvent(Rec);
        end;
    end;


    [EventSubscriber(ObjectType::Table, Database::"Battery", OnAfterModifyEvent, '', true, true)]
    local procedure SendEventOnAfterModifyEvent(
        var Rec: Record "Battery";
        var xRec: Record "Battery";
        RunTrigger: Boolean)

    var
        RetailIntegrationSetup: Record "Retail Integration Setup";

    begin
        RetailIntegrationSetup.SafeGet();
        if not RetailIntegrationSetup.Enabled then
            exit;

        if Rec.IsTemporary then
            exit;

        if RunTrigger then begin
            if RetailIntegrationSetup."Handle Events Asyncronously" then
                RaiseEventAsync(Rec)
            else
                RaiseEvent(Rec);
        end;
    end;

    local procedure RaiseEventAsync(Battery: Record Battery)
    var
        SessionId: Integer;

    begin
        Session.StartSession(SessionId, Codeunit::"Battery Events", CompanyName, Battery);
    end;

    local procedure RaiseEvent(Battery: Record Battery)
    var
        RetailIntegrationSetup: Record "Retail Integration Setup";
        IntegrationManagement: Codeunit "Integration Management";
        Common: Codeunit "WSA Common";
        JObject: JsonObject;

    begin
        RetailIntegrationSetup.SafeGet();
        if not RetailIntegrationSetup.Enabled then
            exit;

        if Battery."Code" = '' then
            exit;

        if Battery.Name = '' then
            exit;

        if IsNullGuid(Battery.SystemId) then
            exit;

        JObject := Common.BatteryToJson(Battery);

        if not IntegrationManagement.TryRaiseEvent(JObject, 'batteries', Format(Battery.SystemId).TrimStart('{').TrimEnd('}')) then
            exit;
    end;
}