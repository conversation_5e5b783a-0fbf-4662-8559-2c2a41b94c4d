﻿CREATE VIEW [dbo].[Payors]
AS 

SELECT Source.Id,
       Source.Code,
       (SELECT TOP 1 ExternalRecordId
          FROM dbo.Coupling
         WHERE Coupling.RecordId = Source.Id AND Coupling.ExternalSystemId = ExternalSystem.Id) AS ExternalCode,
       Source.[Name],
       ExternalSystem.Id AS ExternalSystemId,
       ExternalSystem.Code AS ExternalSystemCode,
       Source.[ModifiedOn],
       t1.JSON

  FROM dbo.Payor AS Source

 OUTER APPLY dbo.ExternalSystem

 OUTER APPLY (SELECT(
              SELECT Payor.Id AS 'id',
                     ExternalSystem.Code AS 'externalSystemCode',
                     UPPER(Payor.Code) AS 'code',
                     (SELECT TOP 1 ExternalRecordId 
                        FROM dbo.Coupling 
                       WHERE Coupling.RecordId = Payor.Id AND Coupling.ExternalSystemId = ExternalSystem.Id) AS 'externalCode',
                     Payor.[Name] AS 'name',
                     Payor.[Address] AS 'address',
                     Payor.Address2 AS 'address2',
                     Payor.City AS 'city',
                     Payor.Region AS 'region',
                     Payor.Country AS 'country',
                     Payor.PostalCode AS 'postalCode',
                     Payor.Phone AS 'phone',
                     Payor.Email AS 'email',
                     Payor.AccountNo AS 'accountNo'

                FROM dbo.Payor

                WHERE Payor.Id = Source.Id

                  FOR JSON PATH, WITHOUT_ARRAY_WRAPPER
                      ) AS JSON
                      ) AS t1