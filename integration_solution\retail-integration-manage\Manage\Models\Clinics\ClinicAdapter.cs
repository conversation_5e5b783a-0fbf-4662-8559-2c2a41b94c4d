﻿using Microsoft.Azure.Amqp.Framing;
using WSA.Retail.Integration.Manage.EventProcessing;
using WSA.Retail.Integration.Models.Clinics;

namespace WSA.Retail.Integration.Manage.Models.Clinics;

public class ClinicAdapter : IEventHubEntityAdapter<ClinicEventHubEvent, Clinic>
{
    public Clinic ToDomainModel(
        ClinicEventHubEvent source,
        string externalSystemCode)
    {
        if (string.IsNullOrWhiteSpace(source.LocationCode))
        {
            throw new ArgumentNullException(nameof(source),
                $"{source.GetType().Name} {nameof(source.LocationID)}: {source.LocationID} " +
                $"contains a null or empty {nameof(source.LocationCode)}.");
        }

        return new Clinic()
        {
            ExternalSystemCode = externalSystemCode,
            ExternalCode = source.LocationID.ToString(),
            Code = source.LocationCode,
            Name = source.Name,
            Address = source.Address1,
            Address2 = source.Address2,
            City = source.City,
            Country = source.Country,
            PostalCode = source.ZipCode,
            Phone = source.PhoneNumber
        };
    }
}