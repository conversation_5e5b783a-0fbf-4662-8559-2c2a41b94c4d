namespace WSA.Integration;

using Microsoft.Foundation.Address;
using Microsoft.Inventory.Item.Catalog;
using System.Email;


tableextension 50103 Manufacturer extends Manufacturer
{
    LookupPageId = 50177;

    fields
    {
        field(50100; "External Code"; Code[100])
        {
            Caption = 'External Code';
            DataClassification = SystemMetadata;
        }

        field(50101; Address; Text[100])
        {
            Caption = 'Address';
        }

        field(50102; "Address 2"; Text[50])
        {
            Caption = 'Address 2';
        }

        field(50103; City; Text[30])
        {
            Caption = 'City';
            // TableRelation = if ("Country/Region Code" = const('')) "Post Code".City
            // else
            // if ("Country/Region Code" = filter(<> '')) "Post Code".City where("Country/Region Code" = field("Country/Region Code"));
            // ValidateTableRelation = false;

            trigger OnLookup()
            var
                thisCity: Text;
                thisCounty: Text;

            begin
                // thisCity := Rec.City;
                // thisCity := Rec.County;
                // PostCode.LookupPostCode(thisCity, Rec."Post Code", thisCounty, Rec."Country/Region Code");
                // Rec.City := CopyStr(thisCity, 1, MaxStrLen(Rec.City));
                // Rec.County := CopyStr(thisCounty, 1, MaxStrLen(Rec.County));
            end;

            trigger OnValidate()
            begin
                //PostCode.ValidateCity(Rec.City, Rec."Post Code", Rec.County, Rec."Country/Region Code", (CurrFieldNo <> 0) and GuiAllowed);
            end;
        }

        field(50104; "Post Code"; Code[20])
        {
            Caption = 'Post Code';
            // TableRelation = if ("Country/Region Code" = const('')) "Post Code"
            // else
            // if ("Country/Region Code" = filter(<> '')) "Post Code" where("Country/Region Code" = field("Country/Region Code"));
            // ValidateTableRelation = false;

            trigger OnLookup()
            var
                thisCity: Text;
                thisCounty: Text;

            begin
                // thisCity := Rec.City;
                // thisCity := Rec.County;
                // PostCode.LookupPostCode(thisCity, Rec."Post Code", thisCounty, Rec."Country/Region Code");
                // Rec.City := CopyStr(thisCity, 1, MaxStrLen(Rec.City));
                // Rec.County := CopyStr(thisCounty, 1, MaxStrLen(Rec.County));
            end;

            trigger OnValidate()
            begin
                // PostCode.ValidateCity(Rec.City, Rec."Post Code", Rec.County, Rec."Country/Region Code", (CurrFieldNo <> 0) and GuiAllowed);
            end;
        }

        field(50105; County; Text[30])
        {
            CaptionClass = '5,1,' + "Country/Region Code";
            Caption = 'County';
        }

        field(50106; "Country/Region Code"; Code[10])
        {
            Caption = 'Country/Region Code';
            TableRelation = "Country/Region";

            trigger OnValidate()
            var
                thisCity: Text;
                thisCounty: Text;

            begin
                // thisCity := Rec.City;
                // thisCity := Rec.County;
                // PostCode.CheckClearPostCodeCityCounty(thisCity, Rec."Post Code", thisCounty,
                //     Rec."Country/Region Code", xRec."Country/Region Code");
                // Rec.City := CopyStr(thisCity, 1, MaxStrLen(Rec.City));
                // Rec.County := CopyStr(thisCounty, 1, MaxStrLen(Rec.County));
            end;
        }

        field(50109; "Phone No."; Text[30])
        {
            Caption = 'Phone No.';
            ExtendedDatatype = PhoneNo;
        }

        field(50108; "E-Mail"; Text[80])
        {
            Caption = 'Email';
            ExtendedDatatype = EMail;

            trigger OnValidate()
            var
                MailManagement: Codeunit "Mail Management";
                thisEmail: Text;

            begin
                thisEmail := Rec."E-Mail";
                MailManagement.ValidateEmailAddressField(thisEmail);
                Rec."E-Mail" := CopyStr(thisEmail, 1, MaxStrLen(Rec."E-Mail"));
            end;
        }
    }

    keys
    {
        key(ExternalCode; "External Code")
        {
        }
    }


    var
        PostCode: Record "Post Code";
}
