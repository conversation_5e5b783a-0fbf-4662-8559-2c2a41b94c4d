﻿<?xml version="1.0" encoding="utf-8"?>
<xliff version="1.2" xmlns="urn:oasis:names:tc:xliff:document:1.2" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="urn:oasis:names:tc:xliff:document:1.2 xliff-core-1.2-transitional.xsd">
  <file datatype="xml" source-language="en-US" target-language="en-US" original="Retail Integration Connector">
    <body>
      <group id="body">
        <trans-unit id="Page 1444768839 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>apiV1Clinic</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page API V1 Clinic - Property Caption</note>
        </trans-unit>
        <trans-unit id="Page 805191900 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Retail Integration Request</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page API V1 Integration Request - Property Caption</note>
        </trans-unit>
        <trans-unit id="Page 805191900 - Property 3446740159" size-unit="char" translate="yes" xml:space="preserve">
          <source>Request</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page API V1 Integration Request - Property EntityCaption</note>
        </trans-unit>
        <trans-unit id="Page 805191900 - Property 631549417" size-unit="char" translate="yes" xml:space="preserve">
          <source>Requests</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page API V1 Integration Request - Property EntitySetCaption</note>
        </trans-unit>
        <trans-unit id="Page 805191900 - Control 2683860767 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Entry No.</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page API V1 Integration Request - Control entryNo - Property Caption</note>
        </trans-unit>
        <trans-unit id="Page 805191900 - Control 2686962215 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Id</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page API V1 Integration Request - Control id - Property Caption</note>
        </trans-unit>
        <trans-unit id="Page 805191900 - Control 3743604651 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>request</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page API V1 Integration Request - Control requestBody - Property Caption</note>
        </trans-unit>
        <trans-unit id="Page 805191900 - Control 1009662778 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Request Method</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page API V1 Integration Request - Control requestMethod - Property Caption</note>
        </trans-unit>
        <trans-unit id="Page 805191900 - Control 2372889619 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Request Type</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page API V1 Integration Request - Control requestType - Property Caption</note>
        </trans-unit>
        <trans-unit id="Page 805191900 - Control 2862218949 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>response</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page API V1 Integration Request - Control responseBody - Property Caption</note>
        </trans-unit>
        <trans-unit id="Page 805191900 - Control 998393528 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Status</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page API V1 Integration Request - Control status - Property Caption</note>
        </trans-unit>
        <trans-unit id="Table 1784400834 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Retail Integration Setup</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Table Retail Integration Setup - Property Caption</note>
        </trans-unit>
        <trans-unit id="Table 1784400834 - Field 869913466 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>API Base URL</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Table Retail Integration Setup - Field API Base URL - Property Caption</note>
        </trans-unit>
        <trans-unit id="Table 1784400834 - Field 4253087169 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>API Key</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Table Retail Integration Setup - Field API Key - Property Caption</note>
        </trans-unit>
        <trans-unit id="Table 1784400834 - Field 325736873 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Claim Journal Batch</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Table Retail Integration Setup - Field Claim Journal Batch - Property Caption</note>
        </trans-unit>
        <trans-unit id="Table 1784400834 - Field 3786929933 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Claim Journal Template</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Table Retail Integration Setup - Field Claim Journal Template - Property Caption</note>
        </trans-unit>
        <trans-unit id="Table 1784400834 - Field 2891538263 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Enabled</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Table Retail Integration Setup - Field Enabled - Property Caption</note>
        </trans-unit>
        <trans-unit id="Table 1784400834 - Field 4225666337 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Event Grid Access Key</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Table Retail Integration Setup - Field Event Grid Access Key - Property Caption</note>
        </trans-unit>
        <trans-unit id="Table 1784400834 - Field 1341209202 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Event Grid Topic Endpoint</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Table Retail Integration Setup - Field Event Grid Topic Endpoint - Property Caption</note>
        </trans-unit>
        <trans-unit id="Table 1784400834 - Field 1466987183 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>External System Code</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Table Retail Integration Setup - Field External System Code - Property Caption</note>
        </trans-unit>
        <trans-unit id="Table 1784400834 - Field 2613651216 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Handle Events Asyncronously</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Table Retail Integration Setup - Field Handle Events Asyncronously - Property Caption</note>
        </trans-unit>
        <trans-unit id="Table 1784400834 - Field 513753450 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Item Adjustment Batch</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Table Retail Integration Setup - Field Item Adjustment Batch - Property Caption</note>
        </trans-unit>
        <trans-unit id="Table 1784400834 - Field 3803952390 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Item Adjustment Template</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Table Retail Integration Setup - Field Item Adjustment Template - Property Caption</note>
        </trans-unit>
        <trans-unit id="Table 1784400834 - Field 2203546865 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Payment Journal Batch</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Table Retail Integration Setup - Field Payment Journal Batch - Property Caption</note>
        </trans-unit>
        <trans-unit id="Table 1784400834 - Field 1308935589 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Payment Journal Template</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Table Retail Integration Setup - Field Payment Journal Template - Property Caption</note>
        </trans-unit>
        <trans-unit id="Table 1784400834 - Field 1663593181 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Primary Key</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Table Retail Integration Setup - Field Primary Key - Property Caption</note>
        </trans-unit>
        <trans-unit id="Table 630922058 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>WSA Integration Request Log</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Table WSA Integration Request Log - Property Caption</note>
        </trans-unit>
        <trans-unit id="Table 630922058 - Method 3333449226 - NamedType 3606899323" size-unit="char" translate="yes" xml:space="preserve">
          <source>Requests to process:  #1####\\Current Request:  #2########\</source>
          <note from="Developer" annotates="general" priority="2">#1#### = Request Count, #2######## = Current Request No.</note>
          <note from="Xliff Generator" annotates="general" priority="3">Table WSA Integration Request Log - Method ProcessAllHeaders - NamedType DialogMsg</note>
        </trans-unit>
        <trans-unit id="Table 630922058 - Method 3333449226 - NamedType 3950098385" size-unit="char" translate="yes" xml:space="preserve">
          <source>%1 %2 (%3%)</source>
          <note from="Developer" annotates="general" priority="2">%1 = interface, %2 = key, %3 = progress</note>
          <note from="Xliff Generator" annotates="general" priority="3">Table WSA Integration Request Log - Method ProcessAllHeaders - NamedType ProgressLbl</note>
        </trans-unit>
        <trans-unit id="Table 630922058 - Field 1872295196 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Battery Code</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Table WSA Integration Request Log - Field Battery Code - Property Caption</note>
        </trans-unit>
        <trans-unit id="Table 630922058 - Field 2682141055 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Claim No.</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Table WSA Integration Request Log - Field Claim No. - Property Caption</note>
        </trans-unit>
        <trans-unit id="Table 630922058 - Field 3064813023 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Clinic No.</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Table WSA Integration Request Log - Field Clinic No. - Property Caption</note>
        </trans-unit>
        <trans-unit id="Table 630922058 - Field 2275574084 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Color Code</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Table WSA Integration Request Log - Field Color Code - Property Caption</note>
        </trans-unit>
        <trans-unit id="Table 630922058 - Field 580412389 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Document Date.</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Table WSA Integration Request Log - Field Document Date - Property Caption</note>
        </trans-unit>
        <trans-unit id="Table 630922058 - Field 486908828 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Document No.</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Table WSA Integration Request Log - Field Document No. - Property Caption</note>
        </trans-unit>
        <trans-unit id="Table 630922058 - Field 883711285 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Entry No.</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Table WSA Integration Request Log - Field Entry No. - Property Caption</note>
        </trans-unit>
        <trans-unit id="Table 630922058 - Field 2585418950 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Manufacturer Code</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Table WSA Integration Request Log - Field Manufacturer Code - Property Caption</note>
        </trans-unit>
        <trans-unit id="Table 630922058 - Field 4132733409 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Method</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Table WSA Integration Request Log - Field Method - Property Caption</note>
        </trans-unit>
        <trans-unit id="Table 630922058 - Field 222620933 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Notes</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Table WSA Integration Request Log - Field Notes - Property Caption</note>
        </trans-unit>
        <trans-unit id="Table 630922058 - Field ********** - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Patient No.</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Table WSA Integration Request Log - Field Patient No. - Property Caption</note>
        </trans-unit>
        <trans-unit id="Table 630922058 - Field ********** - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Payor No.</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Table WSA Integration Request Log - Field Payor No. - Property Caption</note>
        </trans-unit>
        <trans-unit id="Table 630922058 - Field ********** - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Product Model Code</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Table WSA Integration Request Log - Field Product Model Code - Property Caption</note>
        </trans-unit>
        <trans-unit id="Table 630922058 - Field ********** - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Product No.</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Table WSA Integration Request Log - Field Product No. - Property Caption</note>
        </trans-unit>
        <trans-unit id="Table 630922058 - Field 2877945414 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Purchase Order No.</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Table WSA Integration Request Log - Field Purchase Order No. - Property Caption</note>
        </trans-unit>
        <trans-unit id="Table 630922058 - Field 2532908604 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Purchase Receipt No.</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Table WSA Integration Request Log - Field Purchase Receipt No. - Property Caption</note>
        </trans-unit>
        <trans-unit id="Table 630922058 - Field 342365012 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Request</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Table WSA Integration Request Log - Field Request Content - Property Caption</note>
        </trans-unit>
        <trans-unit id="Table 630922058 - Field 3004909150 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Response</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Table WSA Integration Request Log - Field Response Content - Property Caption</note>
        </trans-unit>
        <trans-unit id="Table 630922058 - Field 3753913412 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Sales Credit Memo No.</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Table WSA Integration Request Log - Field Sales Credit Memo No. - Property Caption</note>
        </trans-unit>
        <trans-unit id="Table 630922058 - Field 33726994 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Sales Invoice No.</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Table WSA Integration Request Log - Field Sales Invoice No. - Property Caption</note>
        </trans-unit>
        <trans-unit id="Table 630922058 - Field 1475490283 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Sales Order No.</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Table WSA Integration Request Log - Field Sales Order No. - Property Caption</note>
        </trans-unit>
        <trans-unit id="Table 630922058 - Field 513339096 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Status</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Table WSA Integration Request Log - Field Status - Property Caption</note>
        </trans-unit>
        <trans-unit id="Table 630922058 - Field 1878130204 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Type</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Table WSA Integration Request Log - Field Type - Property Caption</note>
        </trans-unit>
        <trans-unit id="Table 630922058 - Field 1527594213 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Vendor No.</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Table WSA Integration Request Log - Field Vendor No. - Property Caption</note>
        </trans-unit>
        <trans-unit id="Codeunit 3533182055 - Method 1148061732 - NamedType 3231710450" size-unit="char" translate="yes" xml:space="preserve">
          <source>https://api.businesscentral.dynamics.com/v2.0/%1/%2/api</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Codeunit Integration Management - Method GetBaseUrl - NamedType BaseUrlTxt</note>
        </trans-unit>
        <trans-unit id="Codeunit 3533182055 - Method 3379293773 - NamedType 3231710450" size-unit="char" translate="yes" xml:space="preserve">
          <source>https://businesscentral.dynamics.com/%1/companies(%2)</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Codeunit Integration Management - Method GetExternalSystemCode - NamedType BaseUrlTxt</note>
        </trans-unit>
        <trans-unit id="Page 1784400834 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Retail Integration Setup</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page Retail Integration Setup - Property Caption</note>
        </trans-unit>
        <trans-unit id="Page 1784400834 - Control 2445482498 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>General</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page Retail Integration Setup - Control General - Property Caption</note>
        </trans-unit>
        <trans-unit id="Page 1784400834 - Control 787935911 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>API Settings</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page Retail Integration Setup - Control API Settings - Property Caption</note>
        </trans-unit>
        <trans-unit id="Page 1784400834 - Control 869913466 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>The base URL of the API</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page Retail Integration Setup - Control API Base URL - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="Page 1784400834 - Control 4253087169 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>The API key to authenticate with the API</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page Retail Integration Setup - Control API Key - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="Page 1784400834 - Control 3979251883 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Event Grid Settings</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page Retail Integration Setup - Control Event Grid Settings - Property Caption</note>
        </trans-unit>
        <trans-unit id="Page 1784400834 - Control 4225666337 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>The key of the Event Grid topic</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page Retail Integration Setup - Control Event Grid Access Key - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="Page 1784400834 - Control 1341209202 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>The endpoint of the Event Grid topic</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page Retail Integration Setup - Control Event Grid Topic Endpoint - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="Page 1784400834 - Control 2613651216 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Handle events asyncronously</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page Retail Integration Setup - Control Handle Events Asyncronously - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="Page 1784400834 - Control 1466987183 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>The code of the external system</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page Retail Integration Setup - Control External System Code - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="Page 1784400834 - Control 2891538263 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Enable or disable the integration with the API</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page Retail Integration Setup - Control Enabled - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="Page 1784400834 - Control 1074715950 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Journals</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page Retail Integration Setup - Control Journals - Property Caption</note>
        </trans-unit>
        <trans-unit id="Page 1784400834 - Control 1049914261 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Claim Journal</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page Retail Integration Setup - Control Claim Journal - Property Caption</note>
        </trans-unit>
        <trans-unit id="Page 1784400834 - Control 1550804266 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>The journal batch to use for claim journals</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page Retail Integration Setup - Control ClaimBatch - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="Page 1784400834 - Control 2701684550 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>The journal template to use for claim journals</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page Retail Integration Setup - Control ClaimTemplate - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="Page 1784400834 - Control 3009232573 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Item Adjustment Journal</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page Retail Integration Setup - Control Item Adjustment Journal - Property Caption</note>
        </trans-unit>
        <trans-unit id="Page 1784400834 - Control 1513328805 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>The journal batch to use for item adjustment journals</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page Retail Integration Setup - Control AdjustmentBatch - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="Page 1784400834 - Control 3091584881 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>The journal template to use for item adjustment journals</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page Retail Integration Setup - Control AdjustmentTemplate - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="Page 1784400834 - Control 3196174509 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Payment Journal</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page Retail Integration Setup - Control Payment Journal - Property Caption</note>
        </trans-unit>
        <trans-unit id="Page 1784400834 - Control 2866533842 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>The journal batch to use for payment journals</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page Retail Integration Setup - Control PaymentBatch - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="Page 1784400834 - Control 2639201662 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>The journal template to use for payment journals</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page Retail Integration Setup - Control PaymentTemplate - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="Page 630922058 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Integration Requests</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page WSA Integration Request Log - Property Caption</note>
        </trans-unit>
        <trans-unit id="Page 630922058 - Control 1872295196 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Specifies the value of the Battery Code field.</source>
          <note from="Developer" annotates="general" priority="2">%</note>
          <note from="Xliff Generator" annotates="general" priority="3">Page WSA Integration Request Log - Control Battery Code - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="Page 630922058 - Control 3064813023 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Specifies the value of the Clinic No. field.</source>
          <note from="Developer" annotates="general" priority="2">%</note>
          <note from="Xliff Generator" annotates="general" priority="3">Page WSA Integration Request Log - Control Clinic No. - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="Page 630922058 - Control 2275574084 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Specifies the value of the Color Code field.</source>
          <note from="Developer" annotates="general" priority="2">%</note>
          <note from="Xliff Generator" annotates="general" priority="3">Page WSA Integration Request Log - Control Color Code - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="Page 630922058 - Control 580412389 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Specifies the value of the Document Date field.</source>
          <note from="Developer" annotates="general" priority="2">%</note>
          <note from="Xliff Generator" annotates="general" priority="3">Page WSA Integration Request Log - Control Document Date - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="Page 630922058 - Control 486908828 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Specifies the value of the Document No. field.</source>
          <note from="Developer" annotates="general" priority="2">%</note>
          <note from="Xliff Generator" annotates="general" priority="3">Page WSA Integration Request Log - Control Document No. - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="Page 630922058 - Control 883711285 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Specifies the value of the Entry No. field.</source>
          <note from="Developer" annotates="general" priority="2">%</note>
          <note from="Xliff Generator" annotates="general" priority="3">Page WSA Integration Request Log - Control Entry No. - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="Page 630922058 - Control 2585418950 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Specifies the value of the Manufacturer Code field.</source>
          <note from="Developer" annotates="general" priority="2">%</note>
          <note from="Xliff Generator" annotates="general" priority="3">Page WSA Integration Request Log - Control Manufacturer Code - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="Page 630922058 - Control 222620933 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Specifies the value of the Notes field.</source>
          <note from="Developer" annotates="general" priority="2">%</note>
          <note from="Xliff Generator" annotates="general" priority="3">Page WSA Integration Request Log - Control Notes - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="Page 630922058 - Control ********** - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Specifies the value of the Patient No. field.</source>
          <note from="Developer" annotates="general" priority="2">%</note>
          <note from="Xliff Generator" annotates="general" priority="3">Page WSA Integration Request Log - Control Patient No. - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="Page 630922058 - Control ********** - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Specifies the value of the Payor No. field.</source>
          <note from="Developer" annotates="general" priority="2">%</note>
          <note from="Xliff Generator" annotates="general" priority="3">Page WSA Integration Request Log - Control Payor No. - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="Page 630922058 - Control ********** - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Specifies the value of the Product Model Code field.</source>
          <note from="Developer" annotates="general" priority="2">%</note>
          <note from="Xliff Generator" annotates="general" priority="3">Page WSA Integration Request Log - Control Product Model Code - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="Page 630922058 - Control ********** - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Specifies the value of the Product No. field.</source>
          <note from="Developer" annotates="general" priority="2">%</note>
          <note from="Xliff Generator" annotates="general" priority="3">Page WSA Integration Request Log - Control Product No. - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="Page 630922058 - Control 2877945414 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Specifies the value of the Purchase Order No. field.</source>
          <note from="Developer" annotates="general" priority="2">%</note>
          <note from="Xliff Generator" annotates="general" priority="3">Page WSA Integration Request Log - Control Purchase Order No. - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="Page 630922058 - Control 513339096 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Specifies the value of the Status field.</source>
          <note from="Developer" annotates="general" priority="2">%</note>
          <note from="Xliff Generator" annotates="general" priority="3">Page WSA Integration Request Log - Control Status - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="Page 630922058 - Control 577209412 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Specifies the value of the SystemCreatedAt field.</source>
          <note from="Developer" annotates="general" priority="2">%</note>
          <note from="Xliff Generator" annotates="general" priority="3">Page WSA Integration Request Log - Control SystemCreatedAt - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="Page 630922058 - Control 4000648066 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Specifies the value of the SystemCreatedBy field.</source>
          <note from="Developer" annotates="general" priority="2">%</note>
          <note from="Xliff Generator" annotates="general" priority="3">Page WSA Integration Request Log - Control SystemCreatedBy - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="Page 630922058 - Control 4000648066 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Created By</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page WSA Integration Request Log - Control SystemCreatedBy - Property Caption</note>
        </trans-unit>
        <trans-unit id="Page 630922058 - Control 2453272523 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Specifies the value of the SystemModifiedAt field.</source>
          <note from="Developer" annotates="general" priority="2">%</note>
          <note from="Xliff Generator" annotates="general" priority="3">Page WSA Integration Request Log - Control SystemModifiedAt - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="Page 630922058 - Control 4085837753 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Specifies the value of the SystemModifiedBy field.</source>
          <note from="Developer" annotates="general" priority="2">%</note>
          <note from="Xliff Generator" annotates="general" priority="3">Page WSA Integration Request Log - Control SystemModifiedBy - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="Page 630922058 - Control 4085837753 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Modified By</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page WSA Integration Request Log - Control SystemModifiedBy - Property Caption</note>
        </trans-unit>
        <trans-unit id="Page 630922058 - Control 1878130204 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Specifies the value of the Type field.</source>
          <note from="Developer" annotates="general" priority="2">%</note>
          <note from="Xliff Generator" annotates="general" priority="3">Page WSA Integration Request Log - Control Type - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="Page 630922058 - Control 1527594213 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Specifies the value of the Vendor No. field.</source>
          <note from="Developer" annotates="general" priority="2">%</note>
          <note from="Xliff Generator" annotates="general" priority="3">Page WSA Integration Request Log - Control Vendor No. - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="Page 630922058 - Action 3445273183 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Upload a new json file to the request content.</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page WSA Integration Request Log - Action AttachFile - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="Page 630922058 - Action 3445273183 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Upload Request Content</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page WSA Integration Request Log - Action AttachFile - Property Caption</note>
        </trans-unit>
        <trans-unit id="Page 630922058 - Action 1972153130 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>ToolTip: </source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page WSA Integration Request Log - Action processAllHeaders - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="Page 630922058 - Action 1972153130 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Process All Requests</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page WSA Integration Request Log - Action processAllHeaders - Property Caption</note>
        </trans-unit>
        <trans-unit id="Page 630922058 - Action 915599582 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Process Request</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page WSA Integration Request Log - Action processRequest - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="Page 630922058 - Action 915599582 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Process Request</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page WSA Integration Request Log - Action processRequest - Property Caption</note>
        </trans-unit>
        <trans-unit id="Page 630922058 - Action 2553816663 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Set Status Ready</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page WSA Integration Request Log - Action setStatusReady - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="Page 630922058 - Action 2553816663 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Set Status Ready</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page WSA Integration Request Log - Action setStatusReady - Property Caption</note>
        </trans-unit>
        <trans-unit id="Page 630922058 - View 1427226557 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Batteries</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page WSA Integration Request Log - View Batteries - Property Caption</note>
        </trans-unit>
        <trans-unit id="Page 630922058 - View 3405155355 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Clinics</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page WSA Integration Request Log - View Clinics - Property Caption</note>
        </trans-unit>
        <trans-unit id="Page 630922058 - View 3857907682 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Colors</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page WSA Integration Request Log - View Colors - Property Caption</note>
        </trans-unit>
        <trans-unit id="Page 630922058 - View 2011898816 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Manufacturers</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page WSA Integration Request Log - View Manufacturers - Property Caption</note>
        </trans-unit>
        <trans-unit id="Page 630922058 - View ********** - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Patients</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page WSA Integration Request Log - View Patients - Property Caption</note>
        </trans-unit>
        <trans-unit id="Page 630922058 - View ********** - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Payments</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page WSA Integration Request Log - View Payments - Property Caption</note>
        </trans-unit>
        <trans-unit id="Page 630922058 - View 874863434 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Payors</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page WSA Integration Request Log - View Payors - Property Caption</note>
        </trans-unit>
        <trans-unit id="Page 630922058 - View 564419761 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>ProductModels</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page WSA Integration Request Log - View ProductModels - Property Caption</note>
        </trans-unit>
        <trans-unit id="Page 630922058 - View 2362238540 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Products</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page WSA Integration Request Log - View Products - Property Caption</note>
        </trans-unit>
        <trans-unit id="Page 630922058 - View 2827062946 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Purchase Orders</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page WSA Integration Request Log - View PurchaseOrders - Property Caption</note>
        </trans-unit>
        <trans-unit id="Page 630922058 - View 1467451928 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Purchase Receipts</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page WSA Integration Request Log - View PurchaseReceipts - Property Caption</note>
        </trans-unit>
        <trans-unit id="Page 630922058 - View 2685059554 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Sales Credits</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page WSA Integration Request Log - View SalesCredits - Property Caption</note>
        </trans-unit>
        <trans-unit id="Page 630922058 - View 2728404382 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Sales Invoices</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page WSA Integration Request Log - View SalesInvoices - Property Caption</note>
        </trans-unit>
        <trans-unit id="Page 630922058 - View 713531721 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Vendors</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page WSA Integration Request Log - View Vendors - Property Caption</note>
        </trans-unit>
        <trans-unit id="PageExtension 1129455602 - Control 378437900 - Property **********" size-unit="char" translate="yes" xml:space="preserve" al-object-target="Page 1129455602">
          <source>Specifies whether the adjustment should be integrated with the POS.</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">PageExtension Item Journal Batches - Control WSA Integrate with POS - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="Enum ********** - EnumValue 2400746976 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Adjustment</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Enum WSA Request Type - EnumValue adjustments - Property Caption</note>
        </trans-unit>
        <trans-unit id="Enum ********** - EnumValue 2849948893 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Battery</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Enum WSA Request Type - EnumValue batteries - Property Caption</note>
        </trans-unit>
        <trans-unit id="Enum ********** - EnumValue 1291479322 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Category</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Enum WSA Request Type - EnumValue categories - Property Caption</note>
        </trans-unit>
        <trans-unit id="Enum ********** - EnumValue 1327948699 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Claim</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Enum WSA Request Type - EnumValue claims - Property Caption</note>
        </trans-unit>
        <trans-unit id="Enum ********** - EnumValue 3804383803 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Clinic</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Enum WSA Request Type - EnumValue clinics - Property Caption</note>
        </trans-unit>
        <trans-unit id="Enum ********** - EnumValue 3767720386 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Color</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Enum WSA Request Type - EnumValue colors - Property Caption</note>
        </trans-unit>
        <trans-unit id="Enum ********** - EnumValue 2563799520 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Manufacturer</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Enum WSA Request Type - EnumValue manufacturers - Property Caption</note>
        </trans-unit>
        <trans-unit id="Enum ********** - EnumValue 3007465456 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Model</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Enum WSA Request Type - EnumValue models - Property Caption</note>
        </trans-unit>
        <trans-unit id="Enum ********** - EnumValue 971209292 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Patient</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Enum WSA Request Type - EnumValue patients - Property Caption</note>
        </trans-unit>
        <trans-unit id="Enum ********** - EnumValue 954921427 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Payment</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Enum WSA Request Type - EnumValue payments - Property Caption</note>
        </trans-unit>
        <trans-unit id="Enum ********** - EnumValue 659163946 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Payor</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Enum WSA Request Type - EnumValue payors - Property Caption</note>
        </trans-unit>
        <trans-unit id="Enum ********** - EnumValue ********** - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Product</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Enum WSA Request Type - EnumValue products - Property Caption</note>
        </trans-unit>
        <trans-unit id="Enum ********** - EnumValue 3213736258 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Purchase Order</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Enum WSA Request Type - EnumValue purchaseOrders - Property Caption</note>
        </trans-unit>
        <trans-unit id="Enum ********** - EnumValue 1165194168 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Purchase Receipt</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Enum WSA Request Type - EnumValue purchaseReceipts - Property Caption</note>
        </trans-unit>
        <trans-unit id="Enum ********** - EnumValue 2213129374 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Purchase Return</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Enum WSA Request Type - EnumValue purchaseReturns - Property Caption</note>
        </trans-unit>
        <trans-unit id="Enum ********** - EnumValue 2650665660 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Purchase Shipment</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Enum WSA Request Type - EnumValue purchaseShipments - Property Caption</note>
        </trans-unit>
        <trans-unit id="Enum ********** - EnumValue 1892365954 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Sales Credit</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Enum WSA Request Type - EnumValue salesCredits - Property Caption</note>
        </trans-unit>
        <trans-unit id="Enum ********** - EnumValue 1312188414 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Sales Invoice</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Enum WSA Request Type - EnumValue salesInvoices - Property Caption</note>
        </trans-unit>
        <trans-unit id="Enum ********** - EnumValue 1828038775 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Sales Order</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Enum WSA Request Type - EnumValue salesOrders - Property Caption</note>
        </trans-unit>
        <trans-unit id="Enum ********** - EnumValue 2034977870 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Subcategory</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Enum WSA Request Type - EnumValue subcategories - Property Caption</note>
        </trans-unit>
        <trans-unit id="Enum ********** - EnumValue 2860254825 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Vendor</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Enum WSA Request Type - EnumValue vendors - Property Caption</note>
        </trans-unit>
        <trans-unit id="PermissionSet 3777878915 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Retail Integration Setup Table Permissions</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">PermissionSet RETAILINTEGSETUP - Property Caption</note>
        </trans-unit>
        <trans-unit id="PermissionSet 231688266 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>WSA Integration Request Log Table Permissions</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">PermissionSet WSAINTEGREQLOG - Property Caption</note>
        </trans-unit>
        <trans-unit id="Page 3328534530 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>JSON Viewer</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page API Log Json Viewer - Property Caption</note>
        </trans-unit>
        <trans-unit id="Page 3328534530 - Control 3649849801 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Choose Query Direction</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page API Log Json Viewer - Control Query Direction - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="Page 3328534530 - Control 3649849801 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Query Direction</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page API Log Json Viewer - Control Query Direction - Property Caption</note>
        </trans-unit>
        <trans-unit id="Page 3328534530 - Control 3649849801 - Property 62802879" size-unit="char" translate="yes" xml:space="preserve">
          <source>Request,Response</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page API Log Json Viewer - Control Query Direction - Property OptionCaption</note>
        </trans-unit>
        <trans-unit id="Page 3328534530 - Action 593408208 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Download content file</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page API Log Json Viewer - Action Download - Property Caption</note>
        </trans-unit>
        <trans-unit id="Page 3328534530 - Action 3351914939 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Request</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page API Log Json Viewer - Action Request - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="Page 3328534530 - Action 3351914939 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Request</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page API Log Json Viewer - Action Request - Property Caption</note>
        </trans-unit>
        <trans-unit id="Page 3328534530 - Action 2278520725 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Response</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page API Log Json Viewer - Action Response - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="Page 3328534530 - Action 2278520725 - Property **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>Response</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page API Log Json Viewer - Action Response - Property Caption</note>
        </trans-unit>
      </group>
    </body>
  </file>
</xliff>