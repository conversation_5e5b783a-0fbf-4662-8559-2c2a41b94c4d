﻿CREATE VIEW [cosium].[Vendors]
AS 

SELECT Source.id,
       Source.id AS Code,
       Source.id AS ExternalCode,
       Source.nomfournisseur AS [Name],
       Source.IntegrationRequired,
       Source.IntegrationDate,
       Source.ModifiedOn,
       t1.JSON

  FROM cosium.[fournisseur] AS Source

 OUTER APPLY  (
       SELECT (
              SELECT fournisseur.id AS 'code',
                     fournisseur.id AS 'externalCode',
                     fournisseur.nomfournisseur AS 'name',
                     fournisseur.adresse AS 'address',
                     fournisseur.adresse2 AS 'address2',
                     fournisseur.ville AS 'city',
                     CASE WHEN UPPER(fournisseur.pays) = 'FRANCE' THEN 'FR'
                          WHEN UPPER(fournisseur.pays) = 'INDIA' THEN 'IN'
                          WHEN UPPER(fournisseur.pays) = 'GERMANY' THEN 'DE'
                          ELSE fournisseur.pays
                      END AS 'country',
                     fournisseur.codepostal AS 'postalCode',
                     fournisseur.tel AS 'phone',
                     fournisseur.email AS 'email'

                FROM cosium.[fournisseur]

               WHERE [fournisseur].id = Source.id

                 FOR JSON PATH, WITHOUT_ARRAY_WRAPPER

                     ) AS JSON
              ) AS t1