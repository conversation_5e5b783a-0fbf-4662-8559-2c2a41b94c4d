﻿CREATE VIEW [dbo].[Adjustments]
AS

  WITH Source AS (
SELECT Adjustment.Id,
       ExternalSystem.Code AS ExternalSystemCode,
       Adjustment.DocumentNumber,
       LatestCouplings.ExternalCode AS ExternalReference,
       Product.Id AS ProductId,
       Product.Code AS ProductCode,
       LatestProductCouplings.ExternalCode AS ProductExternalCode,
       Product.[Name] AS ProductName,
       Clinic.Id AS ClinicId,
       Clinic.Code AS ClinicCode,
       LatestClinicCouplings.ExternalCode AS ClinicExternalCode,
       Clinic.[Name] AS ClinicName,
       Adjustment.DocumentDate,
       Adjustment.Quantity,
       Adjustment.SerialNumber,
       Adjustment.IntegrateWithPos,
       Adjustment.CreatedOn,
       Adjustment.ModifiedOn

  FROM dbo.Adjustment

  LEFT JOIN dbo.Product
    ON Adjustment.ProductId = Product.Id

  LEFT JOIN dbo.Clinic
    ON Adjustment.ClinicId = Clinic.Id

 CROSS JOIN dbo.ExternalSystem

 OUTER APPLY (
       SELECT TOP 1 Coupling.ExternalRecordId AS ExternalCode
         FROM dbo.Coupling
        WHERE Coupling.ExternalSystemId = ExternalSystem.Id
          AND Coupling.RecordId = Adjustment.Id
        ORDER BY ModifiedOn DESC) AS LatestCouplings

 OUTER APPLY (
       SELECT TOP 1 Coupling.ExternalRecordId AS ExternalCode
         FROM dbo.Coupling
        WHERE Coupling.ExternalSystemId = ExternalSystem.Id
          AND Coupling.RecordId = Product.Id
        ORDER BY ModifiedOn DESC) AS LatestProductCouplings

 OUTER APPLY (
       SELECT TOP 1 Coupling.ExternalRecordId AS ExternalCode
         FROM dbo.Coupling
        WHERE Coupling.ExternalSystemId = ExternalSystem.Id
          AND Coupling.RecordId = Product.Id
        ORDER BY ModifiedOn DESC) AS LatestClinicCouplings
     )

SELECT Source.Id,
       Source.ExternalSystemCode,
       Source.DocumentNumber,
       Source.ExternalReference,
       Source.ProductId,
       Source.ClinicId,
       Source.DocumentDate,
       Source.Quantity,
       Source.SerialNumber,
       Source.IntegrateWithPos,
       Source.[CreatedOn],
       Source.[ModifiedOn],
       (
       SELECT Source.Id AS 'id',
              Source.ExternalSystemCode AS 'externalSystemCode',
              Source.DocumentNumber AS 'documentNumber',
              Source.ExternalReference AS 'externalReference',
              Source.ProductId AS 'product.id',
              Source.ProductCode AS 'product.code',
              Source.ProductExternalCode AS 'product.externalCode',
              Source.ProductName AS 'product.name',
              Source.ClinicId AS 'clinic.id',
              Source.ClinicCode AS 'clinic.code',
              Source.ClinicExternalCode AS 'clinic.externalCode',
              Source.ClinicName AS 'clinic.name',
              Source.DocumentDate AS 'documentDate',
              Source.Quantity AS 'quantity',
              Source.SerialNumber AS 'serialNumber',
              Source.IntegrateWithPos AS 'integrateWithPos',
              Source.CreatedOn AS 'createdOn',
              Source.ModifiedOn AS 'modifiedOn'

           FOR JSON PATH, WITHOUT_ARRAY_WRAPPER
       ) AS JSON
  FROM Source