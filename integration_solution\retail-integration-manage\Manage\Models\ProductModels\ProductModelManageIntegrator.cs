﻿using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using WSA.Retail.Integration.Logging;
using WSA.Retail.Integration.Manage.API;
using WSA.Retail.Integration.Manage.Configuration;
using WSA.Retail.Integration.Models.Configuration;
using WSA.Retail.Integration.Models.ProductModels;
using WSA.Retail.Integration.Models.Products;
using WSA.Retail.Integration.Manage.Models.Products;
using WSA.Retail.Integration.Utilities;
using WSA.Retail.Integration.Manage.Models.CategoryMappings;
using System.Text.Json;


namespace WSA.Retail.Integration.Manage.Models.ProductModels;

public class ProductModelManageIntegrator(
    IOptions<AppSettings> appSettings,
    ILogger<ProductModelManageIntegrator> logger,
    ManageAPI manageApi,
    IProductModelService productModelService,
    IEntitySubscriberService subscriberService,
    ICategoryMappingService categoryMappingService) : IProductModelManageIntegrator
{
    private readonly AppSettings _appSettings = appSettings.Value;
    private readonly ILogger<ProductModelManageIntegrator> _logger = logger;
    private readonly ManageAPI _manageApi = manageApi;
    private readonly IProductModelService _productModelService = productModelService;
    private readonly IEntitySubscriberService _subscriberService = subscriberService;
    private readonly ICategoryMappingService _categoryMappingService = categoryMappingService;

    public async Task<ProductResponse?> GetFromManageAsync(Guid id)
    {
        _logger.LogMethodStart();

        try
        {
            ArgumentNullException.ThrowIfNull(_manageApi);
            var response = await _manageApi.ProductsGET2Async(id);
            if (response != null)
            {
                return response;
            }
        }
        catch (Exception ex)
        {
            _logger.LogCustomError(ex);
        }
        return null;
    }

    public async Task<ProductModel?> UpdateManageAsync(ProductModel productModel)
    {
        _logger.LogMethodStart();

        var subscriber = await _subscriberService.GetAsync(
            _appSettings.ExternalSystemCode,
            EntityType.ProductModel.GetEntityCode());

        if (subscriber?.ToExternalSystem ?? false)
        {
            ProductModel? existingProductModel = null;
            if (productModel.Id != Guid.Empty)
            {
                existingProductModel = await _productModelService.GetAsync(
                    _appSettings.ExternalSystemCode,
                    id: productModel.Id);
            }

            ProductResponse? response = null;
            if (!string.IsNullOrEmpty(existingProductModel?.ExternalCode) &&
                Guid.TryParse(existingProductModel.ExternalCode, out var manageId))
            {
                response = await GetFromManageAsync(manageId);
            }

            if (existingProductModel != null && response == null)
            {
                return await PostToManageAsync(existingProductModel);
            }
            else
            {
                if (existingProductModel != null && response != null)
                {
                    return await PutToManageAsync(existingProductModel, response);
                }
            }
        }
        return null;
    }

    private async Task<ProductModel?> PostToManageAsync(ProductModel productModel)
    {
        _logger.LogMethodStart(_appSettings.AppName);

        /*
        var childProducts = await _productModelService.GetChildProductsAsync(
            _appSettings.ExternalSystemCode,
            productModel.Id);

        childProducts = childProducts
            .Where(x => x.Category != null)
            .Where(x => x.Category.Code != null)
            .Where(x => x.Category.Code!.StartsWith("11") || x.Category.Code!.StartsWith("61")).ToList();

        if (childProducts == null || childProducts.Count == 0)
        {
            _logger.LogCustomWarning(_appSettings.AppName, nameof(PostToManageAsync),
                $"No child products found for product model {productModel.Id}");
            return null;
        }

        var request = childProducts.First().ToRequest();
        request.Colors ??= [];
        request.BatteryTypes ??= [];
        request.Attributes ??= [];
        request.SuggestedProductIds ??= [];

        request.Name = productModel.Name;
        request.VendorProductNumber = null;
        foreach (var childProduct in childProducts)
        {
            if (childProduct.Color != null && childProduct.Color.ExternalCode != null)
            {   
                if (Guid.TryParse(childProduct.Color.ExternalCode, out var colorId))
                {
                    request.Colors ??= [];
                    request.Colors.Add(colorId);
                }
            }
        }

        if (request.ManufacturerId == null || request.ManufacturerId == Guid.Empty)
        {
            if (Guid.TryParse(_appSettings.DefaultManufacturer, out var manufacturerId))
            {
                request.ManufacturerId = manufacturerId;
            }
        }

        var categoryMapping = await _categoryMappingService.GetAsync(childProducts.First().Category?.Id ?? Guid.Empty);
        if (categoryMapping != null)
        {
            request.HearingAidTypeId = categoryMapping.HearingAidTypeId;
        }

        var json = JsonSerializer.Serialize(request, Common.GetJsonOptions());
        _logger.LogCustomInformation(_appSettings.AppName, $"Sending Manage request:\r\n{json}");

        CreatedResponse? response = null;
        try
        {
            ArgumentNullException.ThrowIfNull(_manageApi);
            response = await _manageApi.ProductsPOSTAsync(request);
        }
        catch(Exception ex)
        {
            
            _logger.LogCustomError(ex, _appSettings.AppName, nameof(PostToManageAsync),
                $"Failed to insert new product model with code {productModel.Code} in Manage");
        }

        if (response != null)
        {
            try
            {
                productModel.ExternalSystemCode = _appSettings.ExternalSystemCode;
                productModel.ExternalCode = response.Id.ToString();
                await _productModelService.UpsertAsync(productModel);
                _logger.LogCustomInformation(_appSettings.AppName, nameof(PostToManageAsync),
                    $"Inserted new ProductModel with ID {response.Id} in Manage");
                return productModel;
            }
            catch (Exception ex)
            {
                _logger.LogCustomError(ex, _appSettings.AppName, nameof(PostToManageAsync));
                return null;
            }
        }
        */
        return null;
    }

    private async Task<ProductModel?> PutToManageAsync(ProductModel updatedProductModel, ProductResponse oldProductResponse)
    {
        _logger.LogMethodStart(_appSettings.AppName);

        /*
        if (updatedProductModel != null && oldProductResponse != null)
        {
            var childProducts = await _productModelService.GetChildProductsAsync(
                _appSettings.ExternalSystemCode,
                updatedProductModel.Id);

            if (childProducts == null || childProducts.Count == 0)
            {
                _logger.LogCustomWarning(_appSettings.AppName, nameof(PostToManageAsync),
                    $"No child products found for product model {updatedProductModel.Id}");
                return null;
            }

            if (HasChanges(updatedProductModel, childProducts.First(), oldProductResponse) ||
                    childProducts.Count > oldProductResponse.Colors.Count)
            {
                var request = oldProductResponse.ToRequest();
                request.Name = updatedProductModel.Name;
                request.VendorProductNumber = oldProductResponse.VendorProductNumber;
                request.Attributes = [];
                request.SuggestedProductIds = [];

                if (Guid.TryParse(childProducts.First().Manufacturer?.ExternalCode, out var manufacturerId))
                {
                    request.ManufacturerId = manufacturerId;
                }
                if (Guid.TryParse(childProducts.First().Vendor?.ExternalCode, out var vendorId))
                {
                    request.SupplierId = vendorId;
                }
                foreach (var childProduct in childProducts)
                {
                    if (childProduct.Color != null && childProduct.Color.ExternalCode != null)
                    {
                        if (Guid.TryParse(childProduct.Color.ExternalCode, out var colorId))
                        {
                            if (!request.Colors.Where(c => c == colorId).Any())
                            {
                                request.Colors.Add(colorId);
                            }
                        }
                    }
                }

                if (request.ManufacturerId == null || request.ManufacturerId == Guid.Empty)
                {
                    request.ManufacturerId = oldProductResponse.Manufacturer.Id;
                }

                var json = JsonSerializer.Serialize(request, Utilities.Common.GetJsonOptions());
                _logger.LogCustomInformation(_appSettings.AppName, $"Sending Manage request:\r\n{json}");

                try
                {
                    ArgumentNullException.ThrowIfNull(_manageApi);
                    await _manageApi.ProductsPUTAsync(oldProductResponse.Id, request);

                    _logger.LogCustomInformation(_appSettings.AppName, nameof(PutToManageAsync),
                        $"Updated vendor with ID {oldProductResponse.Id} in Manage");

                    var response = await GetFromManageAsync(oldProductResponse.Id);
                    return updatedProductModel;
                }
                catch (Exception ex)
                {
                    _logger.LogCustomError(ex, _appSettings.AppName, nameof(PutToManageAsync));
                    return null;
                }
            }
            else
            {
                return updatedProductModel;
            }
        }
        */
        return null;
    }

    private static bool HasChanges(ProductModel newProductModel, Product firstChildProduct, ProductResponse oldProduct)
    {
        if (oldProduct == null) return true;
        if (!Common.AreEqual(firstChildProduct.Manufacturer?.ExternalCode, oldProduct.Manufacturer.Id.ToString())) return true;
        if (!Common.AreEqual(firstChildProduct.Vendor?.ExternalCode, oldProduct.Supplier.Id.ToString())) return true;
        if (!Common.AreEqual(newProductModel.Name, oldProduct.Name)) return true;
        if (!Common.AreEqual(firstChildProduct.PimProductId, oldProduct.VendorProductNumber)) return true;
        return false;
    }
}