﻿CREATE TABLE [cosium].[staging_facture] (
    [id]                                       NVARCHAR (50)  NOT NULL,
    [refclient]                                NVARCHAR (50)  NULL,
    [numfacture]                               NVARCHAR (100)  NULL,
    [datefacture]                              NVARCHAR (50)  NULL,
    [numavoir]                                 NVARCHAR (50)  NULL,
    [totalht]                                  NVARCHAR (50)  NULL,
    [totaltva]                                 NVARCHAR (50)  NULL,
    [totalnet]                                 NVARCHAR (50)  NULL,
    [restedu]                                  NVARCHAR (50)  NULL,
    [totalttc]                                 NVARCHAR (50)  NULL,
    [centre]                                   NVARCHAR (50)  NULL,
    [typefacture]                              NVARCHAR (50)  NULL,
    [facturefinale]                            NVARCHAR (50)  NULL,
    [adresseclient1]                           NVARCHAR (200)  NULL,
    [adresseclient2]                           NVARCHAR (200)  NULL,
    [adresseclient3]                           NVARCHAR (200)  NULL,
    [adresseclient4]                           NVARCHAR (200)  NULL,
    [adresseclient5]                           NVARCHAR (200)  NULL,
    [numsecuclient]                            NVARCHAR (50)  NULL,
    [mutuelleclient]                           NVARCHAR (200) NULL,
    [numadherentclient]                        NVARCHAR (50)  NULL,
    [nomclient]                                NVARCHAR (200) NULL,
    [nomassure]                                NVARCHAR (200) NULL,
    [commentaire]                              NVARCHAR (MAX) NULL,
    [datecreation]                             NVARCHAR (50)  NULL,
    [alerte]                                   NVARCHAR (50)  NULL,
    [partro]                                   NVARCHAR (50)  NULL,
    [partrc]                                   NVARCHAR (50)  NULL,
    [numadeliaudioclient]                      NVARCHAR (20)  NULL,
    [refvendeur]                               NVARCHAR (50)  NULL,
    [htorttc]                                  NVARCHAR (50)  NULL,
    [caissesecuclient]                         NVARCHAR (200) NULL,
    [tauxrbstss]                               NVARCHAR (50)  NULL,
    [prescripteur]                             NVARCHAR (50)  NULL,
    [dateordonnance]                           NVARCHAR (50)  NULL,
    [refprescripteur]                          NVARCHAR (50)  NULL,
    [datenaissance]                            NVARCHAR (50)  NULL,
    [article115]                               NVARCHAR (50)  NULL,
    [telclient]                                NVARCHAR (50)  NULL,
    [dateentreefacture]                        NVARCHAR (50)  NULL,
    [regul]                                    NVARCHAR (50)  NULL,
    [datenaisslunairefact]                     NVARCHAR (50)  NULL,
    [assurenomfact]                            NVARCHAR (200)  NULL,
    [assureprenomfact]                         NVARCHAR (50)  NULL,
    [assuretitrefact]                          NVARCHAR (50)  NULL,
    [dateexpcomptafact]                        NVARCHAR (50)  NULL,
    [datearchivage]                            NVARCHAR (50)  NULL,
    [creepar]                                  NVARCHAR (50)  NULL,
    [rc2]                                      NVARCHAR (50)  NULL,
    [rc3]                                      NVARCHAR (50)  NULL,
    [partrc1]                                  NVARCHAR (50)  NULL,
    [partrc2]                                  NVARCHAR (50)  NULL,
    [partrc3]                                  NVARCHAR (50)  NULL,
    [datemodif]                                NVARCHAR (50)  NULL,
    [validation]                               NVARCHAR (50)  NULL,
    [datevalidationdevis]                      NVARCHAR (50)  NULL,
    [reflienmedecinadresseprescripteurfacture] NVARCHAR (50)  NULL,
    [downpayment]                              NVARCHAR (50)  NULL,
    [updatestock]                              NVARCHAR (50)  NULL,
    [retrocession]                             NVARCHAR (50)  NULL,
);
GO;