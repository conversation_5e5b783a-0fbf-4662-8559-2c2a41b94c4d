﻿using Microsoft.EntityFrameworkCore;
using System.Text.Json;
using WSA.Retail.Integration.Models.Configuration;
using WSA.Retail.Integration.Models.Couplings;
using WSA.Retail.Integration.Models.Patients;
using WSA.Retail.Integration.Tests.Configuration;

namespace WSA.Retail.Integration.Tests.Data;

public static class TestDataLoader
{
    public async static Task<List<PatientEntity>> LoadPatients(TestIntegrationContext dbContext, bool couple = false)
    {
        var appSettings = TestAppSettings.CreateDefault();
                         
        var filePath = Path.Combine(Directory.GetCurrentDirectory(), "Tests", "Data", "TestData", "Patients.json");
        var json = File.ReadAllText(filePath);

        var patientEntities = new List<PatientEntity>();
        var couplingEntities = new List<CouplingEntity>();

        var patients = JsonSerializer.Deserialize<List<Patient>>(json, Utilities.Common.GetJsonOptions());
        if (patients != null)
        {
            var patientGroups = patients.GroupBy(x => x.Code);
            foreach (var group in patientGroups)
            {
                var patient = group.FirstOrDefault();
                if (patient != null)
                {
                   /* var patientEntity = patient.ToEntity();
                    patientEntity.CreatedOn = DateTime.UtcNow;
                    patientEntity.ModifiedOn = DateTime.UtcNow;
                    patientEntities.Add(patientEntity);*/

                    if (couple)
                    {
                        foreach (var couplingPatient in group)
                        {
                            var externalSystem = dbContext.EntityEntity
                                .Where(x => x.Code == couplingPatient.ExternalSystemCode)
                                .FirstOrDefault();

                            ArgumentNullException.ThrowIfNull(externalSystem);
                            var couplingEntity = new CouplingEntity()
                            {
                                Id = Guid.NewGuid(),
                                EntityId = TestReferenceData.PatientId,
                                ExternalSystemId = externalSystem.Id,
                                RecordId = patient.Id,
                                ExternalRecordId = couplingPatient.ExternalCode
                            };

                            couplingEntities.Add(couplingEntity);
                        }
                    }
                }
            }
        }

        dbContext.PatientEntity.AddRange(patientEntities);
        if (couple) dbContext.CouplingEntity.AddRange(couplingEntities);
        await dbContext.SaveChangesAsync();

        return patientEntities ?? [];
    }
}