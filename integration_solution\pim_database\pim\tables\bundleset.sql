﻿CREATE TABLE [pim].[BundleSet]
(
    [Id]                    UNIQUEIDENTIFIER CONSTRAINT [DF_BundleSet_Id] DEFAULT NEWID() NOT NULL,
    [Name]                  NVARCHAR(100) NOT NULL,
    [Type]                  NVARCHAR(20) NULL,
    [IsDefault]             BIT NULL,
    [IsMandatory]           BIT NULL,
    [IsMasterData]          BIT NULL,
    [Ranking]               INT NULL,
    [State]                 NVARCHAR(20) NULL,
    [CreatedAt]             DATETIME2(7) NULL,
    [UpdatedAt]             DATETIME2(7) NULL,
    [SystemCreatedOn]       DATETIME2 CONSTRAINT [DF_BundleSet_SystemCreatedOn] DEFAULT (sysutcdatetime()) NOT NULL,
    [SystemModifiedOn]      DATETIME2 CONSTRAINT [DF_BundleSet_SystemModifiedOn] DEFAULT (sysutcdatetime()) NOT NULL,
    CONSTRAINT              [PK_BundleSet_Id] PRIMARY KEY CLUSTERED ([Id] ASC)
)
GO