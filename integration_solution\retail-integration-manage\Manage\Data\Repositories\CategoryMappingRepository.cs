﻿using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using WSA.Retail.Integration.Configuration;
using WSA.Retail.Integration.Logging;
using WSA.Retail.Integration.Manage.Data.Repositories.Interfaces;
using WSA.Retail.Integration.Manage.Models.CategoryMappings;


namespace WSA.Retail.Integration.Manage.Data.Repositories;

public class CategoryMappingRepository(
    IOptions<AppSettings> options,
    ILogger<CategoryMappingRepository> logger,
    IDbContextFactory<ManageDbContext> dbContextFactory)
    : ICategoryMappingRepository
{
    private readonly AppSettings _appSettings = options.Value;
    private readonly ILogger<CategoryMappingRepository> _logger = logger;
    private readonly IDbContextFactory<ManageDbContext> _dbContextFactory = dbContextFactory;

    public async Task<CategoryMapping?> GetAsync(Guid categoryId)
    {
        _logger.LogMethodStart(_appSettings.AppName);
        using var context = _dbContextFactory.CreateDbContext();

        var entity = await context.CategoryMappingEntity
            .FirstOrDefaultAsync(x => x.CategoryId == categoryId);

        return entity?.ToModel();
    }

    public async Task<bool> InsertAsync(CategoryMapping categoryMapping)
    {
        _logger.LogMethodStart(_appSettings.AppName);

        using var context = _dbContextFactory.CreateDbContext();
        try
        {
            var entity = categoryMapping.ToEntity();
            context.CategoryMappingEntity.Add(entity);
            await context.SaveChangesAsync();
            categoryMapping.Id = entity.Id;
            return true;
        }
        catch (DbUpdateException ex)
        {
            _logger.LogCustomError(ex,
                $"Encountered a database error while inserting CategoryMapping record: {ex.Message}");
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogCustomError(ex);
            return false;
        }
    }

    public async Task<bool> UpdateAsync(CategoryMapping categoryMapping)
    {
        _logger.LogMethodStart(_appSettings.AppName);

        if (categoryMapping.Id == Guid.Empty)
        {
            _logger.LogCustomWarning(
                $"CategoryMapping for CategoryId {categoryMapping.CategoryId} cannot be updated because it has a null or missing Id.");
            return false;
        }

        using var context = _dbContextFactory.CreateDbContext();

        // Retrieve the existing CategoryMapping
        var existingMapping = await context.CategoryMappingEntity.FindAsync(categoryMapping.Id);
        if (existingMapping == null)
        {
            _logger.LogWarning("CategoryMapping with ID {Id} not found for update.", categoryMapping.Id);
            return false;
        }

        // Update CategoryMapping fields
        context.Entry(existingMapping).CurrentValues.SetValues(categoryMapping.ToEntity());
        try
        {
            await context.SaveChangesAsync();

            _logger.LogCustomInformation(
                $"CategoryMapping for CategoryId {categoryMapping.CategoryId} was successfully updated in the database.");
            return true;
        }
        catch (DbUpdateException ex)
        {
            _logger.LogCustomError(ex,
                $"A database error while updating CategoryMapping record: {ex.Message}");
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogCustomError(ex);
            return false;
        }
    }
}
