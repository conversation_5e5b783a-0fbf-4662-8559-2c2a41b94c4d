{"profiles": {"we-dev": {"commandName": "Project", "commandLineArgs": "--port 7151", "launchBrowser": false, "environmentVariables": {"AppName": "Manage", "EventGridEndpoint": "https://wsa-retail-integration-wedev.westeurope-1.eventgrid.azure.net/api/events", "EventGridAccessKey": "yyIEHEjpWLFkOXXbGzoajWxWBH64Htuo+vdyGaa+uE4=", "EventHubConnectionString": "Endpoint=sb://manage-dev-ga4-eventhub.servicebus.windows.net/;SharedAccessKeyName=WSA;SharedAccessKey=mEj62CUA4TPd9rkwFS1b1n32UQWmowcmj+AEhLgFQKk=", "EventHubConsumerGroup": "wsa_bc", "EventHubName": "manage9-wsa", "ExternalSystemCode": "MANAGE", "FromStorageQueueName": "from-manage", "ManageApiKey": "YXVfd3NhXzIuNWVkNDNhNmMtZmFhYS00NjFkLThiNjktZWFiMTM2ZmI4MzkxLjNlNjQ4OWQyZDk=", "ManageApiBaseUrl": "https://eu-dev-ga4-manageapigateway.auditdata.app", "QueueRetryInterval": "5", "QueueMaxRetryInterval": "1440", "SqlConnectionString": "Server=tcp:retail-integration-we-dev.database.windows.net,1433;Initial Catalog=we-dev;Persist Security Info=False;User ID=manage;Password=*********;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;", "StorageQueueConnectionString": "DefaultEndpointsProtocol=https;AccountName=retailintegrationwedev;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net", "ToStorageQueueName": "to-manage", "DefaultCountry": "AU", "EnableDetailedDbLogging": "false", "DefaultManufacturer": "f90020da-bb01-f011-90cb-0022489e219e"}}, "au_uat": {"commandName": "Project", "commandLineArgs": "--port 7251", "launchBrowser": false, "environmentVariables": {"APPLICATIONINSIGHTS_CONNECTION_STRING": "InstrumentationKey=68b5ae8e-c829-44a9-891f-c8cfd8df9a7c;IngestionEndpoint=https://australiaeast-1.in.applicationinsights.azure.com/;LiveEndpoint=https://australiaeast.livediagnostics.monitor.azure.com/;ApplicationId=f7b09d36-46e6-4a0d-a3f4-32e3e5c2c3e5", "AppName": "Manage", "EventGridEndpoint": "https://wsa-retail-integration-auuat.australiaeast-1.eventgrid.azure.net/api/events", "EventGridAccessKey": "CB2Rch114kqTx4i63EgRWtpl7qOoxr1PbGhkq8mqLZv1vyWqtpInJQQJ99BCACL93NaXJ3w3AAABAZEGYr5G", "ExternalSystemCode": "MANAGE", "EventHubConnectionString": "Endpoint=sb://manage-au-wsa-uat-evh.servicebus.windows.net/;SharedAccessKeyName=wsa;SharedAccessKey=athxVDvFcWpJEx8uPXkYHJyHpz86xa35i+AEhAygTvA=", "EventHubConsumerGroup": "bc", "EventHubName": "entityevents-uat", "FromStorageQueueName": "from-manage", "ManageApiKey": "S2V5X1dTQV9VQVQuYjRmMWNmODctZDU0Yi00ODg3LWEzZjUtYTUxNWE1OWQ3ZjAyLjZhMDQyMDk0ODQ=", "ManageApiBaseUrl": "https://au-wsa-uat-manageapigateway.auditdata.app", "ManageTenantId": "b4f1cf87-d54b-4887-a3f5-a515a59d7f02", "LocalTimeZone": "Australia/Melbourne", "QueueRetryInterval": "5", "QueueMaxRetryInterval": "1440", "SqlConnectionString": "Server=tcp:retail-integration-au.database.windows.net,1433;Initial Catalog=au-uat;Persist Security Info=False;User ID=manage;Password=*********;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;", "StorageQueueConnectionString": "DefaultEndpointsProtocol=https;AccountName=retailintegrationauuat;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net", "ToStorageQueueName": "to-manage", "DefaultCountry": "AU", "EnableDetailedDbLogging": "false", "DefaultManufacturer": "36c9c19d-f503-f011-846f-002248941ab2"}}}}