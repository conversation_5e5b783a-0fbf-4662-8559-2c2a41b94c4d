﻿CREATE VIEW [dbo].[SalesOrders]
AS

SELECT Source.Id,
       Source.DocumentNumber,
       Source.ModifiedOn,
       Coupling.ExternalSystemId,
       t1.JSON

  FROM dbo.SalesOrder AS Source

       LEFT JOIN dbo.Coupling
              ON Coupling.RecordId = Source.Id

 OUTER APPLY (SELECT(
              SELECT SalesOrder.Id AS id,
                     SalesOrder.DocumentNumber AS documentNumber,
                     Coupling.ExternalRecordId AS externalReference,
                     SalesOrder.AlternateNumber AS alternateNumber,
                     SalesOrder.DocumentDate AS documentDate,
                     Patient.Id AS 'patient.id',
                     Patient.Code AS 'patient.code',
                     PatientCoupling.ExternalRecordId AS 'patient.externalCode',
                     Patient.[Name] AS 'patient.name',
                     Clinic.Id AS 'clinic.id',
                     Clinic.Code AS 'clinic.code',
                     ClinicCoupling.ExternalRecordId AS 'clinic.externalCode',
                     Clinic.[Name] AS 'clinic.name',
                     (
                            SELECT SalesOrderLine.[Sequence] AS [sequence],
                                   Product.Id AS 'product.id',
                                   Product.Code AS 'product.code',
                                   ProductCoupling.ExternalRecordId AS 'product.externalCode',
                                   Product.[Name] AS 'product.name',
                                   SalesOrderLine.[Description] AS [description],
                                   SalesOrderLine.[Quantity] AS quantity,
                                   SalesOrderLine.[UnitPrice] AS unitPrice,
                                   SalesOrderLine.[GrossAmount] AS grossAmount,
                                   SalesOrderLine.[DiscountAmount] AS discountAmount,
                                   SalesOrderLine.[AmountInclTax] AS amountInclTax,
                                   SalesOrderLine.[TaxAmount] AS taxAmount,
                                   SalesOrderLine.[AmountExclTax] AS amountExclTax

                              FROM dbo.SalesOrderLine

                                   LEFT JOIN dbo.Product
                                          ON SalesOrderLine.ProductId = Product.Id

                                   LEFT JOIN dbo.Coupling AS ProductCoupling
                                          ON Product.Id = ProductCoupling.RecordId
                                         AND ProductCoupling.ExternalSystemId = Coupling.ExternalSystemId

                             WHERE SalesOrderLine.SalesOrderId = SalesOrder.Id

                             ORDER BY SalesOrderLine.[Sequence]

                               FOR JSON PATH
                     ) AS salesOrderLines

                FROM dbo.SalesOrder

                     INNER JOIN dbo.Patient
                             ON SalesOrder.PatientId = Patient.Id

                      LEFT JOIN dbo.Coupling AS PatientCoupling
                             ON Patient.Id = PatientCoupling.RecordId
                            AND PatientCoupling.ExternalSystemId = Coupling.ExternalSystemId

                     INNER JOIN dbo.Clinic
                             ON SalesOrder.ClinicId = Clinic.Id

                      LEFT JOIN dbo.Coupling AS ClinicCoupling
                             ON Clinic.Id = ClinicCoupling.RecordId
                            AND ClinicCoupling.ExternalSystemId = Coupling.ExternalSystemId

               WHERE SalesOrder.Id = Source.Id

                FOR JSON PATH, WITHOUT_ARRAY_WRAPPER
                ) AS JSON
                ) AS t1