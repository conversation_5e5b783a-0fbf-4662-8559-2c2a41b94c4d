namespace WSA.Integration.API.V1;

using Microsoft.Integration.Graph;
using Microsoft.Inventory.Location;
using WSA.Integration;


codeunit 50111 "WSA Clinic Handler" implements "WSA Integration Request"
{
    TableNo = "WSA Integration Request Log";

    trigger OnRun()
    begin
        Code(Rec);
    end;


    procedure HandleRequest(var Request: Record "WSA Integration Request Log")
    begin
        if not Codeunit.Run(Codeunit::"WSA Clinic Handler", Request) then begin
            Common.SetErrorResponse(Request, '');
        end;
    end;


    local procedure Code(var Request: Record "WSA Integration Request Log")
    var
        json: JsonObject;

    begin
        case Request.Method of
            Request.Method::post:
                HandlePost(Request);
            Request.Method::get:
                HandleGet(Request);
        end;
    end;


    local procedure HandlePost(var Request: Record "WSA Integration Request Log")
    var
        responsibilityCenter: Record "Responsibility Center";

    begin
        if TryHandlePost(Request, responsibilityCenter) then
            Common.SetCreatedResponse(Request, Common.ClinicToJson(responsibilityCenter))
        else
            Common.SetErrorResponse(Request, '');
    end;


    local procedure HandleGet(var Request: Record "WSA Integration Request Log")
    var
        responsibilityCenter: Record "Responsibility Center";

    begin
        if not TryHandleGet(Request, responsibilityCenter) then
            Common.SetErrorResponse(Request, '');
    end;


    [TryFunction]
    local procedure TryHandlePost(
        var Request: Record "WSA Integration Request Log";
        var ResponsibilityCenter: Record "Responsibility Center")

    var
        recRef: RecordRef;
        json: JsonObject;

    begin
        Request.TestField("Request Content");
        json := Common.GetJsonFromBlob(Request);

        if not ResponsibilityCenter.Get(Common.GetJsonValue(json, '$.code').AsCode()) then begin
            ResponsibilityCenter.Init();
            ResponsibilityCenter.Code := Common.GetJsonValue(json, '$.code').AsCode();
            ResponsibilityCenter.Insert(false);
        end;

        recRef.GetTable(ResponsibilityCenter);
        Common.ValidateFieldFromJson(json, '$.id', recRef, ResponsibilityCenter.FieldNo("External Code"), TempFieldSet);
        Common.ValidateFieldFromJson(json, '$.name', recRef, ResponsibilityCenter.FieldNo(Name), TempFieldSet);
        Common.ValidateFieldFromJson(json, '$.address', recRef, ResponsibilityCenter.FieldNo(Address), TempFieldSet);
        Common.ValidateFieldFromJson(json, '$.address2', recRef, ResponsibilityCenter.FieldNo("Address 2"), TempFieldSet);
        Common.ValidateFieldFromJson(json, '$.country', recRef, ResponsibilityCenter.FieldNo("Country/Region Code"), TempFieldSet);
        Common.ValidateFieldFromJson(json, '$.region', recRef, ResponsibilityCenter.FieldNo(County), TempFieldSet);
        Common.ValidateFieldFromJson(json, '$.postalCode', recRef, ResponsibilityCenter.FieldNo("Post Code"), TempFieldSet);
        Common.ValidateFieldFromJson(json, '$.city', recRef, ResponsibilityCenter.FieldNo(City), TempFieldSet);
        Common.ValidateFieldFromJson(json, '$.phone', recRef, ResponsibilityCenter.FieldNo("Phone No."), TempFieldSet);

        GraphMgtGeneralTools.ProcessNewRecordFromAPI(recRef, TempFieldSet, CurrentDateTime());

        recRef.SetTable(ResponsibilityCenter);
        ResponsibilityCenter.Modify();

        HandleLocation(ResponsibilityCenter);

        Request."Clinic No." := ResponsibilityCenter.Code;
        Request.Modify();
    end;


    [TryFunction]
    local procedure TryHandleGet(
        var Request: Record "WSA Integration Request Log";
        var ResponsibilityCenter: Record "Responsibility Center")

    var
        recRef: RecordRef;
        json: JsonObject;

    begin
        Request.TestField("Request Content");
        json := Common.GetJsonFromBlob(Request);

        if not (Common.GetJsonValue(json, '$.id').IsNull) then begin
            if ResponsibilityCenter.GetBySystemId(Common.GetJsonValue(json, '$.id').AsText()) then begin
                Request."Clinic No." := ResponsibilityCenter.Code;
                Request.Modify();
                Common.SetOkResponse(Request, Common.ClinicToJson(ResponsibilityCenter));
                exit;
            end;
        end;

        if not (Common.GetJsonValue(json, '$.code').IsNull) then begin
            ResponsibilityCenter.SetRange(Code, Common.GetJsonValue(json, '$.code').AsCode());
            if ResponsibilityCenter.FindFirst() then begin
                Request."Clinic No." := ResponsibilityCenter.Code;
                Request.Modify();
                Common.SetOkResponse(Request, Common.ClinicToJson(ResponsibilityCenter));
                exit;
            end
        end;

        if not (Common.GetJsonValue(json, '$.name').IsNull) then begin
            ResponsibilityCenter.SetRange(Name, Common.GetJsonValue(json, '$.name').AsCode());
            if ResponsibilityCenter.FindFirst() then begin
                Request."Clinic No." := ResponsibilityCenter.Code;
                Request.Modify();
                Common.SetOkResponse(Request, Common.ClinicToJson(ResponsibilityCenter));
                exit;
            end
        end;

        Common.SetNoContentResponse(Request);
    end;


    local procedure HandleLocation(var ResponsibilityCenter: Record "Responsibility Center")
    begin
        if ResponsibilityCenter."Location Code" = '' then begin
            CreateLocation(ResponsibilityCenter);
            ResponsibilityCenter.Modify();
        end;
        UpdateLocation(ResponsibilityCenter);
    end;


    local procedure CreateLocation(var ResponsibilityCenter: Record "Responsibility Center")
    var
        Location: Record Location;

    begin
        if ResponsibilityCenter."Location Code" <> '' then
            exit;

        if not (Location.Get(ResponsibilityCenter.Code)) then begin
            Location.Init();
            Location.Code := ResponsibilityCenter.Code;
            Location.Name := ResponsibilityCenter.Name;
            Location.Insert(true);
        end;

        ResponsibilityCenter.Validate("Location Code", Location.Code);
    end;


    local procedure UpdateLocation(ResponsibilityCenter: Record "Responsibility Center")
    var
        Location: Record Location;
        ModifyRequired: Boolean;

    begin
        ResponsibilityCenter.TestField("Location Code");
        Location.Get(ResponsibilityCenter."Location Code");

        if Location.Name <> ResponsibilityCenter.Name then begin
            Location.Validate(Name, ResponsibilityCenter.Name);
            ModifyRequired := true;
        end;

        if Location.Address <> ResponsibilityCenter.Address then begin
            Location.Validate(Address, ResponsibilityCenter.Address);
            ModifyRequired := true;
        end;

        if Location."Address 2" <> ResponsibilityCenter."Address 2" then begin
            Location.Validate("Address 2", ResponsibilityCenter."Address 2");
            ModifyRequired := true;
        end;

        if Location."Country/Region Code" <> ResponsibilityCenter."Country/Region Code" then begin
            Location.Validate("Country/Region Code", ResponsibilityCenter."Country/Region Code");
            ModifyRequired := true;
        end;

        if Location.County <> ResponsibilityCenter.County then begin
            Location.Validate(County, ResponsibilityCenter.County);
            ModifyRequired := true;
        end;

        if Location."Post Code" <> ResponsibilityCenter."Post Code" then begin
            Location.Validate("Post Code", ResponsibilityCenter."Post Code");
            ModifyRequired := true;
        end;

        if Location.City <> ResponsibilityCenter.City then begin
            Location.Validate(City, ResponsibilityCenter.City);
            ModifyRequired := true;
        end;

        if Location."Phone No." <> ResponsibilityCenter."Phone No." then begin
            Location.Validate("Phone No.", ResponsibilityCenter."Phone No.");
            ModifyRequired := true;
        end;

        if ModifyRequired then
            Location.Modify;
    end;


    var
        TempFieldSet: Record 2000000041 temporary;
        GraphMgtGeneralTools: Codeunit "Graph Mgt - General Tools";
        Common: Codeunit "WSA Common";
}
