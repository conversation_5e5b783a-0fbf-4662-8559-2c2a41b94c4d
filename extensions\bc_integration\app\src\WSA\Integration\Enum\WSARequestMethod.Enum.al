namespace WSA.Integration;

enum 50102 "WSA Request Method"
{
    Extensible = true;

    value(1; get)
    {
        Caption = 'get', Locked = true;
    }

    value(2; post)
    {
        Caption = 'post', Locked = true;
    }

    value(3; patch)
    {
        Caption = 'patch', Locked = true;
    }

    value(4; delete)
    {
        Caption = 'delete', Locked = true;
    }

    value(5; put)
    {
        Caption = 'put', Locked = true;
    }
}
