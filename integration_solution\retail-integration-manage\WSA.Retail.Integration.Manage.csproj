﻿<Project Sdk="Microsoft.NET.Sdk">
	<PropertyGroup>
		<TargetFramework>net8.0</TargetFramework>
		<AzureFunctionsVersion>v4</AzureFunctionsVersion>
		<OutputType>Exe</OutputType>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>enable</Nullable>
		<RootNamespace>WSA.Retail.Integration</RootNamespace>
		<Configurations>Debug;Release;we-dev</Configurations>
	</PropertyGroup>
	<ItemGroup>
	  <None Remove="OpenAPIs\swagger.json" />
	</ItemGroup>
	<ItemGroup>
		<FrameworkReference Include="Microsoft.AspNetCore.App" />
		<OpenApiReference Include="OpenAPIs\swagger.json" Namespace="WSA.Retail.Integration.Manage.API" ClassName="ManageAPI" />
		<PackageReference Include="AutoMapper" Version="14.0.0" />
		<PackageReference Include="Azure.Messaging.EventGrid" Version="4.31.0" />
		<PackageReference Include="Azure.Storage.Blobs" Version="12.24.1" />
		<PackageReference Include="Azure.Storage.Queues" Version="12.22.0" />
		<PackageReference Include="Microsoft.Azure.Functions.Worker" Version="2.0.0" />
		<PackageReference Include="Microsoft.Azure.Functions.Worker.Extensions.EventGrid" Version="3.5.0" />
		<PackageReference Include="Microsoft.Azure.Functions.Worker.Extensions.EventHubs" Version="6.5.0" />
		<PackageReference Include="Microsoft.Azure.Functions.Worker.Extensions.Http" Version="3.3.0" />
		<PackageReference Include="Microsoft.Azure.Functions.Worker.Extensions.Http.AspNetCore" Version="2.0.2" />
		<PackageReference Include="Microsoft.Azure.Functions.Worker.Extensions.Storage.Queues" Version="5.5.2" />
		<PackageReference Include="Microsoft.Azure.Functions.Worker.Sdk" Version="2.0.5" />
		<PackageReference Include="Microsoft.ApplicationInsights.WorkerService" Version="2.23.0" />
		<PackageReference Include="Microsoft.Azure.Functions.Worker.ApplicationInsights" Version="2.0.0" />
		<PackageReference Include="Microsoft.Extensions.ApiDescription.Client" Version="10.0.0-preview.1.25120.3">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
		<PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
		<PackageReference Include="NSwag.ApiDescription.Client" Version="14.4.0">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
		<PackageReference Include="WSA.Retail.Integration.Model" Version="0.0.454" />
	</ItemGroup>
	<ItemGroup>
		<None Update="host.json">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
		<None Update="local.settings.json">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
			<CopyToPublishDirectory>Never</CopyToPublishDirectory>
		</None>
	</ItemGroup>
	<ItemGroup>
		<Using Include="System.Threading.ExecutionContext" Alias="ExecutionContext" />
	</ItemGroup>
</Project>