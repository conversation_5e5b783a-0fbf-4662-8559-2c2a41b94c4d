﻿CREATE TABLE [cosium].[utilisateurs](
	[id] [nvarchar](50) NOT NULL,
	[nom] [varchar](max) NULL,
	[prenom] [varchar](max) NULL,
	[login] [varchar](max) NULL,
	[nomprofil] [varchar](max) NULL,
	[datemodif] [varchar](50) NULL,
	[cache] [varchar](max) NULL,
	[email] [varchar](max) NULL,
	[isaudio] [varchar](50) NULL,
	[datecreation] [varchar](max) NULL,
	[titre] [varchar](max) NULL,
	[numadeli] [varchar](max) NULL,
	[isopticien] [varchar](max) NULL,
	[supprime] [varchar](max) NULL,
	[datesuppression] [varchar](max) NULL,
	[registrationNumber] [varchar](max) NULL,
	[CreatedOn] [datetime2](7) NOT NULL,
	[ModifiedOn] [datetime2](7) NOT NULL,
	[code] [varchar](max) NULL,
	[name] [varchar](max) NULL,
	[matricule] [varchar](max) NULL,
	[locked] [varchar](max) NULL,
 CONSTRAINT [PK_utilisateurs] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

ALTER TABLE [cosium].[utilisateurs] ADD  CONSTRAINT [DF_utilisateurs_CreatedOn]  DEFAULT (sysutcdatetime()) FOR [CreatedOn]
GO

ALTER TABLE [cosium].[utilisateurs] ADD  CONSTRAINT [DF_utilisateurs_ModifiedOn]  DEFAULT (sysutcdatetime()) FOR [ModifiedOn]
GO
