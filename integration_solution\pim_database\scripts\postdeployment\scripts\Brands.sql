﻿DECLARE @brands TABLE (Id UNIQUEIDENTIFIER, Code NVARCHAR(20), Name NVARCHAR(100))
INSERT INTO @brands (Id, Code, Name) VALUES
('8B334F61-FE4F-4BF2-BB43-06116037CD2C', '0001', '<PERSON>ia'),
('82D9D7CE-F1D5-411B-ADB7-FEBEDFEDB4A6', '0014', 'WIDEX')

 MERGE pim.Brand AS Target
 USING @brands AS Source
    ON Source.Id = Target.Id

  WHEN NOT MATCHED BY Target THEN
INSERT (Id, Code, [Name])
VALUES (Source.Id, Source.Code, Source.[Name])
;