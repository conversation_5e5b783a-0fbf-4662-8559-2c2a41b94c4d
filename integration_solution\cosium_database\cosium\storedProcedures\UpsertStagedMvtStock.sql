﻿CREATE PROCEDURE [cosium].[UpsertStagedMvtStock]
AS

        SET XACT_ABORT ON
        BEGIN TRANSACTION
     
       UPDATE cosium.mvtstock
          SET [commentairemvt] = Source.[commentairemvt],
              [datemvt] = Source.[datemvt],
              [refarticleorigine] = Source.[refarticleorigine],
              [refarticledestination] = Source.[refarticledestination],
              [quantitemvt] = Source.[quantitemvt],
              [libellearticlemvt] = Source.[libellearticlemvt],
              [codeproduitmvt] = Source.[codeproduitmvt],
              [numseriemvt] = Source.[numseriemvt],
              [typemvt] = Source.[typemvt],
              [refcentreori] = Source.[refcentreori],
              [refcentredest] = Source.[refcentredest],
              [datemodif] = Source.[datemodif]

         FROM cosium.mvtstock

              INNER JOIN cosium.staging_mvtstock AS Source
                      ON mvtstock.id = Source.id

        WHERE mvtstock.datemodif <> Source.datemodif;

       INSERT INTO cosium.mvtstock (
              [id],
              [commentairemvt],
              [datemvt],
              [refarticleorigine],
              [refarticledestination],
              [quantitemvt],
              [libellearticlemvt],
              [codeproduitmvt],
              [numseriemvt],
              [typemvt],
              [refcentreori],
              [refcentredest],
              [datemodif]
              )

       SELECT Source.[id],
              Source.[commentairemvt],
              Source.[datemvt],
              Source.[refarticleorigine],
              Source.[refarticledestination],
              Source.[quantitemvt],
              Source.[libellearticlemvt],
              Source.[codeproduitmvt],
              Source.[numseriemvt],
              Source.[typemvt],
              Source.[refcentreori],
              Source.[refcentredest],
              Source.[datemodif]

         FROM cosium.staging_mvtstock AS Source

        WHERE NOT EXISTS (SELECT * FROM cosium.mvtstock WHERE id = Source.id)

     TRUNCATE TABLE cosium.staging_mvtstock
       
       COMMIT TRANSACTION;