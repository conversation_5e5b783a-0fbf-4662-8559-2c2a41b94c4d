﻿CREATE PROCEDURE [pim].[ProductUpsert]
(
@source pim.ProductUpsertTableType READONLY,
-- Add output parameters for diagnostics
@rowsReceived INT = NULL OUTPUT,
@diagnosticMessage NVARCHAR(MAX) = NULL OUTPUT
)
AS

BEGIN
DECLARE @rowCount INT;
DECLARE @initialCount INT;
DECLARE @diagnosticLog NVARCHAR(MAX) = '';
DECLARE @firstMergeAffected INT;
DECLARE @finalCount INT;

-- Populate variables
SELECT @rowCount = COUNT(*) FROM @source;
SELECT @initialCount = COUNT(*) FROM pim.Product;

-- Build diagnostic info
SET @diagnosticLog = 'Received ' + CAST(@rowCount AS VARCHAR(20)) + ' rows in @source parameter' + CHAR(13) + CHAR(10) +
                    'Current pim.Product count: ' + CAST(@initialCount AS VARCHAR(20));
    
BEGIN TRY
SET XACT_ABORT ON
BEGIN TRANSACTION

 MERGE pim.Product AS Target
 USING (SELECT t1.Id,
               t2.[Name],
               t2.ColorId,
               t2.IsAvailable,
               t2.IsPhasedOut,
               t2.ListPrice,
               t2.ProductSource,
               t2.ProductType,
               t2.Ranking,
               t2.ReleaseDate,
               t2.Sku,
               t2.[State],
               t2.CreatedAt,
               t2.UpdatedAt

         FROM (SELECT DISTINCT Id FROM @source) AS t1
        OUTER APPLY (
              SELECT TOP 1
                     s1.[Name],
                     Color.Id AS ColorId,
                     IsAvailable,
                     IsPhasedOut,
                     ListPrice,
                     ProductSource,
                     ProductType,
                     Ranking,
                     ReleaseDate,
                     Sku,
                     [State],
                     CreatedAt,
                     UpdatedAt
                FROM @source AS s1

                LEFT JOIN pim.Color
                  ON s1.ColorCode = pim.Color.Code

               WHERE s1.Id = t1.Id
               ORDER BY UpdatedAt DESC) AS t2
                     
       ) AS Source
    ON Source.Id = Target.Id
    
  WHEN NOT MATCHED BY Target 
  THEN
INSERT (Id,
       [Name],
       ColorId,
       IsAvailable,
       IsPhasedOut,
       ListPrice,
       ProductSource,
       ProductType,
       Ranking,
       ReleaseDate,
       Sku,
       [State],
       CreatedAt,
       UpdatedAt)

VALUES (Source.Id,
       Source.[Name],
       Source.ColorId,
       Source.IsAvailable,
       Source.IsPhasedOut,
       Source.ListPrice,
       Source.ProductSource,
       Source.ProductType,
       Source.Ranking,
       Source.ReleaseDate,
       Source.Sku,
       Source.[State],
       Source.CreatedAt,
       Source.UpdatedAt)

  WHEN MATCHED AND (
       (Target.Name <> Source.Name) OR (Target.Name IS NULL AND Source.Name IS NOT NULL)
    OR (Target.ColorId <> Source.ColorId) OR (Target.ColorId IS NULL AND Source.ColorId IS NOT NULL)
    OR (Target.IsAvailable <> Source.IsAvailable) OR (Target.IsAvailable IS NULL AND Source.IsAvailable IS NOT NULL)
    OR (Target.IsPhasedOut <> Source.IsPhasedOut) OR (Target.IsPhasedOut IS NULL AND Source.IsPhasedOut IS NOT NULL)
    OR (Target.ListPrice <> Source.ListPrice) OR (Target.ListPrice IS NULL AND Source.ListPrice IS NOT NULL)
    OR (Target.ProductSource <> Source.ProductSource) OR (Target.ProductSource IS NULL AND Source.ProductSource IS NOT NULL)
    OR (Target.ProductType <> Source.ProductType) OR (Target.ProductType IS NULL AND Source.ProductType IS NOT NULL)
    OR (Target.Ranking <> Source.Ranking) OR (Target.Ranking IS NULL AND Source.Ranking IS NOT NULL)
    OR (Target.ReleaseDate <> Source.ReleaseDate) OR (Target.ReleaseDate IS NULL AND Source.ReleaseDate IS NOT NULL)
    OR (Target.Sku <> Source.Sku) OR (Target.Sku IS NULL AND Source.Sku IS NOT NULL)
    OR (Target.[State] <> Source.[State]) OR (Target.[State] IS NULL AND Source.[State] IS NOT NULL)
    OR (Target.CreatedAt <> Source.CreatedAt) OR (Target.CreatedAt IS NULL AND Source.CreatedAt IS NOT NULL)
    OR (Target.UpdatedAt <> Source.UpdatedAt) OR (Target.UpdatedAt IS NULL AND Source.UpdatedAt IS NOT NULL))
  
  THEN 
UPDATE 
   SET Name = Source.Name,
       ColorId = Source.ColorId,
       IsAvailable = Source.IsAvailable,
       IsPhasedOut = Source.IsPhasedOut,
       ListPrice = Source.ListPrice,
       ProductSource = Source.ProductSource,
       ProductType = Source.ProductType,
       Ranking = Source.Ranking,
       ReleaseDate = Source.ReleaseDate,
       Sku = Source.Sku,
       [State] = Source.[State],
       CreatedAt = Source.CreatedAt,
       UpdatedAt = Source.UpdatedAt,
       SystemModifiedOn = GETDATE(),
       IntegrationRequired = 1; 

   SET @firstMergeAffected = @@rowCount;
   SET @diagnosticLog = @diagnosticLog + CHAR(13) + CHAR(10) + 'Rows affected by MERGE: ' + CAST(@firstMergeAffected AS VARCHAR(20));


COMMIT TRANSACTION;

-- Final counts
SELECT @finalCount = COUNT(*) FROM pim.Product;
   SET @diagnosticLog = @diagnosticLog + CHAR(13) + CHAR(10) + 'Final pim.Product count: ' + CAST(@finalCount AS VARCHAR(20))

   END TRY
 BEGIN CATCH
    IF @@TRANCOUNT > 0
       ROLLBACK TRANSACTION;
            
       SET @diagnosticLog = @diagnosticLog + CHAR(13) + CHAR(10) + 'Error occurred: ' + ERROR_MESSAGE();
       SET @diagnosticMessage = @diagnosticLog;
       THROW;
   END CATCH
    
   SET @diagnosticMessage = @diagnosticLog;
END