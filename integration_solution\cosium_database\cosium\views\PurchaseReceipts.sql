﻿CREATE VIEW [cosium].[PurchaseReceipts]
AS
SELECT Source.id,
       Source.IntegrationRequired,
       Source.IntegrationDate,
       Source.ModifiedOn,
       t1.JSON

  FROM cosium.facture AS Source

       OUTER APPLY (SELECT 
                   (SELECT CONCAT('PREC-', FORMAT(CAST(facture.id AS INT), '00000000')) AS 'documentNumber',
                           facture.id AS 'externalReference',
                           LEFT(facture.numfacture, 35) AS 'alternateNumber',
                           IIF(p1.id IS NOT NULL, CONCAT('PORD-', FORMAT(CAST(p1.id AS INT), '00000000')), NULL) AS 'purchaseOrder.documentNumber',
                           p1.id AS 'purchaseOrder.externalReference',

                           Vendors.ExternalCode AS 'vendor.externalCode',
                           Vendors.[Name] AS 'vendor.name',

                           Clinics.ExternalCode AS 'clinic.externalCode',
                           Clinics.[Name] AS 'clinic.name',
                           facture.datefacture AS 'documentDate',
                           (SELECT articlefacture.id AS 'sequence',
                                   p2.id AS 'purchaseOrderLine.sequence',
                                   Products.Code AS 'product.code',
                                   Products.ExternalCode AS 'product.externalCode',
                                   Products.[Name] AS 'product.name',
                                   articlefacture.libelle AS 'description',
                                   CAST(IIF(ISNUMERIC(articlefacture.qtearticle) = 1, articlefacture.qtearticle, 0E0) AS DECIMAL) AS 'quantity',
                                   CAST(IIF(ISNUMERIC(articlefacture.prixunitttc) = 1, articlefacture.prixunitttc, 0E0) AS MONEY) AS 'unitPrice',
                                   CAST(CAST(IIF(ISNUMERIC(articlefacture.qtearticle) = 1, articlefacture.qtearticle, 0E0) AS DECIMAL) * 
                                        CAST(IIF(ISNUMERIC(articlefacture.prixunitttc) = 1, articlefacture.prixunitttc, 0E0) AS DECIMAL(18,4)) AS MONEY) AS 'grossAmount',
                                   CAST(IIF(ISNUMERIC(articlefacture.remise) = 1, articlefacture.remise, 0E0) AS MONEY) AS 'discountAmount',
                                   CAST(CAST(IIF(ISNUMERIC(articlefacture.totalarticlettc) = 1, articlefacture.totalarticlettc, 0E0) AS MONEY) / 
                                        (1.00 + (CAST(IIF(ISNUMERIC(articlefacture.tauxtva) = 1, articlefacture.tauxtva, 0E0) AS DECIMAL(18,4))/100)) AS MONEY) AS 'amountExclTax', 
                                   CAST(IIF(ISNUMERIC(articlefacture.totalarticlettc) = 1, articlefacture.totalarticlettc, 0E0) AS DECIMAL(18,4)) - 
                                        CAST(CAST(IIF(ISNUMERIC(articlefacture.totalarticlettc) = 1, articlefacture.totalarticlettc, 0E0) AS DECIMAL(18,4)) / 
                                        (1 + (CAST(IIF(ISNUMERIC(articlefacture.tauxtva) = 1, articlefacture.tauxtva, 0E0) AS DECIMAL(18,4))/100)) AS MONEY) AS 'taxAmount',
                                   CAST(IIF(ISNUMERIC(articlefacture.totalarticlettc) = 1, articlefacture.totalarticlettc, 0E0) AS MONEY) AS 'amountInclTax',
                                   ISNULL(articles.numserie, '') AS 'serialNumber'

                                      FROM cosium.articlefacture

                                           LEFT JOIN cosium.articles
                                                  ON articlefacture.refarticle = articles.id

                                           LEFT JOIN cosium.Products
                                                  ON articlefacture.refproduit = Products.ExternalCode

                                           LEFT JOIN cosium.articlefacture AS p2
                                                   ON articlefacture.supplierorderforminvoiceditem_id = p2.id

                                     WHERE articlefacture.reffacture = facture.id

                                       FOR JSON PATH
                                   ) AS 'purchaseReceiptLines'

                              FROM cosium.facture

                                   LEFT JOIN cosium.Vendors
                                          ON facture.refclient = Vendors.ExternalCode

                                   LEFT JOIN cosium.Clinics
                                          ON facture.centre = Clinics.ExternalCode

                                   LEFT JOIN cosium.facture AS p1
                                          ON p1.id = (SELECT TOP 1 p3.reffacture 
                                                        FROM cosium.articlefacture AS p2
                                                             INNER JOIN cosium.articlefacture AS p3
                                                                     ON p2.supplierorderforminvoiceditem_id = p3.id
                                                       WHERE p2.reffacture = facture.id)

                             WHERE facture.id = Source.id

                               FOR JSON PATH, WITHOUT_ARRAY_WRAPPER

                           ) AS JSON
                   ) AS t1

 WHERE Source.typefacture = 60