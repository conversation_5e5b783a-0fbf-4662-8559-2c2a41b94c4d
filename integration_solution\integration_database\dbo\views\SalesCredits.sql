﻿CREATE VIEW [dbo].[SalesCredits]
AS

SELECT Source.Id,
       Source.DocumentNumber,
       (SELECT TOP 1 ExternalRecordId
          FROM dbo.Coupling
         WHERE Coupling.RecordId = Source.Id AND Coupling.ExternalSystemId = ExternalSystem.Id) AS ExternalReference,
       Source.DocumentDate,
       ExternalSystem.Id AS ExternalSystemId,
       ExternalSystem.Code AS ExternalSystemCode,
       Source.ModifiedOn,
       t1.JSON

  FROM dbo.SalesCredit AS Source

 OUTER APPLY dbo.ExternalSystem

 OUTER APPLY (SELECT(
              SELECT SalesCredit.Id AS id,
                     SalesCredit.DocumentNumber AS documentNumber,
                     (SELECT TOP 1 ExternalRecordId 
                        FROM dbo.Coupling 
                       WHERE Coupling.RecordId = SalesCredit.Id AND Coupling.ExternalSystemId = ExternalSystem.Id) AS 'externalReference',
                     SalesCredit.AlternateNumber AS alternateNumber,

                     SalesOrder.Id AS 'salesOrder.id',
                     SalesOrder.DocumentNumber AS 'salesOrder.documentNumber',
                     (SELECT TOP 1 ExternalRecordId 
                        FROM dbo.Coupling 
                       WHERE Coupling.RecordId = SalesOrder.Id AND Coupling.ExternalSystemId = ExternalSystem.Id) AS 'salesOrder.externalReference',
                     
                     Patient.Id AS 'patient.id',
                     Patient.Code AS 'patient.code',
                     (SELECT TOP 1 ExternalRecordId 
                        FROM dbo.Coupling 
                       WHERE Coupling.RecordId = Patient.Id AND Coupling.ExternalSystemId = ExternalSystem.Id) AS 'patient.externalCode',
                     Patient.[Name] AS 'patient.name',

                     Clinic.Id AS 'clinic.id',
                     Clinic.Code AS 'clinic.code',
                     (SELECT TOP 1 ExternalRecordId 
                        FROM dbo.Coupling 
                       WHERE Coupling.RecordId = Patient.Id AND Coupling.ExternalSystemId = ExternalSystem.Id) AS 'clinic.externalCode',
                     Clinic.[Name] AS 'clinic.name',
                     SalesCredit.DocumentDate AS documentDate,
                     (
                            SELECT SalesCreditLine.[Sequence] AS [sequence],
                                   Product.Id AS 'product.id',
                                   Product.Code AS 'product.code',
                                   (SELECT TOP 1 ExternalRecordId 
                                      FROM dbo.Coupling 
                                      WHERE Coupling.RecordId = Patient.Id AND Coupling.ExternalSystemId = ExternalSystem.Id) AS 'product.externalCode',
                                   Product.[Name] AS 'product.name',
                                   SalesCreditLine.[Description] AS [description],
                                   SalesCreditLine.[Quantity] AS quantity,
                                   SalesCreditLine.[UnitPrice] AS unitPrice,
                                   SalesCreditLine.[GrossAmount] AS grossAmount,
                                   SalesCreditLine.[DiscountAmount] AS discountAmount,
                                   SalesCreditLine.[AmountInclTax] AS amountInclTax,
                                   SalesCreditLine.[TaxAmount] AS taxAmount,
                                   SalesCreditLine.[AmountExclTax] AS amountExclTax,
                                   SalesCreditLine.[SerialNumber] AS serialNumber

                              FROM dbo.SalesCreditLine

                                   LEFT JOIN dbo.Product
                                          ON SalesCreditLine.ProductId = Product.Id

                             WHERE SalesCreditLine.SalesCreditId = SalesCredit.Id
                               AND SalesCreditLine.IsActive = 1

                             ORDER BY SalesCreditLine.[Sequence]

                               FOR JSON PATH
                     ) AS salesCreditLines

                FROM dbo.SalesCredit

                     INNER JOIN dbo.Patient
                             ON SalesCredit.PatientId = Patient.Id

                     INNER JOIN dbo.Clinic
                             ON SalesCredit.ClinicId = Clinic.Id

                      LEFT JOIN dbo.SalesOrder
                             ON SalesCredit.SalesOrderId = SalesOrder.Id

               WHERE SalesCredit.Id = Source.Id

                FOR JSON PATH, WITHOUT_ARRAY_WRAPPER
                ) AS JSON
                ) AS t1

 WHERE Source.IsActive = 1