namespace WSA.Integration.API.V1;

using Microsoft.Integration.Graph;
using Microsoft.Inventory.Item;
using WSA.Integration;


codeunit 50118 "WSA Subcategory Handler" implements "WSA Integration Request"
{
    TableNo = "WSA Integration Request Log";

    trigger OnRun()
    begin
        Code(Rec);
    end;


    procedure HandleRequest(var Request: Record "WSA Integration Request Log")
    begin
        if not Codeunit.Run(Codeunit::"WSA Subcategory Handler", Request) then begin
            Common.SetErrorResponse(Request, '');
        end;
    end;


    local procedure Code(var Request: Record "WSA Integration Request Log")
    var
        json: JsonObject;

    begin
        case Request.Method of
            Request.Method::post:
                HandlePost(Request);
            Request.Method::get:
                HandleGet(Request);
        end;
    end;


    local procedure HandlePost(var Request: Record "WSA Integration Request Log")
    var
        subcategory: Record "Item Category";

    begin
        if TryHandlePost(Request, subcategory) then
            Common.SetCreatedResponse(Request, Common.SubcategoryToJson(subcategory))
        else
            Common.SetErrorResponse(Request, '');
    end;


    local procedure HandleGet(var Request: Record "WSA Integration Request Log")
    var
        subcategory: Record "Item Category";

    begin
        if not TryHandleGet(Request, subcategory) then
            Common.SetErrorResponse(Request, '');
    end;


    [TryFunction]
    local procedure TryHandlePost(
        var Request: Record "WSA Integration Request Log";
        var Subcategory: Record "Item Category")

    var
        recRef: RecordRef;
        json: JsonObject;

    begin
        Request.TestField("Request Content");
        json := Common.GetJsonFromBlob(Request);

        if not Subcategory.Get(Common.GetJsonValue(json, '$.code').AsCode()) then begin
            Subcategory.Init();
            Subcategory.Code := Common.GetJsonValue(json, '$.code').AsCode();
            Subcategory.Insert(false);
        end;

        recRef.GetTable(Subcategory);
        Common.ValidateFieldFromJson(json, '$.name', recRef, Subcategory.FieldNo(Description), TempFieldSet);
        Common.ValidateFieldFromJson(json, '$.parentCategory', recRef, Subcategory.FieldNo("Parent Category"), TempFieldSet);

        GraphMgtGeneralTools.ProcessNewRecordFromAPI(recRef, TempFieldSet, CurrentDateTime());

        recRef.SetTable(Subcategory);
        Subcategory.Modify();
    end;


    [TryFunction]
    local procedure TryHandleGet(
        var Request: Record "WSA Integration Request Log";
        var Subcategory: Record "Item Category")

    var
        recRef: RecordRef;
        json: JsonObject;

    begin
        Request.TestField("Request Content");
        json := Common.GetJsonFromBlob(Request);

        if not (Common.GetJsonValue(json, '$.id').IsNull) then begin
            if Subcategory.GetBySystemId(Common.GetJsonValue(json, '$.id').AsText()) then begin
                Common.SetOkResponse(Request, Common.SubcategoryToJson(Subcategory));
                exit;
            end;
        end;

        if not (Common.GetJsonValue(json, '$.code').IsNull) then begin
            Subcategory.SetRange(Code, Common.GetJsonValue(json, '$.code').AsCode());
            if Subcategory.FindFirst() then begin
                Common.SetOkResponse(Request, Common.SubcategoryToJson(Subcategory));
                exit;
            end
        end;

        if not (Common.GetJsonValue(json, '$.name').IsNull) then begin
            Subcategory.SetRange(Description, Common.GetJsonValue(json, '$.name').AsCode());
            if Subcategory.FindFirst() then begin
                Common.SetOkResponse(Request, Common.SubcategoryToJson(Subcategory));
                exit;
            end
        end;

        Common.SetNoContentResponse(Request);
    end;


    var
        TempFieldSet: Record 2000000041 temporary;
        GraphMgtGeneralTools: Codeunit "Graph Mgt - General Tools";
        Common: Codeunit "WSA Common";
}
