﻿CREATE TABLE [cosium].[client] (
    [id]                    NVARCHAR (50)  NOT NULL,
    [denomination]          NVARCHAR (50)  NULL,
    [nom]                   NVARCHAR (200)  NULL,
    [prenom]                NVARCHAR (100)  NULL,
    [datedenaissance]       NVARCHAR (50)  NULL,
    [secunumero]            NVARCHAR (50)  NULL,
    [secudatedebutdroit]    NVARCHAR (50)  NULL,
    [secudatefindroit]      NVARCHAR (50)  NULL,
    [secutauxremboursement] NVARCHAR (50)  NULL,
    [adresse1numerovoie]    NVARCHAR (50) NULL,
    [adresse1typevoie]      NVARCHAR (50) NULL,
    [adresse1]              NVARCHAR (200)  NULL,
    [adresse1bis]           NVARCHAR (200)  NULL,
    [adresse1codepostal]    NVARCHAR (50)  NULL,
    [adresse1ligne3]        NVARCHAR (200)  NULL,
    [adresse1ligne4]        NVARCHAR (200)  NULL,
    [adresse1ville]         NVARCHAR (50)  NULL,
    [adresse1pays]          NVARCHAR (50)  NULL,
    [denomination2]         NVARCHAR (50)  NULL,
    [nom2]                  NVARCHAR (100)  NULL,
    [prenom2]               NVARCHAR (100)  NULL,
    [adresse2numerovoie]    NVARCHAR (50) NULL,
    [adresse2typevoie]      NVARCHAR (50) NULL,
    [adresse2]              NVARCHAR (100)  NULL,
    [adresse2bis]           NVARCHAR (100)  NULL,
    [adresse2ligne3]        NVARCHAR (200)  NULL,
    [adresse2ligne4]        NVARCHAR (200)  NULL,
    [adresse2codepostal]    NVARCHAR (50)  NULL,
    [adresse2ville]         NVARCHAR (50)  NULL,
    [adresse2pays]          NVARCHAR (50)  NULL,
    [typeadr2]              NVARCHAR (50)  NULL,
    [medecin]               NVARCHAR (50)  NULL,
    [telportable]           NVARCHAR (50)  NULL,
    [telbureau]             NVARCHAR (50)  NULL,
    [teldomicile]           NVARCHAR (50)  NULL,
    [commentteld]           NVARCHAR (MAX) NULL,
    [email]                 NVARCHAR (50)  NULL,
    [datecreation]          NVARCHAR (50)  NULL,
    [centre]                NVARCHAR (50)  NULL,
    [stereo]                NVARCHAR (50)  NULL,
    [sterealisable]         NVARCHAR (50)  NULL,
    [caissesecu]            NVARCHAR (50)  NULL,
    [nomassure]             NVARCHAR (100)  NULL,
    [assurenom]             NVARCHAR (200)  NULL,
    [assureprenom]          NVARCHAR (100)  NULL,
    [assuretitre]           NVARCHAR (50)  NULL,
    [allocationforfaitaire] NVARCHAR (50)  NULL,
    [appareillable]         NVARCHAR (50)  NULL,
    [cache]                 NVARCHAR (50)  NULL,
    [exclus]                NVARCHAR (50)  NULL,
    [reforigine]            NVARCHAR (50)  NULL,
    [article115]            NVARCHAR (50)  NULL,
    [generaliste]           NVARCHAR (50)  NULL,
    [prospect]              NVARCHAR (50)  NULL,
    [audioid]               NVARCHAR (50)  NULL,
    [fax]                   NVARCHAR (50)  NULL,
    [numeroclient]          NVARCHAR (50)  NULL,
    [offrecommercialemail]  NVARCHAR (50)  NULL,
    [offrecommercialesms]   NVARCHAR (50)  NULL,
    [rang]                  NVARCHAR (50)  NULL,
    [datenaissassure]       NVARCHAR (50)  NULL,
    [refvendeur]            NVARCHAR (50)  NULL,
    [prescription]          NVARCHAR (50)  NULL,
    [adresse1npai]          NVARCHAR (50)  NULL,
    [adresse2npai]          NVARCHAR (50)  NULL,
    [datelunaire]           NVARCHAR (50)  NULL,
    [pertesaudioog]         NVARCHAR (50)  NULL,
    [pertesaudiood]         NVARCHAR (50)  NULL,
    [pertesaudiomoyenne]    NVARCHAR (50)  NULL,
    [datemodif]             NVARCHAR (50)  NULL,
    [creator_id]            NVARCHAR (50)  NULL,
    [CreatedOn]             DATETIME2(7) CONSTRAINT [DF_client_CreatedOn] DEFAULT (sysutcdatetime()) NOT NULL,
    [ModifiedOn]            DATETIME2(7) CONSTRAINT [DF_client_ModifiedOn] DEFAULT (sysutcdatetime()) NOT NULL,
    [IntegrationRequired]   BIT DEFAULT 0 NOT NULL,
    [IntegrationDate]       DATETIME2(7) NULL
    CONSTRAINT [PK_client] PRIMARY KEY CLUSTERED ([id] ASC)
);
GO

CREATE INDEX IX_client_numeroclient
ON [cosium].[client] (numeroclient)
GO

CREATE INDEX IX_client_ModifiedOn
ON [cosium].[client] ([ModifiedOn])
GO

CREATE INDEX IX_client_IntegrationRequired
ON [cosium].[client] ([IntegrationRequired], [id])
GO

CREATE TRIGGER [cosium].[client_UpdateModified]
ON [cosium].[client]
AFTER UPDATE 
AS
   UPDATE [cosium].[client]
      SET [ModifiedOn] = sysutcdatetime(),
          [IntegrationRequired] = IIF((d.[IntegrationDate] IS NOT NULL) AND (ISNULL(d.[datemodif], '1900-01-01') < i.[datemodif]), 1, [client].[IntegrationRequired])
     FROM Inserted AS i
          LEFT JOIN deleted AS d
            ON i.id = d.id
    WHERE [client].[id] = i.[id]
GO