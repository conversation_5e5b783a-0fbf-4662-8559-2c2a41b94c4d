namespace WSA.Integration.API.V1;

using Microsoft.Integration.Graph;
using WSA.Integration;


codeunit 50116 "WSA Color Handler" implements "WSA Integration Request"
{
    TableNo = "WSA Integration Request Log";

    trigger OnRun()
    begin
        Code(Rec);
    end;


    procedure HandleRequest(var Request: Record "WSA Integration Request Log")
    begin
        if not Codeunit.Run(Codeunit::"WSA Color Handler", Request) then begin
            Common.SetErrorResponse(Request, '');
        end;
    end;


    local procedure Code(var Request: Record "WSA Integration Request Log")
    var
        json: JsonObject;

    begin
        case Request.Method of
            Request.Method::post:
                HandlePost(Request);
            Request.Method::get:
                HandleGet(Request);
        end;
    end;


    local procedure HandlePost(var Request: Record "WSA Integration Request Log")
    var
        color: Record Color;

    begin
        if TryHandlePost(Request, color) then
            Common.SetCreatedResponse(Request, Common.ColorToJson(color))
        else
            Common.SetErrorResponse(Request, '');
    end;


    local procedure HandleGet(var Request: Record "WSA Integration Request Log")
    var
        color: Record Color;

    begin
        if not TryHandleGet(Request, color) then
            Common.SetErrorResponse(Request, '');
    end;


    [TryFunction]
    local procedure TryHandlePost(
        var Request: Record "WSA Integration Request Log";
        var Color: Record Color)

    var
        recRef: RecordRef;
        json: JsonObject;

    begin
        Request.TestField("Request Content");
        json := Common.GetJsonFromBlob(Request);

        if not Color.Get(Common.GetJsonValue(json, '$.code').AsCode()) then begin
            Color.Init();
            Color.Code := Common.GetJsonValue(json, '$.code').AsCode();
            Color.Insert(false);
        end;

        recRef.GetTable(Color);
        Common.ValidateFieldFromJson(json, '$.name', recRef, Color.FieldNo(Description), TempFieldSet);
        Common.ValidateFieldFromJson(json, '$.hexCode', recRef, Color.FieldNo("Hex Code"), TempFieldSet);

        GraphMgtGeneralTools.ProcessNewRecordFromAPI(recRef, TempFieldSet, CurrentDateTime());

        recRef.SetTable(Color);
        Color.Modify();
    end;


    [TryFunction]
    local procedure TryHandleGet(
        var Request: Record "WSA Integration Request Log";
        var Color: Record Color)

    var
        recRef: RecordRef;
        json: JsonObject;

    begin
        Request.TestField("Request Content");
        json := Common.GetJsonFromBlob(Request);

        if not (Common.GetJsonValue(json, '$.id').IsNull) then begin
            if Color.GetBySystemId(Common.GetJsonValue(json, '$.id').AsText()) then begin
                Common.SetOkResponse(Request, Common.ColorToJson(Color));
                exit;
            end;
        end;

        if not (Common.GetJsonValue(json, '$.code').IsNull) then begin
            Color.SetRange(Code, Common.GetJsonValue(json, '$.code').AsCode());
            if Color.FindFirst() then begin
                Common.SetOkResponse(Request, Common.ColorToJson(Color));
                exit;
            end
        end;

        if not (Common.GetJsonValue(json, '$.name').IsNull) then begin
            Color.SetRange(Description, Common.GetJsonValue(json, '$.name').AsCode());
            if Color.FindFirst() then begin
                Common.SetOkResponse(Request, Common.ColorToJson(Color));
                exit;
            end
        end;

        Common.SetNoContentResponse(Request);
    end;


    var
        TempFieldSet: Record 2000000041 temporary;
        GraphMgtGeneralTools: Codeunit "Graph Mgt - General Tools";
        Common: Codeunit "WSA Common";
}
