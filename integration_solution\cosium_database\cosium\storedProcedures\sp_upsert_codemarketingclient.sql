﻿CREATE PROCEDURE [cosium].[sp_upsert_codemarketingclient]
AS
BEGIN
    SET NOCOUNT ON;

    -- Step 1: Deduplicate staging table using (id, refclient, codemailing) as the unique key
    SELECT 
        id,
        refclient,
        codemailing,
        datemodif
    INTO #DedupedStaging
    FROM (
        SELECT *,
            ROW_NUMBER() OVER (
                PARTITION BY id, refclient, codemailing
                ORDER BY 
                    CASE WHEN datemodif IS NULL THEN 0 ELSE 1 END DESC,
                    datemodif DESC
            ) AS rn
        FROM [cosium].[stg_codemarketingclient]
    ) ranked
    WHERE rn = 1;

    -- Step 2: Perform UPSERT logic using MERGE
    BEGIN TRY
        BEGIN TRANSACTION;

        MERGE [cosium].[codemarketingclient] AS target
        USING #DedupedStaging AS source
        ON target.[id] = source.[id]
           AND target.[refclient] = source.[refclient]
           AND target.[codemailing] = source.[codemailing]

        WHEN MATCHED AND (
            ISNULL(target.[datemodif], '') <> ISNULL(source.[datemodif], '')
        )
        THEN UPDATE SET
            target.[datemodif] = source.[datemodif],
            target.[ModifiedOn] = SYSUTCDATETIME()

        WHEN NOT MATCHED BY TARGET
        THEN INSERT (
            [id], 
            [refclient], 
            [codemailing], 
            [datemodif], 
            [CreatedOn], 
            [ModifiedOn],
            [guid_id]
        )
        VALUES (
            source.[id], 
            source.[refclient], 
            source.[codemailing], 
            source.[datemodif], 
            SYSUTCDATETIME(), 
            SYSUTCDATETIME(),
            NEWID()
        );

        COMMIT TRANSACTION;
    END TRY
    BEGIN CATCH
        IF @@TRANCOUNT > 0
            ROLLBACK TRANSACTION;

        DECLARE @ErrorMessage NVARCHAR(4000) = ERROR_MESSAGE();
        DECLARE @ErrorSeverity INT = ERROR_SEVERITY();
        DECLARE @ErrorState INT = ERROR_STATE();

        RAISERROR(@ErrorMessage, @ErrorSeverity, @ErrorState);
        RETURN;
    END CATCH

    DROP TABLE #DedupedStaging;
END
GO