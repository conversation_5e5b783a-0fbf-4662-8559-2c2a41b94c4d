﻿CREATE TABLE [cosium].[famillerdv] (
    [id]               NVARCHAR (50) NOT NULL,
    [nom<PERSON><PERSON><PERSON>]       NVARCHAR (50) NULL,
    [datemodif]        NVARCHAR (50) NULL,
    [CreatedOn]        DATETIME2 CONSTRAINT [DF_famillerdv_CreatedOn] DEFAULT (sysutcdatetime()) NOT NULL,
    [ModifiedOn]       DATETIME2 CONSTRAINT [DF_famillerdv_ModifiedOn] DEFAULT (sysutcdatetime()) NOT NULL,
    CONSTRAINT [PK_famillerdv] PRIMARY KEY CLUSTERED ([id] ASC)
);
GO

CREATE TRIGGER [cosium].[famillerdv_UpdateModified]
ON [cosium].[famillerdv]
AFTER UPDATE 
AS
   UPDATE [cosium].[famillerdv]
   SET [ModifiedOn] = sysutcdatetime()
   FROM Inserted AS i
   WHERE [cosium].[famillerdv].[id] = i.[id]
GO