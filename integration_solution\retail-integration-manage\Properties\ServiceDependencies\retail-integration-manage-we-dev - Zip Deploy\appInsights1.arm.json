{"$schema": "https://schema.management.azure.com/schemas/2018-05-01/subscriptionDeploymentTemplate.json#", "contentVersion": "*******", "parameters": {"resourceGroupName": {"type": "string", "defaultValue": "wsa-we-data-Biz_Central-rg", "metadata": {"_parameterType": "resourceGroup", "description": "Name of the resource group for the resource. It is recommended to put resources under same resource group for better tracking."}}, "resourceGroupLocation": {"type": "string", "defaultValue": "westeurope", "metadata": {"_parameterType": "location", "description": "Location of the resource group. Resource groups could have different location than resources."}}, "resourceLocation": {"type": "string", "defaultValue": "[parameters('resourceGroupLocation')]", "metadata": {"_parameterType": "location", "description": "Location of the resource. By default use resource group's location, unless the resource provider is not supported there."}}}, "resources": [{"type": "Microsoft.Resources/resourceGroups", "name": "[parameters('resourceGroupName')]", "location": "[parameters('resourceGroupLocation')]", "apiVersion": "2019-10-01"}, {"type": "Microsoft.Resources/deployments", "name": "[concat(parameters('resourceGroupName'), 'Deployment', uniqueString(concat('retail-integration-we-dev', subscription().subscriptionId)))]", "resourceGroup": "[parameters('resourceGroupName')]", "apiVersion": "2019-10-01", "dependsOn": ["[parameters('resourceGroupName')]"], "properties": {"mode": "Incremental", "template": {"$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#", "contentVersion": "*******", "resources": [{"name": "retail-integration-we-dev", "type": "microsoft.insights/components", "location": "[parameters('resourceLocation')]", "kind": "web", "properties": {}, "apiVersion": "2015-05-01"}]}}}], "metadata": {"_dependencyType": "appInsights.azure"}}