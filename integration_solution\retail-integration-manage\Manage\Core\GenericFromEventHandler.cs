﻿using Microsoft.Extensions.Options;
using System.Text.Json;
using WSA.Retail.Integration.Core;
using WSA.Retail.Integration.Manage.Configuration;
using WSA.Retail.Integration.Manage.EventProcessing;
using WSA.Retail.Integration.Models.Configuration;
using WSA.Retail.Integration.Models.Couplings;
using WSA.Retail.Integration.Utilities;

namespace WSA.Retail.Integration.Manage.Core;

public abstract class GenericFromEventHandler<
    TEventHubEntity, 
    TDomainModelService,
    TEntity,
    TEntitySubscriberService>(
        IOptions<AppSettings> appSettings,
        TDomainModelService domainModelService,
        IEntitySubscriberService entitySubscriberService,
        ICouplingService couplingService,
        IEventHubEntityAdapter<TEventHubEntity, TEntity> eventHubEntityAdapter,
        EntityType entityType)
    where TEventHubEntity : class
    where TDomainModelService : IMasterDataServiceBase<TEntity>
    where TEntity : IMasterData
    where TEntitySubscriberService : class
{
    protected readonly AppSettings _appSettings = appSettings.Value;
    protected readonly TDomainModelService _domainModelService = domainModelService;
    protected readonly IEntitySubscriberService _entitySubscriberService = entitySubscriberService;
    protected readonly ICouplingService _couplingService = couplingService;
    protected readonly IEventHubEntityAdapter<TEventHubEntity, TEntity> _eventHubEntityAdapter = eventHubEntityAdapter;
    protected readonly EntityType _entityType = entityType;

    public async Task<bool> HandleFromQueueAsync(EventHubMessage message)
    {
        LogMethodStart();

        var msg = message.Message?.ToString();
        if (msg != null)
        {
            var thisEvent = JsonSerializer.Deserialize<TEventHubEntity>(msg, Common.GetJsonOptions());
            if (thisEvent == null) return false;

            var thisId = GetEventEntityId(thisEvent);
            var thisName = GetEventEntityName(thisEvent);

            if (thisId == Guid.Empty)
                throw new ArgumentOutOfRangeException($"Id {thisId} for {typeof(TEventHubEntity).Name} {thisName} is not valid.");

            TEntity? domainEntity = ConvertToEntity(thisEvent);
            if (domainEntity == null) return false;

            await AfterConvertToEntity(domainEntity);

            LogCustomInformation($"Looking up {typeof(EntitySubscriber).Name}: {_appSettings.ExternalSystemCode}, {_entityType.GetEntityCode()}.");
            var subscriber = await _entitySubscriberService.GetAsync(_appSettings.ExternalSystemCode, _entityType.GetEntityCode());
            if (subscriber == null)
            {
                LogCustomError($"Unable to retrieve Entity Subscriber record for ExternalSystemCode:{_appSettings.ExternalSystemCode}, "
                    + $"EntityCode:{_entityType.GetEntityCode()}");
                return false;
            }

            if (subscriber.FromExternalSystem ?? false) // Manage can send updates - full upsert
            {
                LogCustomInformation($"{_appSettings.ExternalSystemCode} is allowed to send updates for {typeof(TEntity).Name}.");
                try
                {
                    LogCustomInformation($"Looking up existing {typeof(TEntity).Name} from middle layer");

                    TEntity? existingDomainEntity = default;
                    if (existingDomainEntity == null && domainEntity.ExternalCode != null)
                    {
                        existingDomainEntity = await _domainModelService.GetAsync(
                            _appSettings.ExternalSystemCode, externalCode: domainEntity.ExternalCode);
                        if (existingDomainEntity != null)
                        {
                            domainEntity.Id = existingDomainEntity.Id;
                            domainEntity.Code = existingDomainEntity.Code;
                            LogCustomInformation($"{typeof(TEntity).Name} {nameof(domainEntity.ExternalCode)}: {domainEntity.ExternalCode} exists as " +
                                $"{nameof(domainEntity.Id)}: {existingDomainEntity.Id}, " +
                                $"{nameof(domainEntity.Code)}: {existingDomainEntity.Code}.");
                        }
                    }

                    if (existingDomainEntity == null && domainEntity.Code != null)
                    {
                        existingDomainEntity = await _domainModelService.GetAsync(
                            _appSettings.ExternalSystemCode, code: domainEntity.Code);
                        if (existingDomainEntity != null)
                        {
                            domainEntity.Id = existingDomainEntity.Id;
                            LogCustomInformation($"{typeof(TEntity).Name} {nameof(domainEntity.Code)}: {domainEntity.Code} exists as " +
                                $"{nameof(domainEntity.Id)}: {existingDomainEntity.Id}, " +
                                $"{nameof(domainEntity.Code)}: {existingDomainEntity.Code}.");
                        }
                    }

                    LogCustomInformation($"Sending {typeof(TEntity).Name}: {domainEntity.Code} to middle layer");
                    var responseObject = await _domainModelService.UpsertAsync(domainEntity);
                    if (responseObject != null)
                    {
                        LogCustomInformation($"Successfully sent {typeof(TEntity).Name}: {domainEntity.Code} to middle layer");
                    }
                    return responseObject != null;
                }
                catch (Exception ex)
                {
                    LogCustomError(ex);
                    return false;
                }
            }

            if (!subscriber.FromExternalSystem ?? false) // Manage can't send updates - couple only
            {
                LogCustomInformation($"{_appSettings.ExternalSystemCode} is not allowed to send updates for {typeof(TEntity).Name}.");

                await BeforeCoupling(domainEntity, out bool isHandled);
                if (isHandled)
                {
                    LogCustomInformation($"Coupling for {typeof(TEntity).Name} was marked handled.");
                    return true;
                }
                else
                {
                    if (!string.IsNullOrWhiteSpace(domainEntity.Code))
                    {
                        var entityByCode = await _domainModelService.GetAsync(
                            _appSettings.ExternalSystemCode,
                            code: domainEntity.Code);

                        if (entityByCode != null)
                        {
                            if (entityByCode.Id != Guid.Empty && !string.IsNullOrWhiteSpace(entityByCode.ExternalCode))
                            {
                                var coupling = new Coupling()
                                {
                                    ExternalSystem = new() { Code = _appSettings.ExternalSystemCode },
                                    Entity = new() { Code = _entityType.GetEntityCode() },
                                    RecordId = entityByCode.Id,
                                    ExternalCode = entityByCode.ExternalCode
                                };
                                LogCustomInformation($"Coupling {typeof(TEntity).Name}: {domainEntity.Code} to middle layer");
                                await _couplingService.UpsertAsync(coupling);
                                LogCustomInformation($"Successfully couplied {typeof(TEntity).Name}: {domainEntity.Code} to middle layer");
                                return true;
                            }
                        }
                    }
                }
            }
        }

        return false;
    }

    protected virtual void LogMethodStart() { }
    protected virtual void LogCustomInformation(string message) { }
    protected virtual void LogCustomWarning(string message) { }
    protected virtual void LogCustomError(Exception ex, string message) { }
    protected virtual void LogCustomError(Exception ex) { }
    protected virtual void LogCustomError(string message) { }


    protected abstract Guid GetEventEntityId(TEventHubEntity entity);

    protected abstract string? GetEventEntityName(TEventHubEntity entity);
    
    protected virtual TEntity? ConvertToEntity(TEventHubEntity entity)
    {
        return _eventHubEntityAdapter.ToDomainModel(entity, _appSettings.ExternalSystemCode);
    }

    protected virtual Task AfterConvertToEntity(TEntity entity)
    {
        return Task.CompletedTask;
    }

    protected virtual Task BeforeCoupling(TEntity entity, out bool IsHandled)
    {
        IsHandled = false;
        return Task.CompletedTask;
    }
}