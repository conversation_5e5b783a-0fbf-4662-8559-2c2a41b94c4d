﻿using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using WSA.Retail.Integration.Logging;
using WSA.Retail.Integration.Manage.API;
using WSA.Retail.Integration.Manage.Configuration;
using WSA.Retail.Integration.Models.Configuration;
using WSA.Retail.Integration.Models.Products;
using WSA.Retail.Integration.Manage.Models.CategoryMappings;
using WSA.Retail.Integration.Manage.Core;
using WSA.Retail.Integration.Models.Categories;
using WSA.Retail.Integration.Models.Colors;
using WSA.Retail.Integration.Manage.Models.Colors;
using System.Runtime.CompilerServices;
using WSA.Retail.Integration.Models.Couplings;

namespace WSA.Retail.Integration.Manage.Models.Products;

public class ProductManageIntegrator(
    IOptions<AppSettings> appSettings,
    ILogger<ProductManageIntegrator> logger,
    ManageAPI manageApi,
    IProductService productService,
    IEntitySubscriberService entitySubscriberService,
    IManageRequestAdapter<ProductRequest, Product> requestAdapter,
    IManageResponseAdapter<ProductResponse, Product> responseAdapter,
    ICategoryMappingService categoryMappingService,
    ICategoryService categoryService,
    IColorService colorService,
    IColorManageIntegrator colorManageIntegrator) :
    GenericManageIntegrator<
        IProductService,
        Product,
        ProductRequest,
        ProductResponse,
        ProductListResponsePagingResponse,
        IEntitySubscriberService>(
            appSettings,
            productService,
            requestAdapter,
            responseAdapter,
            entitySubscriberService,
            manageApi,
            EntityType.Product),
    IProductManageIntegrator
{
    private readonly ILogger<ProductManageIntegrator> _logger = logger;
    private readonly ICategoryMappingService _categoryMappingService = categoryMappingService;
    private readonly ICategoryService _categoryService = categoryService;
    private readonly IColorService _colorService = colorService;
    private readonly IColorManageIntegrator _colorManageIntegrator = colorManageIntegrator;
    private readonly IProductService _productService = productService;

    protected override async Task<ProductResponse?> AfterGetManageEntity(ProductResponse? existingManageEntity, Product product)
    {
        // Manage will throw an error if the product name already exists, so if not coupled, do a quick
        // check to manage by name.

        if (existingManageEntity != null) return existingManageEntity;

        var response = await _manageApi.SearchAsync(
            searchText: product.Name,
            categoryId: null,
            categoryCode: null,
            manufacturerId: null,
            isActive: null,
            isSerialized: null,
            isNhs: null,
            page: 1,
            perPage: 100);

        if (response == null) return existingManageEntity;

        var responseList = response.Data;

        var productByName = responseList.Where(x => x.Name == product.Name).FirstOrDefault();
        if (productByName?.Id != null)
        {
            var newProductResponse = await _manageApi.ProductsGET2Async(productByName.Id);
            if (newProductResponse != null)
            {
                existingManageEntity = newProductResponse;

                product.ExternalSystemCode = _appSettings.ExternalSystemCode;
                product.ExternalCode = newProductResponse.Id.ToString();
                await _productService.CoupleAsync(product);
            }
        }

        return existingManageEntity;
    }

    protected override async Task AfterConvertDomainEntityToPostRequest(
        ProductRequest request,
        Product product)
    {
        await UpdateRequestAfterConvertDomainEntity(request, product);
    }

    protected override async Task AfterConvertDomainEntityToPutRequest(
        ProductRequest request,
        ProductResponse existingManageEntity,
        Product product)
    {
        await UpdateRequestAfterConvertDomainEntity(request, product);
        request.Description = existingManageEntity.Description;
        request.Warranty = existingManageEntity.Warranty;
        request.LdWarranty = existingManageEntity.LdWarranty;
        request.Quantity = existingManageEntity.Quantity;
        request.RetailPrice = existingManageEntity.RetailPrice;
        request.MaximumDiscount = existingManageEntity.MaximumDiscount;
        request.Cost = existingManageEntity.Cost;
        request.CategoryId = existingManageEntity.Category.Id;
        request.SuggestedProductIds = existingManageEntity.SuggestedProductIds;
    }

    private async Task UpdateRequestAfterConvertDomainEntity(ProductRequest request, Product product)
    {
        // Manufacturer is required in Manange.  Add default if null.
        if (request.ManufacturerId == null || request.ManufacturerId == Guid.Empty)
        {
            if (Guid.TryParse(_appSettings.DefaultManufacturer, out var manufacturerId))
            {
                request.ManufacturerId = manufacturerId;
            }
        }

        // These arrays cannot be null in Manage.
        request.Colors ??= [];
        request.BatteryTypes ??= [];
        request.Attributes ??= [];
        request.SuggestedProductIds ??= [];

        await HandleHearingAidType(request, product);
        await HandleRequestColor(request, product);
    }

    private async Task HandleHearingAidType(ProductRequest request, Product product)
    {
        ArgumentNullException.ThrowIfNull(product.Category);

        var category = await _categoryService.GetAsync(
            externalSystemCode: _appSettings.ExternalSystemCode,
            code: product.Category.Code);

        ArgumentNullException.ThrowIfNull(category);
        if (category.IsHearingAid ?? false)
        {
            var categoryMapping = await _categoryMappingService.GetAsync(product.Category?.Id ?? Guid.Empty);
            if (categoryMapping != null)
            {
                request.HearingAidTypeId = categoryMapping.HearingAidTypeId;
            }
        }
    }

    private async Task HandleRequestColor(ProductRequest request, Product product)
    {
        ArgumentNullException.ThrowIfNull(product.Category);

        var category = await _categoryService.GetAsync(
            externalSystemCode: _appSettings.ExternalSystemCode,
            code: product.Category.Code);

        ArgumentNullException.ThrowIfNull(category);
        if (category.IsHearingAid ?? false)
        {
            if (product.Color != null)
            {
                if (product.Color.ExternalCode == null)
                {
                    var color = await _colorService.GetAsync(
                        externalSystemCode: _appSettings.ExternalSystemCode,
                        code: product.Color.Code);

                    if (color != null)
                    {
                        var updatedColor = await _colorManageIntegrator.UpdateManageAsync(color);
                        if (updatedColor?.ExternalCode != null)
                        {
                            product.Color.ExternalCode = updatedColor.ExternalCode;
                        }
                    }
                }

                if (Guid.TryParse(product.Color.ExternalCode, out var colorId))
                {
                    if (!request.Colors.Contains(colorId))
                    {
                        request.Colors.Add(colorId);
                    }
                }
            }
        }

    }


    protected override void LogMethodStart([CallerMemberName] string? callingMethod = null)
    {
        _logger.LogMethodStart(
            methodName: callingMethod!);
    }
    protected override void LogCustomInformation(string message, [CallerMemberName] string? callingMethod = null)
    {
        _logger.LogCustomInformation(
            message: message,
            methodName: callingMethod!);
    }
    protected override void LogCustomWarning(string message, [CallerMemberName] string? callingMethod = null)
    {
        _logger.LogCustomWarning(
            message: message,
            methodName: callingMethod!);
    }
    protected override void LogCustomError(Exception ex, string message, [CallerMemberName] string? callingMethod = null)
    {
        _logger.LogCustomError(
            ex: ex,
            methodName: callingMethod!);
    }
    protected override void LogCustomError(Exception ex, [CallerMemberName] string? callingMethod = null)
    {
        _logger.LogCustomError(
            ex: ex,
            methodName: callingMethod!);
    }



    protected override async Task<ProductResponse?> GetEntity(Guid id)
    {
        ArgumentNullException.ThrowIfNull(_manageApi);
        return await _manageApi.ProductsGET2Async(id);
    }

    protected override async Task<ProductListResponsePagingResponse?> GetEntityList()
    {
        ArgumentNullException.ThrowIfNull(_manageApi);
        return await _manageApi.ProductsGETAsync(1, 1000);
    }

    protected override async Task<CreatedResponse?> PostEntity(ProductRequest request)
    {
        ArgumentNullException.ThrowIfNull(_manageApi);
        return await _manageApi.ProductsPOSTAsync(request);
    }

    protected override async Task PutEntity(Guid id, ProductRequest request)
    {
        ArgumentNullException.ThrowIfNull(_manageApi);
        await _manageApi.ProductsPUTAsync(id, request);
    }

    protected override ICollection<ProductResponse> GetEntitiesFromEntityList(ProductListResponsePagingResponse responseList)
    {
        //return responseList.Data;
        return [];
    }
}
