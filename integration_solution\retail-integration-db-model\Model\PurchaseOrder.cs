﻿using System.Text.Json.Serialization;

namespace WSA.Retail.Integration.Model;

public class PurchaseOrder
{
    [JsonPropertyName("id")]
    public Guid? Id { get; set; }

    [JsonPropertyName("externalSystemCode")]
    public string? ExternalSystemCode { get; set; }

    [JsonPropertyName("documentNumber")]
    public string? DocumentNumber { get; set; }

    [JsonPropertyName("externalReference")]
    public string? ExternalReference { get; set; }

    [JsonPropertyName("alternateNumber")]
    public string? AlternateNumber { get; set; }

    [JsonPropertyName("vendor")]
    public ExternalReference? Vendor { get; set; }

    [JsonPropertyName("clinic")]
    public ExternalReference? Clinic { get; set; }

    [JsonPropertyName("documentDate")]
    public DateOnly? DocumentDate { get; set; }

    [JsonPropertyName("purchaseOrderLines")]
    public List<PurchaseOrderLine?> PurchaseOrderLines { get; set; }


    public PurchaseOrder() 
    {
        PurchaseOrderLines = [];
    }
}
