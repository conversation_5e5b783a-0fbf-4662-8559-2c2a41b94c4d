﻿using Microsoft.ApplicationInsights;
using Microsoft.ApplicationInsights.DataContracts;
using System.Text.Json;
using WSA.Retail.Integration.Manage.EventProcessing;
using WSA.Retail.Integration.Utilities;

namespace WSA.Retail.Integration.Manage;

public static class TelemetryExtensions
{
    public static void TrackEventHubEvent(
        this TelemetryClient telemetryClient,
        EventHubMessage message)
    {
        var eventTelemetry = new EventTelemetry(message.Event);
        eventTelemetry.Properties.Add("MessageId", message.MessageId.ToString());
        eventTelemetry.Properties.Add("Event", message.Event);
        eventTelemetry.Properties.Add("Version", message.Version);
        eventTelemetry.Properties.Add("Domain", message.Domain);
        eventTelemetry.Properties.Add("Message", JsonSerializer.Serialize(message.Message, Common.GetJsonOptions()));
        telemetryClient.TrackEvent(eventTelemetry);
    }
}
