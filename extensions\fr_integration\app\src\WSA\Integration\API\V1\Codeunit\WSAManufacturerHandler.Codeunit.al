namespace WSA.Integration.API.V1;

using Microsoft.Integration.Graph;
using Microsoft.Inventory.Item.Catalog;
using WSA.Integration;


codeunit 50115 "WSA Manufacturer Handler" implements "WSA Integration Request"
{
    TableNo = "WSA Integration Request Log";

    trigger OnRun()
    begin
        Code(Rec);
    end;


    procedure HandleRequest(var Request: Record "WSA Integration Request Log")
    begin
        if not Codeunit.Run(Codeunit::"WSA Manufacturer Handler", Request) then begin
            Common.SetErrorResponse(Request, '');
        end;
    end;


    local procedure Code(var Request: Record "WSA Integration Request Log")
    var
        json: JsonObject;

    begin
        case Request.Method of
            Request.Method::post:
                HandlePost(Request);
            Request.Method::get:
                HandleGet(Request);
        end;
    end;


    local procedure HandlePost(var Request: Record "WSA Integration Request Log")
    var
        manufacturer: Record Manufacturer;

    begin
        if TryHandlePost(Request, manufacturer) then
            Common.SetCreatedResponse(Request, Common.ManufacturerToJson(manufacturer))
        else
            Common.SetErrorResponse(Request, '');
    end;


    local procedure HandleGet(var Request: Record "WSA Integration Request Log")
    var
        manufacturer: Record Manufacturer;

    begin
        if not TryHandleGet(Request, manufacturer) then
            Common.SetErrorResponse(Request, '');
    end;


    [TryFunction]
    local procedure TryHandlePost(
        var Request: Record "WSA Integration Request Log";
        var Manufacturer: Record Manufacturer)

    var
        recRef: RecordRef;
        json: JsonObject;

    begin
        Request.TestField("Request Content");
        json := Common.GetJsonFromBlob(Request);

        if not Manufacturer.Get(Common.GetJsonValue(json, '$.code').AsCode()) then begin
            Manufacturer.Init();
            Manufacturer.Code := Common.GetJsonValue(json, '$.code').AsCode();
            Manufacturer.Insert(false);
        end;

        recRef.GetTable(Manufacturer);
        Common.ValidateFieldFromJson(json, '$.name', recRef, Manufacturer.FieldNo(Name), TempFieldSet);
        Common.ValidateFieldFromJson(json, '$.address', recRef, Manufacturer.FieldNo(Address), TempFieldSet);
        Common.ValidateFieldFromJson(json, '$.address2', recRef, Manufacturer.FieldNo("Address 2"), TempFieldSet);
        Common.ValidateFieldFromJson(json, '$.country', recRef, Manufacturer.FieldNo("Country/Region Code"), TempFieldSet);
        Common.ValidateFieldFromJson(json, '$.region', recRef, Manufacturer.FieldNo(County), TempFieldSet);
        Common.ValidateFieldFromJson(json, '$.postalCode', recRef, Manufacturer.FieldNo("Post Code"), TempFieldSet);
        Common.ValidateFieldFromJson(json, '$.city', recRef, Manufacturer.FieldNo(City), TempFieldSet);
        Common.ValidateFieldFromJson(json, '$.phone', recRef, Manufacturer.FieldNo("Phone No."), TempFieldSet);
        Common.ValidateFieldFromJson(json, '$.email', recRef, Manufacturer.FieldNo("E-Mail"), TempFieldSet);

        GraphMgtGeneralTools.ProcessNewRecordFromAPI(recRef, TempFieldSet, CurrentDateTime());

        recRef.SetTable(Manufacturer);
        Manufacturer.Modify();
    end;


    [TryFunction]
    local procedure TryHandleGet(
        var Request: Record "WSA Integration Request Log";
        var Manufacturer: Record Manufacturer)

    var
        recRef: RecordRef;
        json: JsonObject;

    begin
        Request.TestField("Request Content");
        json := Common.GetJsonFromBlob(Request);

        if not (Common.GetJsonValue(json, '$.id').IsNull) then begin
            if Manufacturer.GetBySystemId(Common.GetJsonValue(json, '$.id').AsText()) then begin
                Common.SetOkResponse(Request, Common.ManufacturerToJson(Manufacturer));
                exit;
            end;
        end;

        if not (Common.GetJsonValue(json, '$.code').IsNull) then begin
            Manufacturer.SetRange(Code, Common.GetJsonValue(json, '$.code').AsCode());
            if Manufacturer.FindFirst() then begin
                Common.SetOkResponse(Request, Common.ManufacturerToJson(Manufacturer));
                exit;
            end
        end;

        if not (Common.GetJsonValue(json, '$.name').IsNull) then begin
            Manufacturer.SetRange(Name, Common.GetJsonValue(json, '$.name').AsCode());
            if Manufacturer.FindFirst() then begin
                Common.SetOkResponse(Request, Common.ManufacturerToJson(Manufacturer));
                exit;
            end
        end;

        Common.SetNoContentResponse(Request);
    end;


    var
        TempFieldSet: Record 2000000041 temporary;
        GraphMgtGeneralTools: Codeunit "Graph Mgt - General Tools";
        Common: Codeunit "WSA Common";
}
