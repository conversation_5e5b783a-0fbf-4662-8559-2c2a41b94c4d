﻿<?xml version="1.0" encoding="utf-8"?>
<Report xmlns="http://schemas.microsoft.com/sqlserver/reporting/2016/01/reportdefinition" xmlns:rd="http://schemas.microsoft.com/SQLServer/reporting/reportdesigner">
  <AutoRefresh>0</AutoRefresh>
  <DataSources>
    <DataSource Name="DataSource">
      <ConnectionProperties>
        <DataProvider>SQL</DataProvider>
        <ConnectString />
      </ConnectionProperties>
      <rd:SecurityType>None</rd:SecurityType>
    </DataSource>
  </DataSources>
  <ReportSections>
    <ReportSection>
      <Body>
        <Height>2in</Height>
        <Style />
      </Body>
      <Width>6.5in</Width>
      <Page>
        <Style />
      </Page>
    </ReportSection>
  </ReportSections>
  <Code>Public Function BlankZero(ByVal Value As Decimal)
    if Value = 0 then
        Return ""
    end if
    Return Value
End Function

Public Function BlankPos(ByVal Value As Decimal)
    if Value &gt; 0 then
        Return ""
    end if
    Return Value
End Function

Public Function BlankZeroAndPos(ByVal Value As Decimal)
    if Value &gt;= 0 then
        Return ""
    end if
    Return Value
End Function

Public Function BlankNeg(ByVal Value As Decimal)
    if Value &lt; 0 then
        Return ""
    end if
    Return Value
End Function

Public Function BlankNegAndZero(ByVal Value As Decimal)
    if Value &lt;= 0 then
        Return ""
    end if
    Return Value
End Function
</Code>
  <Language>=User!Language</Language>
  <ConsumeContainerWhitespace>true</ConsumeContainerWhitespace>
  <rd:ReportUnitType>Inch</rd:ReportUnitType>
  <rd:ReportID>0eeb6585-38ae-40f1-885b-8d50088d51b4</rd:ReportID>
  <DataSets>
    <DataSet Name="DataSet_Result">
      <Fields>
        <Field Name="FORMAT_TODAY_0_4_">
          <DataField>FORMAT_TODAY_0_4_</DataField>
        </Field>
        <Field Name="COMPANYNAME">
          <DataField>COMPANYNAME</DataField>
        </Field>
        <Field Name="STRSUBSTNO_Text003_USERID_">
          <DataField>STRSUBSTNO_Text003_USERID_</DataField>
        </Field>
        <Field Name="STRSUBSTNO_Text004_PreviousStartDate_">
          <DataField>STRSUBSTNO_Text004_PreviousStartDate_</DataField>
        </Field>
        <Field Name="PageCaption">
          <DataField>PageCaption</DataField>
        </Field>
        <Field Name="UserCaption">
          <DataField>UserCaption</DataField>
        </Field>
        <Field Name="Customer_TABLECAPTION__________Filter">
          <DataField>Customer_TABLECAPTION__________Filter</DataField>
        </Field>
        <Field Name="Filter">
          <DataField>Filter</DataField>
        </Field>
        <Field Name="Customer__No__">
          <DataField>Customer__No__</DataField>
        </Field>
        <Field Name="Customer_Name">
          <DataField>Customer_Name</DataField>
        </Field>
        <Field Name="PreviousDebitAmountLCY_PreviousCreditAmountLCY">
          <DataField>PreviousDebitAmountLCY_PreviousCreditAmountLCY</DataField>
        </Field>
        <Field Name="PreviousDebitAmountLCY_PreviousCreditAmountLCYFormat">
          <DataField>PreviousDebitAmountLCY_PreviousCreditAmountLCYFormat</DataField>
        </Field>
        <Field Name="PreviousCreditAmountLCY_PreviousDebitAmountLCY">
          <DataField>PreviousCreditAmountLCY_PreviousDebitAmountLCY</DataField>
        </Field>
        <Field Name="PreviousCreditAmountLCY_PreviousDebitAmountLCYFormat">
          <DataField>PreviousCreditAmountLCY_PreviousDebitAmountLCYFormat</DataField>
        </Field>
        <Field Name="PeriodDebitAmountLCY">
          <DataField>PeriodDebitAmountLCY</DataField>
        </Field>
        <Field Name="PeriodDebitAmountLCYFormat">
          <DataField>PeriodDebitAmountLCYFormat</DataField>
        </Field>
        <Field Name="PeriodCreditAmountLCY">
          <DataField>PeriodCreditAmountLCY</DataField>
        </Field>
        <Field Name="PeriodCreditAmountLCYFormat">
          <DataField>PeriodCreditAmountLCYFormat</DataField>
        </Field>
        <Field Name="PreviousDebitAmountLCY_PeriodDebitAmountLCY___PreviousCreditAmountLCY_PeriodCreditAmountLCY_">
          <DataField>PreviousDebitAmountLCY_PeriodDebitAmountLCY___PreviousCreditAmountLCY_PeriodCreditAmountLCY_</DataField>
        </Field>
        <Field Name="PreviousDebitAmountLCY_PeriodDebitAmountLCY___PreviousCreditAmountLCY_PeriodCreditAmountLCY_Format">
          <DataField>PreviousDebitAmountLCY_PeriodDebitAmountLCY___PreviousCreditAmountLCY_PeriodCreditAmountLCY_Format</DataField>
        </Field>
        <Field Name="PreviousCreditAmountLCY_PeriodCreditAmountLCY___PreviousDebitAmountLCY_PeriodDebitAmountLCY_">
          <DataField>PreviousCreditAmountLCY_PeriodCreditAmountLCY___PreviousDebitAmountLCY_PeriodDebitAmountLCY_</DataField>
        </Field>
        <Field Name="PreviousCreditAmountLCY_PeriodCreditAmountLCY___PreviousDebitAmountLCY_PeriodDebitAmountLCY_Format">
          <DataField>PreviousCreditAmountLCY_PeriodCreditAmountLCY___PreviousDebitAmountLCY_PeriodDebitAmountLCY_Format</DataField>
        </Field>
        <Field Name="PreviousDebitAmountLCY_PreviousCreditAmountLCY_Control1120069">
          <DataField>PreviousDebitAmountLCY_PreviousCreditAmountLCY_Control1120069</DataField>
        </Field>
        <Field Name="PreviousDebitAmountLCY_PreviousCreditAmountLCY_Control1120069Format">
          <DataField>PreviousDebitAmountLCY_PreviousCreditAmountLCY_Control1120069Format</DataField>
        </Field>
        <Field Name="PreviousCreditAmountLCY_PreviousDebitAmountLCY_Control1120072">
          <DataField>PreviousCreditAmountLCY_PreviousDebitAmountLCY_Control1120072</DataField>
        </Field>
        <Field Name="PreviousCreditAmountLCY_PreviousDebitAmountLCY_Control1120072Format">
          <DataField>PreviousCreditAmountLCY_PreviousDebitAmountLCY_Control1120072Format</DataField>
        </Field>
        <Field Name="PeriodDebitAmountLCY_Control1120075">
          <DataField>PeriodDebitAmountLCY_Control1120075</DataField>
        </Field>
        <Field Name="PeriodDebitAmountLCY_Control1120075Format">
          <DataField>PeriodDebitAmountLCY_Control1120075Format</DataField>
        </Field>
        <Field Name="PeriodCreditAmountLCY_Control1120078">
          <DataField>PeriodCreditAmountLCY_Control1120078</DataField>
        </Field>
        <Field Name="PeriodCreditAmountLCY_Control1120078Format">
          <DataField>PeriodCreditAmountLCY_Control1120078Format</DataField>
        </Field>
        <Field Name="PreviousDebitAmountLCY_PeriodDebitAmountLCY___PreviousCreditAmountLCY_PeriodCreditAmountLCY__Control1120081">
          <DataField>PreviousDebitAmountLCY_PeriodDebitAmountLCY___PreviousCreditAmountLCY_PeriodCreditAmountLCY__Control1120081</DataField>
        </Field>
        <Field Name="PreviousDebitAmountLCY_PeriodDebitAmountLCY___PreviousCreditAmountLCY_PeriodCreditAmountLCY__Control1120081Format">
          <DataField>PreviousDebitAmountLCY_PeriodDebitAmountLCY___PreviousCreditAmountLCY_PeriodCreditAmountLCY__Control1120081Format</DataField>
        </Field>
        <Field Name="PreviousCreditAmountLCY_PeriodCreditAmountLCY___PreviousDebitAmountLCY_PeriodDebitAmountLCY__Control1120084">
          <DataField>PreviousCreditAmountLCY_PeriodCreditAmountLCY___PreviousDebitAmountLCY_PeriodDebitAmountLCY__Control1120084</DataField>
        </Field>
        <Field Name="PreviousCreditAmountLCY_PeriodCreditAmountLCY___PreviousDebitAmountLCY_PeriodDebitAmountLCY__Control1120084Format">
          <DataField>PreviousCreditAmountLCY_PeriodCreditAmountLCY___PreviousDebitAmountLCY_PeriodDebitAmountLCY__Control1120084Format</DataField>
        </Field>
        <Field Name="Customer_Trial_BalanceCaption">
          <DataField>Customer_Trial_BalanceCaption</DataField>
        </Field>
        <Field Name="No_Caption">
          <DataField>No_Caption</DataField>
        </Field>
        <Field Name="NameCaption">
          <DataField>NameCaption</DataField>
        </Field>
        <Field Name="Balance_at_Starting_DateCaption">
          <DataField>Balance_at_Starting_DateCaption</DataField>
        </Field>
        <Field Name="Balance_Date_RangeCaption">
          <DataField>Balance_Date_RangeCaption</DataField>
        </Field>
        <Field Name="Balance_at_Ending_dateCaption">
          <DataField>Balance_at_Ending_dateCaption</DataField>
        </Field>
        <Field Name="DebitCaption">
          <DataField>DebitCaption</DataField>
        </Field>
        <Field Name="CreditCaption">
          <DataField>CreditCaption</DataField>
        </Field>
        <Field Name="DebitCaption_Control1120030">
          <DataField>DebitCaption_Control1120030</DataField>
        </Field>
        <Field Name="CreditCaption_Control1120032">
          <DataField>CreditCaption_Control1120032</DataField>
        </Field>
        <Field Name="DebitCaption_Control1120034">
          <DataField>DebitCaption_Control1120034</DataField>
        </Field>
        <Field Name="CreditCaption_Control1120036">
          <DataField>CreditCaption_Control1120036</DataField>
        </Field>
        <Field Name="Grand_totalCaption">
          <DataField>Grand_totalCaption</DataField>
        </Field>
        <Field Name="WSAExternalNo">
          <DataField>WSAExternalNo</DataField>
        </Field>
      </Fields>
      <Query>
        <DataSourceName>DataSource</DataSourceName>
        <CommandText />
      </Query>
    </DataSet>
  </DataSets>
</Report>