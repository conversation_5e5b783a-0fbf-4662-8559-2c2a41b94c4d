﻿CREATE TABLE [cosium].[articles_history] (
    [id]               NVARCHAR (50)  NOT NULL,
    [numserie]         NVARCHAR (50)  NULL,
    [type]             NVARCHAR (50)  NULL,
    [statut]           NVARCHAR (50)  NULL,
    [quantite]         NVARCHAR (50)  NULL,
    [prixvente]        NVARCHAR (50)  NULL,
    [refhistoappareil] NVARCHAR (50)  NULL,
    [centre]           NVARCHAR (50)  NULL,
    [commentaire]      NVARCHAR (MAX) NULL,
    [savencours]       NVARCHAR (50)  NULL,
    [cache]            NVARCHAR (50)  NULL,
    [remise]           NVARCHAR (50)  NULL,
    [typeremise]       NVARCHAR (50)  NULL,
    [libelleremiseart] NVARCHAR (200) NULL,
    [appcouleur]       NVARCHAR (50)  NULL,
    [appmatiere]       NVARCHAR (50)  NULL,
    [depot]            NVARCHAR (50)  NULL,
    [seuilarticle]     NVARCHAR (50)  NULL,
    [datecreation]     NVARCHAR (50)  NULL,
    [prixprestation]   NVARCHAR (50)  NULL,
    [datemodif]        NVARCHAR (50)  NULL,
    [prixachat]        NVARCHAR (50)  NULL,
    [IntegrationRequired] BIT DEFAULT 0 NOT NULL,
    [IntegrationDate]  DATETIME2(7)   NULL,
    [CreatedOn]        DATETIME2      NOT NULL,
    [ModifiedOn]       DATETIME2      NOT NULL,
    [ValidFrom]        DATETIME2      NOT NULL,
    [ValidTo]          DATETIME2      NOT NULL
);
GO

CREATE TABLE [cosium].[articles] (
    [id]               NVARCHAR (50)  NOT NULL,
    [numserie]         NVARCHAR (50)  NULL,
    [type]             NVARCHAR (50)  NULL,
    [statut]           NVARCHAR (50)  NULL,
    [quantite]         NVARCHAR (50)  NULL,
    [prixvente]        NVARCHAR (50)  NULL,
    [refhistoappareil] NVARCHAR (50)  NULL,
    [centre]           NVARCHAR (50)  NULL,
    [commentaire]      NVARCHAR (MAX) NULL,
    [savencours]       NVARCHAR (50)  NULL,
    [cache]            NVARCHAR (50)  NULL,
    [remise]           NVARCHAR (50)  NULL,
    [typeremise]       NVARCHAR (50)  NULL,
    [libelleremiseart] NVARCHAR (200) NULL,
    [appcouleur]       NVARCHAR (50)  NULL,
    [appmatiere]       NVARCHAR (50)  NULL,
    [depot]            NVARCHAR (50)  NULL,
    [seuilarticle]     NVARCHAR (50)  NULL,
    [datecreation]     NVARCHAR (50)  NULL,
    [prixprestation]   NVARCHAR (50)  NULL,
    [datemodif]        NVARCHAR (50)  NULL,
    [prixachat]        NVARCHAR (50)  NULL,
    [IntegrationRequired]   BIT DEFAULT 0 NOT NULL,
    [IntegrationDate]  DATETIME2(7) NULL,
    [CreatedOn]        DATETIME2 CONSTRAINT [DF_articles_CreatedOn] DEFAULT (sysutcdatetime()) NOT NULL,
    [ModifiedOn]       DATETIME2 CONSTRAINT [DF_articles_ModifiedOn] DEFAULT (sysutcdatetime()) NOT NULL,
    [ValidFrom]        DATETIME2 GENERATED ALWAYS AS ROW START,
    [ValidTo]          DATETIME2 GENERATED ALWAYS AS ROW END,
    PERIOD FOR SYSTEM_TIME(ValidFrom, ValidTo),
    CONSTRAINT [PK_articles] PRIMARY KEY CLUSTERED ([id] ASC)
)
WITH (SYSTEM_VERSIONING = ON (HISTORY_TABLE = cosium.articles_history)
);
GO

CREATE TRIGGER [cosium].[articles_UpdateModified]
ON [cosium].[articles]
AFTER UPDATE 
AS
   UPDATE [cosium].[articles]
      SET [ModifiedOn] = sysutcdatetime(),
          [IntegrationRequired] = IIF( (i.[statut]='RETA' AND d.[statut] IS NULL)
                                  OR (i.[statut] = 'RETA' AND d.[statut] <> 'RETA'), 1, [articles].[IntegrationRequired] )
   
     FROM inserted AS i
     JOIN deleted AS d
       ON i.id = d.id

   WHERE [cosium].[articles].[id] = i.[id]
GO