﻿using System.Text.Json.Serialization;

namespace WSA.Retail.Integration.Model
{
    public class ExternalDocumentReference
    {
        [JsonPropertyName("id")]
        public Guid? Id { get; set; }

        [JsonPropertyName("documentNumber")]
        public string? DocumentNumber { get; set; }

        [JsonPropertyName("externalReference")]
        public string? ExternalReference { get; set; }

        public ExternalDocumentReference() { }
    }
}
