{"dependencies": {"storage1": {"resourceId": "/subscriptions/[parameters('subscriptionId')]/resourceGroups/[parameters('resourceGroupName')]/providers/Microsoft.Storage/storageAccounts/wsawenonprodbizcentralsc", "type": "storage.azure", "connectionId": "AzureWebJobsStorage"}, "appInsights1": {"secretStore": null, "resourceId": "/subscriptions/[parameters('subscriptionId')]/resourceGroups/[parameters('resourceGroupName')]/providers/microsoft.insights/components/retail-integration-we-dev", "type": "appInsights.azure", "connectionId": "APPLICATIONINSIGHTS_CONNECTION_STRING"}}}