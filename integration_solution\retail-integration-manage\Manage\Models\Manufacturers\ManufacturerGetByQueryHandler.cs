﻿using Microsoft.Extensions.Options;
using WSA.Retail.Integration.Manage.Configuration;
using WSA.Retail.Integration.Manage.Core;
using WSA.Retail.Integration.Models.Manufacturers;

namespace WSA.Retail.Integration.Manage.Models.Manufacturers;

public class ManufacturerGetByQueryHandler(
    IOptions<AppSettings> appSettings,
    IManufacturerService entityService) 
    : BaseApiGetByQuery<
        Manufacturer,
        IManufacturerService>(appSettings, entityService)
{
}