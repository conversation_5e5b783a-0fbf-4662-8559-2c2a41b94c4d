﻿using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System.Data;
using System.Text.Json;

namespace WSA.Retail.Integration.Cosium
{
    internal class Patient:Model.Patient
    {
        internal Patient() { }

        internal Patient(string requestBody): base(requestBody) { }

        internal Patient(Model.Patient? baseObject)
        {
            if (baseObject != null)
            {
                this.Id = baseObject.Id;
                this.ExternalSystemCode = baseObject.ExternalSystemCode;
                this.Code = baseObject.Code;
                this.AlternateCode = baseObject.AlternateCode;
                this.ExternalCode = baseObject.ExternalCode;
                this.Name = baseObject.Name;
                this.Address = baseObject.Address;
                this.Address2 = baseObject.Address2;
                this.City = baseObject.City;
                this.Region = baseObject.Region;
                this.PostalCode = baseObject.PostalCode;
                this.Country = baseObject.Country;
                this.Phone = baseObject.Phone;
                this.Email = baseObject.Email;
                this.IdentificationNumber = baseObject.IdentificationNumber;
            }
        }

        private static readonly string _entityCode = "PATIENT";

        internal static async Task ProcessNewRecordsAsync()
        {
            var log = Common.Logger();
            var scope = InitScope(nameof(ProcessNewRecordsAsync));
            log.BeginScope(scope);
            log.LogInformation("[{className}].[{procedureName}] started", ClassName(), nameof(ProcessNewRecordsAsync));

            List<Patient?> patients = await GetFromSource();
            log.LogInformation("[{className}].[{procedureName}] Fetched {recordCount} patients from Cosium database",
                ClassName(), nameof(ProcessNewRecordsAsync), patients?.Count);

            if (patients?.Count > 0)
            {
                await PostListAsync(patients);
            }
            else
            {
                log.LogInformation("[{className}].[{procedureName}] No new patients are avalable in the Cosium database.",
                    ClassName(), nameof(ProcessNewRecordsAsync));
            }
        }

        private static async Task<List<Patient?>> GetFromSource()
        {
            var log = Common.Logger();
            log.LogInformation("[{className}].[{procedureName}] started", ClassName(), nameof(GetFromSource));

            List<Patient?> ObjectList = [];

            var context = Common.Context();
            try
            {
                var recordList = await context.Patients.Where(x => x.IntegrationRequired == true).ToListAsync();
                if (recordList != null)
                {
                    if (recordList.Count > 0)
                    {
                        log.LogInformation("[{className}].[{procedureName}] SQL reader returned {rowCount} rows.",
                                        ClassName(), nameof(GetFromSource), recordList.Count);
                        foreach (Patients record in recordList)
                        {
                            if (record != null)
                            {
                                if (record.JSON != null)
                                {
                                    Patient newObject = new(record.JSON)
                                    {
                                        ExternalSystemCode = Common.ExternalSystemCode()
                                    };
                                    ObjectList.Add(newObject);
                                }
                            }
                        }
                        return ObjectList;
                    }
                    else
                    {
                        log.LogInformation("[{className}].[{procedureName}] No new patients were retrieved from the database.",
                            ClassName(), nameof(GetFromSource));
                    }
                }
            }
            catch (Exception ex)
            {
                log.LogError(ex, "[{className}].[{procedureName}] Attempt to fetch patients from the database generated an exception:\r\n{object}",
                    ClassName(), nameof(GetFromSource), ex.Message);
            }

            return ObjectList;
        }

        internal static async Task<Patient?> GetFromBaseAsync(string? code = null, string? externalCode = null)
        {
            var log = Common.Logger();
            log.LogInformation("[{className}].[{procedureName}] started", ClassName(), nameof(GetFromBaseAsync));

            Patient? patient = new(await Model.Patient.GetAsync(Common.ExternalSystemCode(), code: code, externalCode: externalCode));
            if (patient.Id != null)
            {
                return patient;
            }
            return null;
        }

        internal static async Task<Patient?> GetFromDatabaseById(int thisId)
        {
            var log = Common.Logger();
            log.LogInformation("[{className}].[{procedureName}] started", ClassName(), nameof(GetFromDatabaseById));

            var context = Common.Context();
            try
            {
                var oldRecord = await context.Patients.FindAsync(thisId.ToString());
                ArgumentNullException.ThrowIfNull(oldRecord);
                ArgumentNullException.ThrowIfNull(oldRecord.JSON);
                Patient? newObject = new(oldRecord.JSON);
                return newObject;
            }
            catch (Exception ex)
            {
                log.LogError(ex, "[{className}].[{procedureName}] Attempt to fetch patient from the database generated an exception:\r\n{object}",
                    ClassName(), nameof(GetFromDatabaseById), ex.Message);
            }
            return null;
        }

        internal static async Task PostListAsync(List<Patient?> patients)
        {
            var log = Common.Logger();
            log.LogInformation("[{className}].[{procedureName}] started", ClassName(), nameof(PostListAsync));

            var entitySubscriber = await Model.EntitySubscriber.GetAsync(null, Common.ExternalSystemCode(), _entityCode);
            ArgumentNullException.ThrowIfNull(entitySubscriber);
            if (entitySubscriber.FromExternalSystem ?? false == true)
            {
                try
                {
                    foreach (var patient in patients)
                    {
                        if (patient != null)
                        {
                            await patient.PostAsync(entitySubscriber);
                        }
                    }
                }
                catch (Exception ex)
                {
                    log.LogError(ex, "[{className}].[{procedureName}] Encountered an exception:\r\n{object}",
                        ClassName(), nameof(PostListAsync), ex.Message);
                }
            }
        }

        internal async Task<Patient?> PostAsync(Model.EntitySubscriber? entitySubscriber = null)
        {
            var log = Common.Logger();
            log.LogInformation("[{className}].[{procedureName}] started", ClassName(), nameof(PostAsync));

            if (entitySubscriber == null)
            {
                entitySubscriber = await Model.EntitySubscriber.GetAsync(null, Common.ExternalSystemCode(), _entityCode);
                ArgumentNullException.ThrowIfNull(entitySubscriber);
            }
            if (entitySubscriber.FromExternalSystem ?? false == true)
            {
                try
                {
                    if (this != null)
                    {
                        log.LogInformation("[{className}].[{procedureName}] Sending patient: {code} to API\r\n{requestBody}",
                            ClassName(), nameof(PostAsync), this.Code, JsonSerializer.Serialize(this, Common.GetJsonOptions()));
                        this.ExternalSystemCode = Common.ExternalSystemCode();
                        var responseObject = await this.Upsert();
                        if (responseObject != null)
                        {
                            log.LogInformation("[{className}].[{procedureName}] Successfully updated patient: {code}", ClassName(), nameof(PostAsync), this.Code);
                            await UpdateIsIntegratedAsync();
                            return new(responseObject);
                        }
                        else
                        {
                            log.LogError("[{className}].[{procedureName}] Attempt to update patient: {code} failed",
                                ClassName(), nameof(PostAsync), this.Code);
                        }
                    }
                }
                catch (Exception ex)
                {
                    log.LogError(ex, "[{className}].[{procedureName}] Encountered an exception:\r\n{object}",
                        ClassName(), nameof(PostAsync), ex.Message);
                }
            }
            return null;
        }

        private async Task<bool> UpdateIsIntegratedAsync()
        {
            var log = Common.Logger();
            log.LogInformation("[{className}].[{procedureName}] started", ClassName(), nameof(UpdateIsIntegratedAsync));

            string? connString = Environment.GetEnvironmentVariable("SqlConnectionString");
            ArgumentNullException.ThrowIfNull(connString);
            ArgumentNullException.ThrowIfNull(this.ExternalCode);

            using SqlConnection conn = new(connString);
            {
                try
                {
                    conn.Open();
                    using SqlCommand cmd = conn.CreateCommand();
                    {
                        cmd.CommandType = CommandType.Text;
                        cmd.CommandText = "UPDATE cosium.Patients SET IntegrationRequired = 0, IntegrationDate = sysutcdatetime() WHERE Id = @id";
                        cmd.Parameters.AddWithValue("@id", this.ExternalCode);
                        await cmd.ExecuteNonQueryAsync();
                        conn.Close();
                        return true;
                    }
                }
                catch
                {
                    return false;
                }
            }
        }

        internal static async Task ValidateExternalReference(Model.ExternalReference? externalReference)
        {
            var log = Common.Logger();
            log.LogInformation("[{className}].[{procedureName}] started", ClassName(), nameof(ValidateExternalReference));

            if (externalReference != null && externalReference.ExternalCode != null)
            {
                ArgumentNullException.ThrowIfNull(externalReference.ExternalCode);
                Patient? patient = await GetFromBaseAsync(externalCode: externalReference.ExternalCode);
                {
                    if (patient == null)
                    {
                        patient = await GetFromDatabaseById(int.Parse(externalReference.ExternalCode));
                        if (patient != null)
                        {
                            patient = await patient.PostAsync(null);
                        }
                    }
                }

                ArgumentNullException.ThrowIfNull(patient);
                ArgumentNullException.ThrowIfNull(patient.Id);
                externalReference.Id = patient.Id;
                externalReference.ExternalCode = patient.ExternalCode;
                externalReference.Code = patient.Code;
                externalReference.Name = patient.Name;
            }
        }

        private static string ClassName()
        {
            return nameof(Patient);
        }

        private static Dictionary<string, object> InitScope(string procedureName)
        {
            return Common.InitScope(ClassName(), procedureName);
        }
    }
}