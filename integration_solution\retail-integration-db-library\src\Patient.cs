﻿using System.Data;
using System.Text.Json.Nodes;
using System.Text;
using System.Text.Json;
using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Logging;
using WSA.Retail.Integration.Base;


namespace WSA.Retail.Integration.Base.Patient;

public class Patient
{
    private string? externalSystemCode;
    private string? code;
    private string? externalCode;
    private string? name;
    private string? address;
    private string? address2;
    private string? city;
    private string? region;
    private string? country;
    private string? postalCode;
    private string? phone;
    private string? email;

    public Guid Id { get; set; } = Guid.NewGuid();

    public string? ExternalSystemCode
    {
        get { return externalSystemCode; }
        set
        {
            externalSystemCode = value;
            ValidateNullOrEmpty(externalSystemCode, nameof(ExternalSystemCode));
            ValidateLength(externalSystemCode, 20, nameof(ExternalSystemCode));
        }
    }

    public string? Code
    {
        get { return code; }
        set
        {
            code = value;
            ValidateNullOrEmpty(code, nameof(Code));
            ValidateLength(code, 20, nameof(Code));
        }
    }

    public string? ExternalCode
    {
        get { return externalCode; }
        set
        {
            externalCode = value;
            ValidateNullOrEmpty(externalCode, nameof(ExternalCode));
            ValidateLength(externalCode, 100, nameof(ExternalCode));
        }
    }

    public string? Name
    {
        get { return name; }
        set
        {
            name = value;
            ValidateLength(name, 100, nameof(Name));
        }
    }

    public string? Address
    {
        get { return address; }
        set
        {
            address = value;
            ValidateLength(address, 100, nameof(Address));
        }
    }

    public string? Address2
    {
        get { return address2; }
        set
        {
            address2 = value;
            ValidateLength(address2, 50, nameof(Address2));
        }
    }

    public string? City
    {
        get { return city; }
        set
        {
            city = value;
            ValidateLength(city, 30, nameof(City));
        }
    }

    public string? Region
    {
        get { return region; }
        set
        {
            region = value;
            ValidateLength(region, 30, nameof(Region));
        }
    }

    public string? Country
    {
        get { return country; }
        set
        {
            country = value;
            ValidateLength(country, 10, nameof(Country));
        }
    }

    public string? PostalCode
    {
        get { return postalCode; }
        set
        {
            postalCode = value;
            ValidateLength(postalCode, 20, nameof(PostalCode));
        }
    }

    public string? Phone
    {
        get { return phone; }
        set
        {
            phone = value;
            ValidateLength(phone, 30, nameof(Phone));
        }
    }

    public string? Email
    {
        get { return email; }
        set
        {
            email = value;
            ValidateLength(email, 50, nameof(Email));
        }
    }

    public bool? IsActive { get; set; }


    private static void ValidateNullOrEmpty(string? value, string paramerterName)
    {
        if (value == null)
        {
            throw new ArgumentNullException(paramerterName);
        }
        else if (value == string.Empty)
        {
            throw new ArgumentException("Value cannot be empty.", paramerterName);
        }
    }


    private static void ValidateLength(string? value, int length, string paramerterName)
    {
        if (value is not null && value.Length > length)
        {
            throw new ArgumentOutOfRangeException(paramerterName, "Value cannot exceed " + length.ToString() + " characters.");
        }
    }


    public static Patient GetPatientFromEvent(WSA.Retail.Integration.Base.Event ev)
    {

    }


    public void GetPatient()
    {
        string? connString = Environment.GetEnvironmentVariable("SqlConnectionString");
        ArgumentNullException.ThrowIfNull(connString);

        using SqlConnection conn = new (connString);
        {
            conn.Open();

            using SqlCommand cmd = conn.CreateCommand();
            {
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandText = "dbo.PatientGet";
                cmd.Parameters.AddWithValue("@externalSystemCode", externalSystemCode);
                cmd.Parameters.AddWithValue("@code", code);
                cmd.Parameters.AddWithValue("@externalCode", externalCode);

                SqlDataReader reader = cmd.ExecuteReader();
                UpdatePatientFromReader(ref reader);
                conn.Close();
            }
        }
    }


    public async void Upsert(ILogger _logger)
    {
        string? connString = Environment.GetEnvironmentVariable("SqlConnectionString");
        ArgumentNullException.ThrowIfNull(connString);

        Patient oldPatient = new()
        {
            externalSystemCode = externalSystemCode,
            code = code,
            externalCode = externalCode
        };
        oldPatient.GetPatient();

        using SqlConnection conn = new(connString);
        {
            conn.Open();

            using SqlCommand cmd = conn.CreateCommand();
            {
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandText = "dbo.PatientUpsert";
                cmd.Parameters.AddWithValue("@externalSystemCode", externalSystemCode);
                cmd.Parameters.AddWithValue("@code", code);
                cmd.Parameters.AddWithValue("@externalCode", externalCode);
                cmd.Parameters.AddWithValue("@name", name).Value ??= DBNull.Value;
                cmd.Parameters.AddWithValue("@address", address).Value ??= DBNull.Value;
                cmd.Parameters.AddWithValue("@address2", address2).Value ??= DBNull.Value;
                cmd.Parameters.AddWithValue("@city", city).Value ??= DBNull.Value;
                cmd.Parameters.AddWithValue("@region", region).Value ??= DBNull.Value;
                cmd.Parameters.AddWithValue("@country", country).Value ??= DBNull.Value;
                cmd.Parameters.AddWithValue("@postalCode", postalCode).Value ??= DBNull.Value;
                cmd.Parameters.AddWithValue("@phone", phone).Value ??= DBNull.Value;
                cmd.Parameters.AddWithValue("@email", email).Value ??= DBNull.Value;
                cmd.Parameters.AddWithValue("@isActive", IsActive).Value ??= DBNull.Value;

                _logger.LogInformation("Executing SQL Command: \r\n{command}", JsonSerializer.Serialize(Common.SerializeSqlCommand(cmd), Common.GetJsonOptions()));

                SqlDataReader reader = cmd.ExecuteReader();
                
                _logger.LogInformation("SQL Command success");
                UpdatePatientFromReader(ref reader);
                conn.Close();

                if (HasChanged(oldPatient))
                {
                    JsonObject data = new()
                    {
                        { "externalSystemCode", "DB" },
                        { "code", Code },
                        { "externalCode", ExternalCode },
                        { "name", Name },
                        { "address", Address },
                        { "address2", Address2 },
                        { "city", City },
                        { "region", Region },
                        { "country", Country },
                        { "postalCode", PostalCode },
                        { "phone", Phone },
                        { "email", Email },
                    };
                        
                    JsonObject eventGridJson = new()
                    {
                        { "id", Guid.NewGuid().ToString() },
                        { "source", "retail-integration-we-dev.database.windows.net" },
                        { "specversion", "1.0" },
                        { "type", "Base" },
                        { "subject", Id.ToString() },
                        { "time", DateTime.UtcNow },
                        { "data", data }
                    };


                    string? eventGridUri = Environment.GetEnvironmentVariable("EventGridEndpoint");
                    ArgumentNullException.ThrowIfNull(eventGridUri);

                    _logger.LogInformation("Sending event to: \r\n{eventUri}", eventGridUri);

                    string? eventGridKey = Environment.GetEnvironmentVariable("EventGridAccessKey");
                    ArgumentNullException.ThrowIfNull(eventGridKey);

                    HttpClient client = new();
                    client.DefaultRequestHeaders.Add("aeg-sas-key", eventGridKey);

                    string requestBody = JsonSerializer.Serialize(eventGridJson);
                    using StringContent content = new(requestBody, Encoding.UTF8, "application/cloudevents+json");

                    using HttpResponseMessage response = await client.PostAsync(eventGridUri, content);
                    response.EnsureSuccessStatusCode();
                    _logger.LogInformation("Event sent successfully");
                };
            }
            conn.Close();
            conn.Close();
        }
    }

    public bool HasChanged(Patient oldPatient)
    {
        if (code != oldPatient.Code ||
            name != oldPatient.Name ||
            address != oldPatient.Address ||
            address2 != oldPatient.Address2 ||
            city != oldPatient.City ||
            region != oldPatient.Region ||
            country != oldPatient.Country ||
            postalCode != oldPatient.PostalCode ||
            phone != oldPatient.Phone ||
            email != oldPatient.Email)
        {
            return true;
        }
        else
        {
            return false;
        }
    }

    private void UpdatePatientFromReader(ref SqlDataReader reader)
    {
        if (reader.Read())
        {
            Id = reader.GetGuid(reader.GetOrdinal("Id"));
            code = reader.GetString(reader.GetOrdinal("Code"));
            externalCode = reader.GetString(reader.GetOrdinal("ExternalCode"));
            name = reader.IsDBNull(reader.GetOrdinal("Name")) ? null : reader.GetString(reader.GetOrdinal("Name"));
            address = reader.IsDBNull(reader.GetOrdinal("Address")) ? null : reader.GetString(reader.GetOrdinal("Address"));
            address2 = reader.IsDBNull(reader.GetOrdinal("Address2")) ? null : reader.GetString(reader.GetOrdinal("Address2"));
            city = reader.IsDBNull(reader.GetOrdinal("City")) ? null : reader.GetString(reader.GetOrdinal("City"));
            region = reader.IsDBNull(reader.GetOrdinal("Region")) ? null : reader.GetString(reader.GetOrdinal("Region"));
            country = reader.IsDBNull(reader.GetOrdinal("Country")) ? null : reader.GetString(reader.GetOrdinal("Country"));
            postalCode = reader.IsDBNull(reader.GetOrdinal("PostalCode")) ? null : reader.GetString(reader.GetOrdinal("PostalCode"));
            phone = reader.IsDBNull(reader.GetOrdinal("Phone")) ? null : reader.GetString(reader.GetOrdinal("Phone"));
            email = reader.IsDBNull(reader.GetOrdinal("Email")) ? null : reader.GetString(reader.GetOrdinal("Email"));
            IsActive = reader.IsDBNull(reader.GetOrdinal("IsActive")) ? null : reader.GetBoolean(reader.GetOrdinal("IsActive"));
            reader.Dispose();
        }
    }
}

