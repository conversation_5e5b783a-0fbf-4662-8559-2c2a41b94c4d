﻿using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System.Data;
using WSA.Retail.Integration.PIM.Core;
using WSA.Retail.Integration.PIM.Data;
using WSA.Retail.Integration.PIM.GraphQL;

namespace WSA.Retail.Integration.PIM.Models.Colors;

public class PimColorRepository(
    ILogger<PimColorRepository> logger,
    IDbContextFactory<PimDbContext> dbContextFactory) :
        PimProductRelatedEntityRepositoryBase<PimColorUpsertTableType>(
            logger,
            dbContextFactory),
        IPimColorRepository
{
    protected override string StoredProcedureName => "pim.ColorUpsert";
    protected override string TableTypeName => "pim.ColorUpsertTableType";

    protected override List<PimColorUpsertTableType> GetListOfRelatedEntities(List<PisProductResponseForAllProductsOutputType> pimProducts)
    {
        var relatedEntities = pimProducts
            .Where(p => p.Color != null)
            .Where(p => p.Color!.AttributeValueCode != null)
            .Select(p => new
            {
                p.Color?.AttributeValueCode,
                p.Color?.AgileName,
                p.Color?.HexCode
            })
            .Distinct()
            .Select(a => new PimColorUpsertTableType
            {
                Code = a.AttributeValueCode!,
                Name = a.AgileName,
                HexCode = a.HexCode
            })
            .ToList();
        return relatedEntities;
    }

    protected override DataTable CreateDataTable()
    {
        var dataTable = new DataTable();
        dataTable.Columns.Add("Code", typeof(string));
        dataTable.Columns.Add("Name", typeof(string));
        dataTable.Columns.Add("HexCode", typeof(string));
        return dataTable;
    }

    protected override DataRow CreateDataRow(PimColorUpsertTableType entity, DataTable dataTable)
    {
        var dataRow = dataTable.NewRow();
        {
            dataRow["Code"] = entity.Code;
            dataRow["Name"] = entity.Name;
            dataRow["HexCode"] = entity.HexCode;
        }
        return dataRow;
    }
}