﻿using WSA.Retail.Integration.PIM.Models.Brands;

namespace WSA.Retail.Integration.PIM.Models.Products
{
    public class PimProductBrandEntity : IPimProductBrandEntity, WSA.Retail.Integration.Core.IIdentifiable
    {
        public Guid Id { get; set; } = Guid.Empty;
        public required Guid ProductId { get; set; }
        public required Guid BrandId { get; set; }
        public required DateTime SystemCreatedOn { get; set; }
        public required DateTime SystemModifiedOn { get; set; }

        public virtual PimProductEntity ProductEntity { get; set; } = null!;
        public virtual PimBrandEntity BrandEntity { get; set; } = null!;
    }
}
