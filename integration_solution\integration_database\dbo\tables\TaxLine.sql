﻿CREATE TABLE [dbo].[TaxLine]
(
    [Id]                    UNIQUEIDENTIFIER CONSTRAINT [DF_TaxLine_Id] DEFAULT NEWID() NOT NULL,
    [RecordId]              UNIQUEIDENTIFIER NOT NULL,
    [LineNo]                INT NOT NULL,
    [TaxGroupId]            UNIQUEIDENTIFIER NOT NULL,
    [IsTaxLiable]           BIT CONSTRAINT [DF_TaxLine_IsTaxLiable] DEFAULT(1) NOT NULL,
    [BaseAmount]            MONEY NULL,
    [TaxableAmount]         MONEY NULL,
    [Rate]                  MONEY NULL,
    [MinAmount]             MONEY NULL,
    [MaxAmount]             MONEY NULL,
    [TaxAmount]             MONEY NULL,
    [IsActive]              BIT CONSTRAINT [DF_TaxLine_IsActive] DEFAULT(1) NOT NULL,
    [CreatedOn]             DATETIME2 CONSTRAINT [DF_TaxLine_CreatedOn] DEFAULT (sysutcdatetime())  NOT NULL,
    [ModifiedOn]            DATETIME2 CONSTRAINT [DF_TaxLine_ModifiedOn] DEFAULT (sysutcdatetime())  NOT NULL,
    CONSTRAINT              [PK_Tax_Id] PRIMARY KEY CLUSTERED ([Id] ASC),
    CONSTRAINT              [FK_TaxLine_TaxGroup] FOREIGN KEY (TaxGroupId) REFERENCES [dbo].[TaxGroup](Id)
)
GO

CREATE UNIQUE INDEX IX_TaxLine_RecordId_LineNo
ON [dbo].[TaxLine] ([RecordId], [LineNo])
GO

CREATE TRIGGER TaxLine_UpdateModified
ON [dbo].[TaxLine]
AFTER UPDATE 
AS
   UPDATE [dbo].[TaxLine]
   SET [ModifiedOn] = sysutcdatetime()
   FROM Inserted AS i
   WHERE [dbo].[TaxLine].[Id] = i.[Id]
GO
