﻿using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System.Data;
using WSA.Retail.Integration.Logging;
using WSA.Retail.Integration.PIM.Data;
using WSA.Retail.Integration.PIM.GraphQL;

namespace WSA.Retail.Integration.PIM.Core;

public abstract class PimProductRelatedEntityRepositoryBase<TUpsertTableType>(
    ILogger logger,
    IDbContextFactory<PimDbContext> dbContextFactory) :
    LoggingBase<TUpsertTableType>(logger),
    IPimProductRelatedEntityRepositoryBase<TUpsertTableType>
    where TUpsertTableType : class
{
    private readonly IDbContextFactory<PimDbContext> _dbContextFactory = dbContextFactory;

    protected abstract string StoredProcedureName { get; }
    protected abstract string TableTypeName { get; }

    public async Task<bool> UpsertListAsync(List<PisProductResponseForAllProductsOutputType> pimProducts)
    {
        LogMethodStart();
        var relatedEntities = GetListOfRelatedEntities(pimProducts);
        if (relatedEntities?.Count > 0)
        {
            await UpsertFromListAsync(relatedEntities);
        }
        return true;
    }

    public async Task<bool> UpsertFromListAsync(List<TUpsertTableType> relatedEntities)
    {
        LogMethodStart();
        using var context = _dbContextFactory.CreateDbContext();
        var connection = context.Database.GetDbConnection();
        using var command = connection.CreateCommand();

        command.CommandType = CommandType.StoredProcedure;
        command.CommandText = StoredProcedureName;
        command.CommandTimeout = 300;

        // Create and prepare the data table parameter
        var dataTable = CreateDataTable();
        foreach (var entity in relatedEntities)
        {
            dataTable.Rows.Add(CreateDataRow(entity, dataTable));
        }

        command.Parameters.Add(new SqlParameter
        {
            ParameterName = "@source",
            SqlDbType = SqlDbType.Structured,
            TypeName = TableTypeName,
            Value = dataTable
        });

        var rowsReceivedParam = new SqlParameter
        {
            ParameterName = "@rowsReceived",
            SqlDbType = SqlDbType.Int,
            Direction = ParameterDirection.Output
        };
        command.Parameters.Add(rowsReceivedParam);

        var diagnosticParam = new SqlParameter
        {
            ParameterName = "@diagnosticMessage",
            SqlDbType = SqlDbType.NVarChar,
            Size = -1,
            Direction = ParameterDirection.Output
        };
        command.Parameters.Add(diagnosticParam);
        
        try
        {
            if (connection.State != ConnectionState.Open)
            {
                await connection.OpenAsync();
            }

            var rowsAffected = await command.ExecuteNonQueryAsync();
            LogCustomInformation($"Stored procedure recieved {rowsReceivedParam.Value} records.");
            LogCustomInformation($"Stored procedure diagnostics: \r\n{diagnosticParam.Value}");

            return true;
        }
        catch (Exception ex)
        {
            LogCustomError(ex);
            return false;
        }
        finally
        {
            if (connection.State == ConnectionState.Open)
            {
                await connection.CloseAsync();
            }
        }
    }

    protected abstract List<TUpsertTableType> GetListOfRelatedEntities(List<PisProductResponseForAllProductsOutputType> pimProducts);
    protected abstract DataTable CreateDataTable();
    protected abstract DataRow CreateDataRow(TUpsertTableType entity, DataTable dataTable);
}