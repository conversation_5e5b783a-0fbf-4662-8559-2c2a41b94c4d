﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace WSA.Retail.Integration.Cosium
{
    [Table("Patients", Schema = "cosium")]
    public class Patients
    {
        [Key]
        public string? Id { get; set; }
        public string? Code { get; set; }
        public string? ExternalCode { get; set; }
        public string? Name { get; set; }
        public bool? IntegrationRequired { get; set; }
        public DateTime? IntegrationDate { get; set; }
        public DateTime ModifiedOn { get; set; }
        public string? JSON { get; set; }
    }
}
