﻿using WSA.Retail.Integration.PIM.Core;
using WSA.Retail.Integration.PIM.Models.Countries;
using WSA.Retail.Integration.PIM.Models.Products;

namespace WSA.Retail.Integration.PIM.Models.Brands;

public interface IPimBrandEntity : IIdentifiable, ICodeIdentifiable, INameable, IAuditInfo
{
    public abstract ICollection<PimProductEntity> Products { get; set; }

    public abstract ICollection<PimCountryEntity> Countries { get; set; }
}