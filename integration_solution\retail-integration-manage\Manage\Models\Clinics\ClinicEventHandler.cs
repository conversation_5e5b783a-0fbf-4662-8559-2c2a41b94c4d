﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.Functions.Worker;
using WSA.Retail.Integration.Events;
using WSA.Retail.Integration.Manage.EventProcessing;

namespace WSA.Retail.Integration.Manage.Models.Clinics;

public class ClinicEventHandler(
    ClinicFromEventHandler fromEventHandler,
    ClinicGetByQueryHandler queryHandler) : IEventHandler
{
    private readonly ClinicFromEventHandler _fromEventHandler = fromEventHandler;
    private readonly ClinicGetByQueryHandler _queryHandler = queryHandler;

    public async Task<bool> HandleFromQueueAsync(EventHubMessage message)
    {
        return await _fromEventHandler.HandleFromQueueAsync(message);
    }

    public async Task<bool> HandleToQueueAsync(Event ev)
    {
        return await Task.FromResult(true);
    }

 
    [Function("ClinicGetByQuery")]
    public async Task<IActionResult> GetByQueryAsync([HttpTrigger(AuthorizationLevel.Function, "get", Route = "clinics")] HttpRequest req)
    {
        return await _queryHandler.GetByQueryInternalAsync(req);
    }
}