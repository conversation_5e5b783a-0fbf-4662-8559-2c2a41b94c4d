﻿using System.Text.Json.Serialization;
using WSA.Retail.Integration.Utilities;

namespace WSA.Retail.Integration.Manage.Models.CategoryMappings;

public class CategoryMapping
{
    [JsonPropertyName("id")] public Guid Id { get; set; }
    [JsonPropertyName("categoryId")] public Guid CategoryId { get; set; }
    [JsonPropertyName("isHearingAid")] public bool IsHearingAid { get; set; }
    [JsonPropertyName("hearingAidTypeId")] public Guid? HearingAidTypeId { get; set; }
    [JsonPropertyName("createdOn")] public DateTime CreatedOn { get; set; }
    [JsonPropertyName("modifiedOn")] public DateTime ModifiedOn { get; set; }


    public bool HasChanges(CategoryMapping? oldCategoryMapping)
    {
        if (oldCategoryMapping == null) return true;
        if (!Common.AreEqual(CategoryId, oldCategoryMapping.CategoryId)) return true;
        if (!Common.AreEqual(IsHearingAid, oldCategoryMapping.IsHearingAid)) return true;
        if (!Common.AreEqual(HearingAidTypeId, oldCategoryMapping.HearingAidTypeId)) return true;
        return false;
    }
}