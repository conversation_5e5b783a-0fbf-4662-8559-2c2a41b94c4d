﻿using System.Text.Json.Serialization;

namespace WSA.Retail.Integration.PIM.GraphQL;

public class PisCategoryResponseOutputType
{
    [JsonPropertyName("categoryId")] public Guid CategoryId { get; set; } = Guid.Empty;
    [JsonPropertyName("coreBrandId")] public string? CoreBrandId { get; set; }
    [JsonPropertyName("createdAt")] public DateTime CreatedAt { get; set; }
    //[JsonPropertyName("isVisible")] public string? IsVisible { get; set; }
    [JsonPropertyName("name")] public string? Name { get; set; }
    [JsonPropertyName("ranking")] public int Ranking { get; set; }
    [JsonPropertyName("state")] public StateOutputType? State { get; set; }
    [JsonPropertyName("supportedBrandIds")] public List<string> SupportedBrandIds { get; set; } = [];
    [JsonPropertyName("updatedAt")] public DateTime UpdatedAt { get; set; }
}