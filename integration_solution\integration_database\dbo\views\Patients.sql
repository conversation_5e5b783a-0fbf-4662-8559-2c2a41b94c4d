﻿CREATE VIEW [dbo].[Patients]
AS 

SELECT Source.Id,
       Source.Code,
       (SELECT TOP 1 ExternalRecordId
          FROM dbo.Coupling
         WHERE Coupling.RecordId = Source.Id AND Coupling.ExternalSystemId = ExternalSystem.Id) AS ExternalCode,
       Source.[Name],
       ExternalSystem.Id AS ExternalSystemId,
       ExternalSystem.Code AS ExternalSystemCode,
       Source.[ModifiedOn],
       t1.JSON

  FROM dbo.Patient AS Source

 OUTER APPLY dbo.ExternalSystem

 OUTER APPLY (SELECT(
              SELECT Patient.Id AS 'id',
                     ExternalSystem.Code AS 'externalSystemCode',
                     UPPER(Patient.Code) AS 'code',
                     (SELECT TOP 1 ExternalRecordId 
                        FROM dbo.Coupling 
                       WHERE Coupling.RecordId = Patient.Id AND Coupling.ExternalSystemId = ExternalSystem.Id) AS 'externalCode',
                     UPPER(Patient.AlternateCode) AS 'alternateCode',
                     Patient.[Name] AS 'name',
                     Patient.[Address] AS 'address',
                     Patient.Address2 AS 'address2',
                     Patient.City AS 'city',
                     Patient.Region AS 'region',
                     Patient.Country AS 'country',
                     Patient.PostalCode AS 'postalCode',
                     Patient.Phone AS 'phone',
                     Patient.Email AS 'email',
                     Patient.IdentificationNumber AS 'identificationNumber'

                FROM dbo.Patient

                WHERE Patient.Id = Source.Id

                  FOR JSON PATH, WITHOUT_ARRAY_WRAPPER
                      ) AS JSON
                      ) AS t1