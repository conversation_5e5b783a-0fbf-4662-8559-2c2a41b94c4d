﻿CREATE TABLE [dbo].[SalesCreditLine]
(
    [Id]                    UNIQUEIDENTIFIER CONSTRAINT [DF_SalesCreditLine_Id] DEFAULT NEWID() NOT NULL,
    [SalesCreditId]         UNIQUEIDENTIFIER NOT NULL,
    [Sequence]              INT NOT NULL,
    [SalesOrderLineId]      UNIQUEIDENTIFIER NULL,
    [ProductId]             UNIQUEIDENTIFIER NULL,
    [Description]           NVARCHAR(100) NULL,
    [Quantity]              MONEY NULL,
    [SerialNumber]          NVARCHAR(30) NULL,
    [UnitPrice]             MONEY NULL,
    [GrossAmount]           MONEY NULL,
    [DiscountAmount]        MONEY NULL,
    [AmountExclTax]         MONEY NULL,
    [TaxAmount]             MONEY NULL,
    [AmountInclTax]         MONEY NULL,
    [IsActive]              BIT CONSTRAINT [DF_SalesCreditLine_IsActive] DEFAULT((1)) NOT NULL,
    [CreatedOn]             DATETIME2 CONSTRAINT [DF_SalesCreditLine_CreatedOn] DEFAULT (sysutcdatetime())  NOT NULL,
    [ModifiedOn]            DATETIME2 CONSTRAINT [DF_SalesCreditLine_ModifiedOn] DEFAULT (sysutcdatetime())  NOT NULL,
    CONSTRAINT              [PK_SalesCreditLine_Id] PRIMARY KEY CLUSTERED ([Id] ASC),
    CONSTRAINT              [FK_SalesCreditLine_SalesCredit] FOREIGN KEY (SalesCreditId) REFERENCES [dbo].[SalesCredit](Id),
    CONSTRAINT              [FK_SalesCreditLine_Product] FOREIGN KEY ([ProductId]) REFERENCES [dbo].[Product](Id)
)
GO

CREATE UNIQUE INDEX IX_SalesCreditLine_SalesCreditId_LineNo
ON [dbo].[SalesCreditLine] ([SalesCreditId], [Sequence])
GO