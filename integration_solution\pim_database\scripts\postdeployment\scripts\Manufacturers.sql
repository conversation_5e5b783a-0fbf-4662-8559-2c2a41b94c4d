﻿DECLARE @manufacturers TABLE (
       Code NVARCHAR(20), 
       Name NVARCHAR(100))

INSERT INTO @manufacturers VALUES
       ('MFR-000001', '<PERSON>ia'),
       ('MFR-000002', 'Widex'),
       ('MFR-000003', 'Connex')

 MERGE dbo.Manufacturer AS Target
 USING @manufacturers AS Source
    ON Source.Code = Target.Code

  WHEN NOT MATCHED BY Target THEN
INSERT (Code, [Name])
VALUES (Source.Code, Source.[Name])

 WHEN MATCHED AND (Source.Name != Target.Name) THEN
UPDATE
   SET Target.Name = Source.Name
;


DECLARE @mfrCoupling TABLE (Code NVARCHAR(20), ExternalCode NVARCHAR(255))
INSERT INTO @mfrCoupling VALUES 
('MFR-000001', 'SI'),
('MFR-000002', 'W'),
('MFR-000003', 'CX');

  WITH Source AS (
SELECT (SELECT TOP 1 Id FROM dbo.ExternalSystem WHERE Code = 'PIM') AS ExternalSystemId,
       (SELECT TOP 1 Id FROM dbo.Entity WHERE Code = 'MANUFACTURER') AS EntityId,
       Manufacturer.Id AS RecordId,
       m1.ExternalCode AS ExternalRecordId
  FROM dbo.Manufacturer
 INNER JOIN @mfrCoupling AS m1
    ON Manufacturer.Code = m1.Code
)

INSERT INTO dbo.Coupling (ExternalSystemId, EntityId, RecordId, ExternalRecordId)
SELECT ExternalSystemId, EntityId, RecordId, ExternalRecordId
  FROM Source

WHERE NOT EXISTS (SELECT * FROM dbo.Coupling 
                   WHERE ExternalSystemId = Source.ExternalSystemId
                     AND EntityId = Source.EntityId
                     AND RecordId = Source.RecordId)