using Moq;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using WSA.Retail.Integration.Configuration;
using WSA.Retail.Integration.Data;
using WSA.Retail.Integration.Data.Repositories;
using WSA.Retail.Integration.Models.Configuration;
using WSA.Retail.Integration.Models.Couplings;
using WSA.Retail.Integration.Tests.Configuration;
using WSA.Retail.Integration.Data.Repositories.Interfaces;

namespace WSA.Retail.Integration.Tests.Data.Repositories;

public class CouplingRepositoryTests
{
    private readonly string _dbName;

    private readonly Mock<IOptions<AppSettings>> _optionsMock;
    private readonly Mock<ILogger<CouplingRepository>> _loggerMock;
    private readonly Mock<IDbContextFactory<IntegrationContext>> _dbContextFactoryMock;
    
    private readonly CouplingRepository _repository;

    public CouplingRepositoryTests()
    {
        _optionsMock = new Mock<IOptions<AppSettings>>();
        _optionsMock.Setup(o => o.Value).Returns(TestAppSettings.CreateDefault());
        _loggerMock = new Mock<ILogger<CouplingRepository>>();

        _dbName = $"TestDb_{Guid.NewGuid()}";
        _dbContextFactoryMock = new Mock<IDbContextFactory<IntegrationContext>>();
        _dbContextFactoryMock
            .Setup(f => f.CreateDbContext())
            .Returns(() => TestDbContextHelper.GetInMemoryDbContext(_dbName));

        _repository = new CouplingRepository(
            _optionsMock.Object,
            _loggerMock.Object,
            _dbContextFactoryMock.Object);

        SeedDatabase();
    }

    private void SeedDatabase()
    {
        using var context = TestDbContextHelper.GetInMemoryDbContext(_dbName);

        // Add couplings with various patterns
        var couplings = new List<CouplingEntity>
            {
                // Basic coupling for simple tests
                new() {
                    Id = Guid.NewGuid(),
                    ExternalSystemId = TestReferenceData.TestSystem1Id,
                    EntityId = TestReferenceData.PatientId,
                    RecordId = Guid.Parse("aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa"),
                    ExternalRecordId = "EXT1-001",
                    CreatedOn = DateTime.UtcNow.AddDays(-10),
                    ModifiedOn = DateTime.UtcNow.AddDays(-5)
                },
                
                // Same entity with different external system
                new() {
                    Id = Guid.NewGuid(),
                    ExternalSystemId = TestReferenceData.TestSystem2Id,
                    EntityId = TestReferenceData.PatientId,
                    RecordId = Guid.Parse("aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa"),
                    ExternalRecordId = "EXT2-001",
                    CreatedOn = DateTime.UtcNow.AddDays(-8),
                    ModifiedOn = DateTime.UtcNow.AddDays(-4)
                },
            };

        context.CouplingEntity.AddRange(couplings);
        context.SaveChanges();
    }


    [Fact]
    public async Task GetAsync_GetByIdShouldReturnCoupling()
    {
        // Act
        var result = await _repository.GetAsync(
            "TEST1",
            EntityType.Patient.GetEntityCode(),
            recordId: Guid.Parse("aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa"));

        // Assert
        Assert.NotNull(result);
        Assert.Equal("EXT1-001", result.ExternalCode);
    }

    
    [Fact]
    public async Task GetAsync_GetByExternalCodeShouldReturnCoupling()
    {
        var dbName = Guid.NewGuid().ToString();
        using var dbContext = TestDbContextHelper.GetInMemoryDbContext(dbName);

        // Seed test data
        var testPatients = await TestDataLoader.LoadPatients(dbContext, true);
        var firstPatient = await dbContext.PatientEntity.FirstOrDefaultAsync();
        Assert.NotNull(firstPatient); // Ensure test data is seeded
        Assert.NotNull(firstPatient.Code);

        // Act
        var repository = CreateRepository(dbName);
        var result = await repository.GetAsync(
            _appSettings.ExternalSystemCode,
            EntityType.Patient.GetEntityCode(),
            externalCode: firstPatient.Code);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(firstPatient.Id, result.RecordId);
    }

    [Fact]
    public async Task UpdateAsync_InsertAsyncShouldReturnTrue()
    {
        var dbName = Guid.NewGuid().ToString();
        using var dbContext = TestDbContextHelper.GetInMemoryDbContext(dbName);

        // Seed test data
        var testPatients = await TestDataLoader.LoadPatients(dbContext, false);
        var firstPatient = await dbContext.PatientEntity.FirstOrDefaultAsync();
        Assert.NotNull(firstPatient); // Ensure test data is seeded

        var entityId = await dbContext.EntityEntity
            .Where(x => x.Code == EntityType.Patient.GetEntityCode())
            .Select(x => x.Id)
            .FirstOrDefaultAsync();
        ArgumentNullException.ThrowIfNull(entityId);
        
        var externalSystemId = await dbContext.ExternalSystemEntity
            .Where(x => x.Code == _appSettings.ExternalSystemCode)
            .Select(x => x.Id)
            .FirstOrDefaultAsync();
        ArgumentNullException.ThrowIfNull(externalSystemId);

        // Initialize with dependency injection
        var repository = CreateRepository(dbName);

        // Create coupling dto and insert
        var coupling = new CouplingDto
        {
            Entity = new() { Id = entityId, Code = EntityType.Patient.GetEntityCode() },
            ExternalSystem = new() { Id = externalSystemId, Code = _appSettings.ExternalSystemCode },
            RecordId = firstPatient.Id,
            ExternalCode = "EXTERNALCODE"
        };
        var success = await repository.InsertAsync(coupling);

        // Assert
        Assert.True(success);
    }

    [Fact]
    public async Task UpdateAsync_UpdateAsyncShouldReturnTrue()
    {
        var dbName = Guid.NewGuid().ToString();
        using var dbContext = TestDbContextHelper.GetInMemoryDbContext(dbName);

        // Seed test data
        var testPatients = await TestDataLoader.LoadPatients(dbContext, true);  // Couplings are seeded
        var firstPatient = await dbContext.PatientEntity.FirstOrDefaultAsync();
        Assert.NotNull(firstPatient); // Ensure test data is seeded

        var entityId = await dbContext.EntityEntity
            .Where(x => x.Code == EntityType.Patient.GetEntityCode())
            .Select(x => x.Id)
            .FirstOrDefaultAsync();
        ArgumentNullException.ThrowIfNull(entityId);

        var externalSystemId = await dbContext.ExternalSystemEntity
            .Where(x => x.Code == _appSettings.ExternalSystemCode)
            .Select(x => x.Id)
            .FirstOrDefaultAsync();
        ArgumentNullException.ThrowIfNull(externalSystemId);

        var couplingId = await dbContext.CouplingEntity
            .Where(x => x.EntityId == entityId
                && x.ExternalSystemId == externalSystemId
                && x.RecordId == firstPatient.Id)
            .Select(x => x.Id)
            .FirstOrDefaultAsync();
        ArgumentNullException.ThrowIfNull(couplingId);

        // Initialize with dependency injection
        var repository = CreateRepository(dbName);

        // Create coupling dto and insert
        var coupling = new CouplingDto
        {
            Id = couplingId,
            Entity = new() { Id = entityId, Code = EntityType.Patient.GetEntityCode() },
            ExternalSystem = new() { Id = externalSystemId, Code = _appSettings.ExternalSystemCode },
            RecordId = firstPatient.Id,
            ExternalCode = "NEWEXTERNALCODE"
        };
        var success = await repository.UpdateAsync(coupling);

        // Assert
        Assert.True(success);
    }
    
}