﻿using WSA.Retail.Integration.Utilities;

namespace WSA.Retail.Integration.Manage.Models.Patients;

public class PatientEventHubEvent
{
    public Guid Id { get; set; }
    public required string PatientNumber { get; set;}
    public bool? PatientLead { get; set; }
    public string? FirstName { get; set; }
    public string? LastName { get; set; }
    public string? MiddleName { get; set; }
    public string? ShortName { get; set; }
    public DateTime? DateOfBirth { get; set; }
    public string? City { get; set; }
    public string? State { get; set; }
    public string? PostCode { get; set; }
    public string? MobilePhone { get; set; }
    public string? HomePhone { get; set; }
    public string? WorkPhone { get; set; }
    public string? PreferredPhone { get; set; }
    public string? FaxNumber { get; set; }
    public string? EmailAddress { get; set; }
    public bool? HadAppointmentOnly { get; set; }
    public Guid? GpId { get; set; }
    public Guid? GpPracticeId { get; set; }
    public string? Note { get; set; }
    public string? Employer { get; set; }
    public string? Occupation { get; set; }
    public string? HomeFlatNumber { get; set; }
    public string? Street { get; set; }
    public string? County { get; set; }
    public string? Address4 { get; set; }
    public string? Address5 { get; set; }
    public string? Suburb { get; set; }
    public Guid? LeadSourceId { get; set; }
    public Guid? CampaignId { get; set; }
    public Guid? TitleId { get; set; }
    public Guid? GenderId { get; set; }
    public Guid? PreferredLanguageId { get; set; }
    public Guid? EmploymentStatusId { get; set; }
    public Guid? LocationId { get; set; }
    public Guid? CountryId { get; set; }
    public Guid? SpecialistId { get; set; }
    public Guid? PatientStatusId { get; set; }
    public string? InitialOwner { get; set; }
    public string? InitialOwnerLocation { get; set; }

    public string? PatientCode
    {
        get
        {
            if (PatientNumber != null)
            {
                return Common.GetCode(PatientNumber, "PAT");
            }
            return null;
        }
    }

    public string? FullName
    {
        get
        {
            return ((FirstName + " " + MiddleName).Trim() + " " + LastName).Trim();
        }
    }
}