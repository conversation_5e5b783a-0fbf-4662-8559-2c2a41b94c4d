﻿CREATE TABLE [cosium].[typereglement] (
    [id]                NVARCHAR (50) NOT NULL,
    [codetypereglement] NVARCHAR (50) NULL,
    [description]       NVARCHAR (80) NULL,
    [typerg]            NVARCHAR (50) NULL,
    [datecreation]      NVARCHAR (50) NULL,
    [datemodif]         NVARCHAR (50) NULL,
    [CreatedOn]         DATETIME2 CONSTRAINT [DF_typereglement_CreatedOn] DEFAULT (sysutcdatetime()) NOT NULL,
    [ModifiedOn]        DATETIME2 CONSTRAINT [DF_typereglement_ModifiedOn] DEFAULT (sysutcdatetime()) NOT NULL,
    CONSTRAINT [PK_typereglement] PRIMARY KEY CLUSTERED ([id] ASC)
);
GO

CREATE INDEX IX_typereglement_codefournisseur
ON [cosium].typereglement (codetypereglement)
GO

CREATE INDEX IX_typereglement_ModifiedOn
ON [cosium].typereglement ([ModifiedOn])
GO

CREATE TRIGGER [cosium].[typereglement_UpdateModified]
ON [cosium].[typereglement]
AFTER UPDATE 
AS
   UPDATE [cosium].[typereglement]
   SET [ModifiedOn] = sysutcdatetime()
   FROM Inserted AS i
   WHERE [cosium].[typereglement].[id] = i.[id]
GO