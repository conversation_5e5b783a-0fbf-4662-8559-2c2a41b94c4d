﻿CREATE TABLE [pim].[Color]
(
    [Id]                    UNIQUEIDENTIFIER CONSTRAINT [DF_Color_Id] DEFAULT NEWID() NOT NULL,
    [Code]                  NVARCHAR(20) NOT NULL,
    [Name]                  NVARCHAR(100) NULL,
    [HexCode]               NVARCHAR(20) NULL,
    [SystemCreatedOn]       DATETIME2 CONSTRAINT [DF_Color_SystemCreatedOn] DEFAULT (sysutcdatetime()) NOT NULL,
    [SystemModifiedOn]      DATETIME2 CONSTRAINT [DF_Color_SystemModifiedOn] DEFAULT (sysutcdatetime()) NOT NULL,
    CONSTRAINT              [PK_Color_Id] PRIMARY KEY CLUSTERED ([Id] ASC)
)
GO

CREATE UNIQUE INDEX IX_Color_Code
ON [pim].[Color] ([Code])
GO