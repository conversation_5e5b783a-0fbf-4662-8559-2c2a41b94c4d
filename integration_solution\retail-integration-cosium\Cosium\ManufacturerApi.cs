﻿using Microsoft.Extensions.Logging;
using System.Text;
using System.Text.Json;

namespace WSA.Retail.Integration.Cosium
{
    public static class ManufacturerApi
    {

        public static async Task PostManufaturersAsync(List<Manufacturer> manufacturer, ILogger _logger)
        {
            _logger.LogInformation("[Cosium].[ProcessManufacture].[PostManufaturersAsync]  Procedure started");
            HttpClient patient = Common.GetHttpClient();
            try
            {
                foreach (var re in manufacturer)
                {

                    var manufacturerRequestContent = new StringContent(JsonSerializer.Serialize(re, Common.GetJsonOptions()), Encoding.UTF8, "application/json");

                    var manufacturerPostRequest = new HttpRequestMessage(HttpMethod.Post, "manufacturers")
                    {
                        Content = manufacturerRequestContent
                    };

                    HttpResponseMessage manufacturerResponse = await patient.SendAsync(manufacturerPostRequest);
                    _logger.LogTrace("[Cosium].[ProcessProduct].[Post] Returning response:\r\n{object}",
                      JsonSerializer.Serialize(manufacturerResponse, Common.GetJsonOptions()));
                    if (manufacturerResponse.StatusCode == System.Net.HttpStatusCode.OK)
                    {
                        _logger.LogInformation("[Cosium].[ProcessManufacturer].[Post] completed for patient : " + JsonSerializer.Serialize(re));
                    }
                    else
                    {
                        _logger.LogTrace("[Cosium].[ProcessManufacturer].[Post] Attempted to post fail for manufacturer" + JsonSerializer.Serialize(re));
                    }

                }
            }
            catch (Exception ex)
            {
                _logger.LogTrace("[Cosium].[ProcessManufacturer].[Post] Attempt to post the manufacturer to API generated an exception.");
                _logger.LogError(ex, "[Cosium].[ProcessManufacturer].[Post] Encountered an exception:\r\n{object}", ex.Message);
            }
        }
    }
}
