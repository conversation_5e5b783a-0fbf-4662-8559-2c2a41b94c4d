namespace WSA.Integration;
using Microsoft.Inventory.Item;

codeunit 50130 "Model Events"
{
    TableNo = "Model";

    trigger OnRun()
    begin
        RaiseEvent(Rec);
    end;


    [EventSubscriber(ObjectType::Table, Database::"Model", OnAfterInsertEvent, '', true, true)]
    local procedure SendEventOnAfterInsertEvent(
        var Rec: Record "Model";
        RunTrigger: Boolean)

    var
        RetailIntegrationSetup: Record "Retail Integration Setup";

    begin
        RetailIntegrationSetup.SafeGet();
        if not RetailIntegrationSetup.Enabled then
            exit;

        if Rec.IsTemporary then
            exit;

        if RunTrigger then begin
            if RetailIntegrationSetup."Handle Events Asyncronously" then
                RaiseEventAsync(Rec)
            else
                RaiseEvent(Rec);
        end;
    end;


    [EventSubscriber(ObjectType::Table, Database::"Model", OnAfterModifyEvent, '', true, true)]
    local procedure SendEventOnAfterModifyEvent(
        var Rec: Record "Model";
        var xRec: Record "Model";
        RunTrigger: Boolean)

    var
        RetailIntegrationSetup: Record "Retail Integration Setup";

    begin
        RetailIntegrationSetup.SafeGet();
        if not RetailIntegrationSetup.Enabled then
            exit;

        if Rec.IsTemporary then
            exit;

        if RunTrigger then begin
            if RetailIntegrationSetup."Handle Events Asyncronously" then
                RaiseEventAsync(Rec)
            else
                RaiseEvent(Rec);
        end;
    end;

    local procedure RaiseEventAsync(Model: Record Model)
    var
        SessionId: Integer;

    begin
        Session.StartSession(SessionId, Codeunit::"Model Events", CompanyName, Model);
    end;

    local procedure RaiseEvent(Model: Record Model)
    var
        RetailIntegrationSetup: Record "Retail Integration Setup";
        IntegrationManagement: Codeunit "Integration Management";
        Common: Codeunit "WSA Common";
        JObject: JsonObject;

    begin
        RetailIntegrationSetup.SafeGet();
        if not RetailIntegrationSetup.Enabled then
            exit;

        if Model."Code" = '' then
            exit;

        if Model.Name = '' then
            exit;

        if IsNullGuid(Model.SystemId) then
            exit;

        JObject := Common.ModelToJson(Model);

        if not IntegrationManagement.TryRaiseEvent(JObject, 'productmodels', Format(Model.SystemId).TrimStart('{').TrimEnd('}')) then
            exit;
    end;
}