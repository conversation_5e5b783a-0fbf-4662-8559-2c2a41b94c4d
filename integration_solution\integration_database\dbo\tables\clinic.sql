﻿CREATE TABLE [dbo].[Clinic]
(
    [Id]                UNIQUEIDENTIFIER CONSTRAINT [DF_Clinic_Id] DEFAULT NEWID() NOT NULL,
    [Code]              NVARCHAR(20) NOT NULL,
    [Name]              NVARCHAR(100) NULL,
    [AlternateCode]     NVARCHAR(50) NULL,
    [Address]           NVARCHAR(100) NULL,
    [Address2]          NVARCHAR(50) NULL,
    [City]              NVARCHAR(30) NULL,
    [Region]            NVARCHAR(30) NULL,
    [Country]           NVARCHAR(10) NULL,
    [PostalCode]        NVARCHAR(20) NULL,
    [Phone]             NVARCHAR(30) NULL,
    [Email]             NVARCHAR(50) NULL,
    [CompanyId]         UNIQUEIDENTIFIER NULL,
    [CreatedOn]         DATETIME2 CONSTRAINT [DF_Clinic_CreatedOn] DEFAULT (sysutcdatetime()) NOT NULL,
    [ModifiedOn]        DATETIME2 CONSTRAINT [DF_Clinic_ModifiedOn] DEFAULT (sysutcdatetime()) NOT NULL,
    CONSTRAINT          [PK_Clinic_Id] PRIMARY KEY CLUSTERED ([Id] ASC),
    CONSTRAINT          [FK_Clinic_Company] FOREIGN KEY (CompanyId) REFERENCES [dbo].[Company](Id),
)
GO

CREATE UNIQUE INDEX IX_Clinic_Code
ON [dbo].[Clinic] ([Code])
GO

CREATE INDEX IX_Clinic_Name
ON [dbo].[Clinic] ([Name])
GO