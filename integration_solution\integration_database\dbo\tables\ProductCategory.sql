﻿CREATE TABLE [dbo].[ProductCategory]
(
    [Id]                UNIQUEIDENTIFIER CONSTRAINT [DF_ProductCategory_Id] DEFAULT NEWID() NOT NULL,
    [Code]              NVARCHAR(20) NOT NULL,
    [Name]              NVARCHAR(100) NULL,
    [AlternateCode]     NVARCHAR(50) NULL,
    [IsHearingAid]      BIT NULL,
    [IsInventory]       BIT NULL,
    [IsSerialized]      BIT NULL,
    [ParentId]          UNIQUEIDENTIFIER NULL,
    [CreatedOn]         DATETIME2 CONSTRAINT [DF_ProductCategory_CreatedOn] DEFAULT (sysutcdatetime()) NOT NULL,
    [ModifiedOn]        DATETIME2 CONSTRAINT [DF_ProductCategory_ModifiedOn] DEFAULT (sysutcdatetime()) NOT NULL,
    CONSTRAINT          [PK_ProductCategory_Id] PRIMARY KEY CLUSTERED ([Id] ASC),
    CONSTRAINT          [FK_ProductCategory_ParentId] FOREIGN KEY (ParentId) REFERENCES [dbo].[ProductCategory](Id)
)
GO

CREATE UNIQUE INDEX IX_ProductCategory_Code
ON [dbo].[ProductCategory] ([Code])
GO

CREATE INDEX IX_ProductCategory_Name
ON [dbo].[ProductCategory] ([Name])
GO