﻿CREATE PROCEDURE [pim].[AttributeUpsert]
(
    @source [pim].[AttributeUpsertTableType] READONLY,

    -- Add output parameters for diagnostics
    @rowsReceived INT = NULL OUTPUT,
    @diagnosticMessage NVARCHAR(MAX) = NULL OUTPUT
)
AS
BEGIN
    DECLARE @rowCount INT;
    DECLARE @DistinctAttrCount INT;
    DECLARE @currentAttributeCount INT;
    DECLARE @currentProductAttributeCount INT;
    DECLARE @firstMergeAffected INT;
    DECLARE @secondMergeAffected INT;
    DECLARE @finalAttributeCount INT;
    DECLARE @finalProductAttributeCount INT;
    DECLARE @diagnosticLog NVARCHAR(MAX) = '';
    
    -- Populate variables
    SELECT @rowCount = COUNT(*) FROM @source;
    SET @rowsReceived = @rowCount;
    SELECT @DistinctAttrCount = COUNT(DISTINCT Code) FROM @source;
    SELECT @currentAttributeCount = COUNT(*) FROM pim.Attribute;
    SELECT @currentProductAttributeCount = COUNT(*) FROM pim.ProductAttribute;
    
    -- Build diagnostic info
    SET @diagnosticLog = 'Received ' + CAST(@rowCount AS VARCHAR(20)) + ' rows in @source parameter' + CHAR(13) + CHAR(10) +
                        'Distinct attribute codes in @source: ' + CAST(@DistinctAttrCount AS VARCHAR(20)) + CHAR(13) + CHAR(10) +
                        'Current pim.Attribute count: ' + CAST(@currentAttributeCount AS VARCHAR(20)) + CHAR(13) + CHAR(10) +
                        'Current pim.ProductAttribute count: ' + CAST(@currentProductAttributeCount AS VARCHAR(20));
    
    BEGIN TRY
        SET XACT_ABORT ON
        BEGIN TRANSACTION
            -- First MERGE: Attributes
            MERGE pim.Attribute AS Target
            USING (SELECT DISTINCT Code, [Name], DataType FROM @source) AS Source
               ON Source.Code = Target.Code
             WHEN NOT MATCHED BY Target THEN
                  INSERT (Code,
                          [Name],
                          DataType)
                  VALUES (Source.Code,
                          Source.[Name],
                          Source.DataType)
             WHEN MATCHED AND (
                         (Target.[Name] <> Source.[Name]) OR (Target.[Name] IS NULL AND Source.[Name] IS NOT NULL)
                      OR (Target.DataType <> Source.DataType) OR (Target.DataType IS NULL AND Source.DataType IS NOT NULL)
                      )
             THEN UPDATE 
                     SET Name = Source.[Name],
                         DataType = Source.DataType,
                         SystemModifiedOn = sysutcdatetime();
                         
            SET @firstMergeAffected = @@rowCount;
            SET @diagnosticLog = @diagnosticLog + CHAR(13) + CHAR(10) + 'Rows affected by first MERGE: ' + CAST(@firstMergeAffected AS VARCHAR(20));
            
            -- Second MERGE: ProductAttributes
            MERGE pim.ProductAttribute AS Target
            USING (SELECT s1.ProductId,
                          Attribute.Id AS AttributeId,
                          s1.[Value]
                     FROM (SELECT DISTINCT ProductId, Code, [Value] FROM @source) AS s1
                    INNER JOIN pim.Attribute ON s1.Code = Attribute.Code) AS Source
               ON Source.ProductId = Target.ProductId
              AND Source.AttributeId = Target.AttributeId
             WHEN NOT MATCHED BY Target THEN
                  INSERT (ProductId,
                          AttributeId,
                          [Value])
                  VALUES (Source.ProductId,
                          Source.AttributeId,
                          Source.[Value])
             WHEN MATCHED AND (
                         (Target.[Value] <> Source.[Value]) 
                      OR (Target.[Value] IS NULL AND Source.[Value] IS NOT NULL)
                      )
             THEN UPDATE 
                     SET [Value] = Source.[Value],
                         SystemModifiedOn = sysutcdatetime();
                        
            SET @secondMergeAffected = @@rowCount;
            SET @diagnosticLog = @diagnosticLog + CHAR(13) + CHAR(10) + 'Rows affected by second MERGE: ' + CAST(@secondMergeAffected AS VARCHAR(20));
            
        COMMIT TRANSACTION;
        
        -- Final counts
        SELECT @finalAttributeCount = COUNT(*) FROM pim.Attribute;
        SELECT @finalProductAttributeCount = COUNT(*) FROM pim.ProductAttribute;
        SET @diagnosticLog = @diagnosticLog + CHAR(13) + CHAR(10) + 'Final pim.Attribute count: ' + CAST(@finalAttributeCount AS VARCHAR(20)) + '. '
                            + CHAR(13) + CHAR(10) + 'Final pim.ProductAttribute count: ' + CAST(@finalProductAttributeCount AS VARCHAR(20)) + '. ';
        
    END TRY
    BEGIN CATCH
        IF @@TRANCOUNT > 0
            ROLLBACK TRANSACTION;
            
        SET @diagnosticLog = @diagnosticLog + CHAR(13) + CHAR(10) + 'Error occurred: ' + ERROR_MESSAGE();
        SET @diagnosticMessage = @diagnosticLog;
        THROW;
    END CATCH
    
    SET @diagnosticMessage = @diagnosticLog;
END