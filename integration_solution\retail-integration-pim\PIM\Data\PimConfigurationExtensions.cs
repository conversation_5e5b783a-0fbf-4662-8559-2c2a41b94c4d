﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using WSA.Retail.Integration.Core;
using WSA.Retail.Integration.PIM.Core;

namespace WSA.Retail.Integration.PIM.Data;

public static class EntityConfigurationExtensions
{
    public static EntityTypeBuilder<T> ConfigureIdentifiableFields<T>(this EntityTypeBuilder<T> builder)
        where T : class, Integration.Core.IIdentifiable
    {
        builder.Property(x => x.Id)
            .HasColumnName("Id")
            .HasColumnType("uniqueidentifier")
            .IsRequired(true);

        return builder;
    }

    public static EntityTypeBuilder<T> ConfigureCodeIdentifiableFields<T>(this EntityTypeBuilder<T> builder)
        where T : class, ICodeIdentifiable
    {
        builder.Property(x => x.Code)
            .HasColumnName("Code")
            .HasColumnType("nvarchar(20)");

        return builder;
    }

    public static EntityTypeBuilder<T> ConfigureNamableFields<T>(this EntityTypeBuilder<T> builder)
        where T : class, INameable
    {
        builder.Property(x => x.Name)
            .HasColumnName("Name")
            .HasColumnType("nvarchar(100)");

        return builder;
    }

    public static EntityTypeBuilder<T> ConfigureAuditInfoFields<T>(this EntityTypeBuilder<T> builder)
        where T : class, PIM.Core.IAuditInfo
    {
        builder.Property(x => x.SystemCreatedOn)
            .HasColumnName("SystemCreatedOn")
            .HasColumnType("datetime2(7)")
            .IsRequired(true)
            .ValueGeneratedOnAdd();

        builder.Property(x => x.SystemModifiedOn)
            .HasColumnName("SystemModifiedOn")
            .HasColumnType("datetime2(7)")
            .IsRequired(true)
            .ValueGeneratedOnAdd();

        return builder;
    }

    public static EntityTypeBuilder<T> ConfigureIntegrationFields<T>(this EntityTypeBuilder<T> builder)
        where T : class, IIntegrationEntity
    {
        builder.Property(x => x.IntegrationRequired)
            .HasColumnName("IntegrationRequired")
            .HasColumnType("bit")
            .IsRequired(true)
            .ValueGeneratedOnAdd();

        builder.Property(x => x.IntegratedOn)
            .HasColumnName("IntegratedOn")
            .HasColumnType("datetime2(7)");

        return builder;
    }
}