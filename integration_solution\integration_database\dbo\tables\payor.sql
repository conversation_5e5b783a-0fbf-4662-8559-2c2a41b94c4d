﻿CREATE TABLE [dbo].[Payor]
(
    [Id]                UNIQUEIDENTIFIER CONSTRAINT [DF_Payor_Id] DEFAULT NEWID() NOT NULL,
    [Code]              NVARCHAR(20) NOT NULL,
    [Name]              NVARCHAR(100) NULL,
    [AlternateCode]     NVARCHAR(50) NULL,
    [Address]           NVARCHAR(100) NULL,
    [Address2]          NVARCHAR(50) NULL,
    [City]              NVARCHAR(30) NULL,
    [Region]            NVARCHAR(30) NULL,
    [Country]           NVARCHAR(10) NULL,
    [PostalCode]        NVARCHAR(20) NULL,
    [Phone]             NVARCHAR(30) NULL,
    [Email]             NVARCHAR(50) NULL,
    [AccountNo]         NVARCHAR(20) NULL,
    [CreatedOn]         DATETIME2 CONSTRAINT [DF_Payor_CreatedOn] DEFAULT (sysutcdatetime()) NOT NULL,
    [ModifiedOn]        DATETIME2 CONSTRAINT [DF_Payor_ModifiedOn] DEFAULT (sysutcdatetime()) NOT NULL,
    CONSTRAINT          [PK_Payor_Id] PRIMARY KEY CLUSTERED ([Id] ASC)
)
GO

CREATE UNIQUE INDEX IX_Payor_Code
ON [dbo].[Payor] ([Code])
GO

CREATE INDEX IX_Payor_Name
ON [dbo].[Payor] ([Name])
GO