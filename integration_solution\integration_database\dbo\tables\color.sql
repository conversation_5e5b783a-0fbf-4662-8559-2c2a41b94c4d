﻿CREATE TABLE [dbo].[Color]
(
    [Id]                UNIQUEIDENTIFIER CONSTRAINT [DF_Color_Id] DEFAULT NEWID() NOT NULL,
    [Code]              NVARCHAR(20) NOT NULL,
    [Name]              NVARCHAR(100) NULL,
    [AlternateCode]     NVARCHAR(50) NULL,
    [HexCode]           NVARCHAR(20) NULL,
    [CreatedOn]         DATETIME2 CONSTRAINT [DF_Color_CreatedOn] DEFAULT (sysutcdatetime()) NOT NULL,
    [ModifiedOn]        DATETIME2 CONSTRAINT [DF_Color_ModifiedOn] DEFAULT (sysutcdatetime()) NOT NULL,
    CONSTRAINT          [PK_Color_Id] PRIMARY KEY CLUSTERED ([Id] ASC)
)
GO

CREATE UNIQUE INDEX IX_Color_Code
ON [dbo].[Color] ([Code])
GO

CREATE INDEX IX_Color_Name
ON [dbo].[Color] ([Name])
GO