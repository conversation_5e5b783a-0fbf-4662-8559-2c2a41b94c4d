﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.Functions.Worker;
using WSA.Retail.Integration.Events;
using WSA.Retail.Integration.Manage.EventProcessing;

namespace WSA.Retail.Integration.Manage.Models.Vendors;

public class VendorEventHandler(
    VendorFromEventHandler fromEventHandler,
    VendorToEventHandler toEventHandler,
    VendorGetByQueryHandler queryHandler) : IEventHandler
{
    private readonly VendorFromEventHandler _fromEventHandler = fromEventHandler;
    private readonly VendorToEventHandler _toEventHandler = toEventHandler;
    private readonly VendorGetByQueryHandler _queryHandler = queryHandler;

    public async Task<bool> HandleFromQueueAsync(EventHubMessage message)
    {
        return await _fromEventHandler.HandleFromQueueAsync(message);
    }

    public async Task<bool> HandleToQueueAsync(Event ev)
    {
        return await _toEventHandler.HandleToQueueAsync(ev);
    }


    [Function("VendorGetByQuery")]
    public async Task<IActionResult> GetByQueryAsync([HttpTrigger(AuthorizationLevel.Function, "get", Route = "vendors")] HttpRequest req)
    {
        return await _queryHandler.GetByQueryInternalAsync(req);
    }
}