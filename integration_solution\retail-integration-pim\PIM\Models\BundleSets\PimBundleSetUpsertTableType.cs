﻿namespace WSA.Retail.Integration.PIM.Models.BundleSets;

public class PimBundleSetUpsertTableType
{
    public Guid BundleId { get; set; }
    public string? BundleName { get; set; }
    public string? BundleType { get; set; }
    public bool BundleIsDefault { get; set; }
    public bool BundleIsMandatory { get; set; }
    public bool BundleIsMasterData { get; set; }
    public int BundleRanking { get; set; }
    public string? BundleState { get; set; }
    public DateTime BundleCreatedAt { get; set; }
    public DateTime BundleUpdatedAt { get; set; }
    public Guid ProductId { get; set; }
    public bool ProductIsAvailable { get; set; }
    public bool ProductIsPhasedOut { get; set; }
    public bool ProductIsDefault { get; set; }
    public int ProductRanking { get; set; }
    public string? ProductSku { get; set; }
    public string? ProductState { get; set; }
    public DateTime ProductCreatedAt { get; set; }
    public DateTime ProductUpdatedAt { get; set; }
}