﻿CREATE TABLE [dbo].[PurchaseOrderLine]
(
    [Id]                    UNIQUEIDENTIFIER CONSTRAINT [DF_PurchaseOrderLine_Id] DEFAULT NEWID() NOT NULL,
    [PurchaseOrderId]       UNIQUEIDENTIFIER NOT NULL,
    [Sequence]              INT NOT NULL,
    [ProductId]             UNIQUEIDENTIFIER NULL,
    [Description]           NVARCHAR(100) NULL,
    [Quantity]              MONEY NULL,
    [UnitPrice]             MONEY NULL,
    [GrossAmount]           MONEY NULL,
    [DiscountAmount]        MONEY NULL,
    [AmountExclTax]         MONEY NULL,
    [TaxAmount]             MONEY NULL,
    [AmountInclTax]         MONEY NULL,
    [IsActive]              BIT CONSTRAINT [DF_PurchaseOrderLine_IsActive] DEFAULT((1)) NOT NULL,
    [CreatedOn]             DATETIME2 CONSTRAINT [DF_PurchaseOrderLine_CreatedOn] DEFAULT (sysutcdatetime())  NOT NULL,
    [ModifiedOn]            DATETIME2 CONSTRAINT [DF_PurchaseOrderLine_ModifiedOn] DEFAULT (sysutcdatetime())  NOT NULL,
    CONSTRAINT              [PK_PurchaseOrderLine_Id] PRIMARY KEY CLUSTERED ([Id] ASC),
    CONSTRAINT              [FK_PurchaseOrderLine_PurchaseOrder] FOREIGN KEY (PurchaseOrderId) REFERENCES [dbo].[PurchaseOrder](Id) ON DELETE CASCADE,
    CONSTRAINT              [FK_PurchaseOrderLine_Product] FOREIGN KEY ([ProductId]) REFERENCES [dbo].[Product](Id)
)
GO

CREATE UNIQUE INDEX IX_PurchaseOrderLine_PurchaseOrderId_LineNo
ON [dbo].[PurchaseOrderLine] ([PurchaseOrderId], [Sequence])
GO