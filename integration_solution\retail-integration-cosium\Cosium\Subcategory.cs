﻿using System.Text.Json;
using System.Text.Json.Nodes;
using WSA.Retail.Integration.Model;

namespace WSA.Retail.Integration.Cosium
{
    internal class Subcategory : Model.Subcategory
    {
        internal Subcategory() { }
        internal Subcategory(string requestBody) : base(requestBody) { }

        internal static Dictionary<string, Subcategory> CachedData = [];

        private static HttpClient? _client;

        internal void AddCachedData()
        {
            ArgumentNullException.ThrowIfNull(this.ExternalCode);
            CachedData.TryGetValue(this.ExternalCode, out var oldData);
            if (oldData == null)
            {
                CachedData.Add(this.ExternalCode, this);
            }
            else
            {
                if (HasChanged(oldData, this))
                {
                    CachedData[this.ExternalCode] = this;
                }
            }
        }

        internal static async Task<Subcategory?> GetAsync(string? code = null, string? externalCode = null)
        {
            Subcategory? subcategory = null;
            HttpRequestMessage request;
            _client ??= Common.GetHttpClient();
            if (code != null)
            {
                subcategory = CachedData.Values.Where(x => x.Code == code).FirstOrDefault();
                if (subcategory != null)
                {
                    return subcategory;
                }
                request = new HttpRequestMessage(HttpMethod.Get, "subcategories?code=" + code);
            }
            else
            {
                ArgumentNullException.ThrowIfNull(externalCode);
                CachedData.TryGetValue(externalCode, out subcategory);
                if (subcategory != null)
                {
                    return subcategory;
                }
                request = new HttpRequestMessage(HttpMethod.Get, "subcategories?externalCode=" + externalCode);
            }

            var response = await _client.SendAsync(request);
            if (response != null)
            {
                var responseBody = await response.Content.ReadAsStringAsync();
                switch (response.StatusCode)
                {
                    case System.Net.HttpStatusCode.OK:

                        if (responseBody != null)
                        {
                            var jsonArray = JsonNode.Parse(responseBody)?.AsArray();
                            if (jsonArray != null)
                            {
                                List<Subcategory?> records = [];
                                foreach (JsonNode? json in jsonArray)
                                {
                                    if (json != null)
                                    {
                                        Subcategory record = new(json.ToJsonString(Common.GetJsonOptions()));
                                        record.AddCachedData();
                                        records.Add(record);
                                    }
                                }
                                if (records != null)
                                {
                                    return records.FirstOrDefault();
                                }
                            }
                        }
                        break;

                    case System.Net.HttpStatusCode.NoContent:
                        return null;

                    default: return null;
                }
            }
            return null;
        }
    }
}
