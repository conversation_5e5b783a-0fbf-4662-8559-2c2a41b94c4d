namespace app.app;

using Microsoft.Inventory.Location;

page 50200 "API V1 Clinic"
{
    APIGroup = 'apiGroup';
    APIPublisher = 'publisherName';
    APIVersion = 'v1.0';
    ApplicationArea = All;
    Caption = 'apiV1Clinic';
    DelayedInsert = true;
    EntityName = 'entityName';
    EntitySetName = 'entitySetName';
    PageType = API;
    SourceTable = "Responsibility Center";
    
    layout
    {
        area(Content)
        {
            repeater(General)
            {
            }
        }
    }
}
