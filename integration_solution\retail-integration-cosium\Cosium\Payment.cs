﻿using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Logging;
using System.Data;
using System.Text.Json;
using System.Text;
using System.Text.Json.Nodes;

namespace WSA.Retail.Integration.Cosium
{
    public class Payment:Model.Payment
    {
        public Payment() { }

        public Payment(DataRow row) 
        {
            string? externalSystemCode = Environment.GetEnvironmentVariable("ExternalSystemCode");
            ArgumentNullException.ThrowIfNull(externalSystemCode);

            string? requestBody = row.IsNull("JSON") ? null : (string?)row["JSON"];
            if (requestBody != null)
            {
                JsonNode? json = JsonNode.Parse(requestBody);
                ArgumentNullException.ThrowIfNull(json);

                Id = (Guid?)json.AsObject()["id"];
                ExternalSystemCode = externalSystemCode;
                DocumentNumber = (string?)json.AsObject()["documentNumber"];
                ExternalReference = (string?)json.AsObject()["externalReference"];
                Patient = new()
                {
                    Id = (Guid?)json.AsObject()["patient"]?["id"],
                    Code = (string?)json.AsObject()["patient"]?["code"],
                    ExternalCode = (string?)json.AsObject()["patient"]?["externalCode"],
                    Name = (string?)json.AsObject()["patient"]?["name"]
                };

                Clinic = new()
                {
                    Id = (Guid?)json.AsObject()["clinic"]?["id"],
                    Code = (string?)json.AsObject()["clinic"]?["code"],
                    ExternalCode = (string?)json.AsObject()["clinic"]?["externalCode"],
                    Name = (string?)json.AsObject()["clinic"]?["name"]
                };
                SalesOrder = new()
                {
                    Id = (Guid?)json.AsObject()["salesOrder"]?["id"],
                    DocumentNumber = (string?)json.AsObject()["salesOrder"]?["documentNumber"],
                    ExternalReference = (string?)json.AsObject()["salesOrder"]?["externalReference"]
                };

                DocumentDate = DateOnly.FromDateTime((DateTime?)json.AsObject()["documentDate"] ?? DateTime.MinValue);
                PaymentMethod = (string?)json.AsObject()["paymentMethod"];
                Amount = (decimal?)json.AsObject()["amount"];
            }
        }

        private static HttpClient? _client;

        public static async Task ProcessNewRecordsAsync(ILogger log)
        {
            var scope = InitScope(nameof(ProcessNewRecordsAsync));
            log.BeginScope(scope);
            log.LogInformation("{procedureName} started", nameof(ProcessNewRecordsAsync));

            List<Payment?> payments = await GetFromDatabase(log);
            log.BeginScope(scope);
            log.LogInformation("Fetched {recordCount} records from Cosium database", payments?.Count);
            if (payments?.Count > 0)
            {
                await PostListAsync(payments, log);
            }
            else
            {
                log.BeginScope(scope);
                log.LogInformation("No new payments are avalable in the Cosium database.");
            }
        }

        private static async Task<List<Payment?>> GetFromDatabase(ILogger log)
        {
            var scope = InitScope(nameof(GetFromDatabase));
            log.BeginScope(scope);
            log.LogInformation("{procedureName} started", nameof(GetFromDatabase));

            List<Payment?> paymentList = [];

            string? connString = Environment.GetEnvironmentVariable("SqlConnectionString");
            ArgumentNullException.ThrowIfNull(connString);
            using SqlConnection conn = new(connString);
            {
                try
                {
                    conn.Open();
                    using SqlCommand cmd = conn.CreateCommand();
                    {
                        cmd.CommandText = "SELECT * FROM cosium.Payments " +
                                           "WHERE (IntegrationRequired = 1)";
                        using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                        {
                            if (reader.HasRows)
                            {
                                DataTable dt = new();
                                dt.Load(reader);
                                log.LogInformation("SQL reader returned {rowCount} rows", dt.Rows.Count);

                                foreach (DataRow dr in dt.Rows)
                                {
                                    Payment newRecord = new(dr);
                                    paymentList.Add(newRecord);
                                }
                                return paymentList;
                            }
                            else
                            {
                                log.LogInformation("No new payments were retrieved from the database.");
                            }
                        }
                        conn.Close();
                    }
                }
                catch (Exception ex)
                {
                    log.BeginScope(scope);
                    log.LogError(ex, "Attempt to fetch records from the database generated an exception:\r\n{object}", ex.Message);
                }
            }
            return paymentList;
        }

        public static async Task PostListAsync(List<Payment?> records, ILogger log)
        {
            var scope = InitScope(nameof(PostListAsync));
            log.BeginScope(scope);
            log.LogInformation("{procedureName} started", nameof(PostListAsync));

            try
            {
                foreach (var record in records)
                {
                    if (record != null)
                    {
                        await record.PostAsync(log);
                    }
                }
            }
            catch (Exception ex)
            {
                log.BeginScope(scope);
                log.LogError(ex, "Encountered an exception:\r\n{object}", ex.Message);
            }
        }

        public async Task<Payment?> PostAsync(ILogger log)
        {
            var scope = InitScope(nameof(PostAsync));
            AddEntityToScope(ref scope, this);
            log.BeginScope(scope);
            log.LogInformation("{procedureName} started", nameof(PostAsync));

            _client ??= Common.GetHttpClient();

            try
            {
                if (this != null)
                {
                    if (Clinic?.ExternalCode != null && Patient?.ExternalCode != null)
                    {
                        await ValidateExternalReferences(log);

                        var requestBody = JsonSerializer.Serialize(this, Common.GetJsonOptions());
                        var content = new StringContent(requestBody, Encoding.UTF8, "application/json");
                        var request = new HttpRequestMessage(HttpMethod.Post, "payments")
                        {
                            Content = content
                        };

                        log.LogInformation("Sending payment: {code} to API\r\n{requestBody}", this.DocumentNumber, requestBody);
                        HttpResponseMessage response = await _client.SendAsync(request);
                        var responseBody = await response.Content.ReadAsStringAsync();
                        log.LogInformation("API response:\r\n{status}\r\n{responseBody}",
                            response.StatusCode.ToString() + " - " + response.RequestMessage, responseBody);

                        if (response.StatusCode == System.Net.HttpStatusCode.OK)
                        {
                            log.LogInformation("Successfully updated payment");
                            UpdateIsIntegrated();
                            return JsonSerializer.Deserialize<Payment?>(responseBody, Common.GetJsonOptions());
                        }
                        else
                        {
                            log.LogError("Attempt to update payment: {code} failed\r\nMessage:{responseBody}",
                                this.DocumentNumber, responseBody);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                log.BeginScope(scope);
                log.LogError(ex, "Encountered an exception:\r\n{object}", ex.Message);
            }
            return null;
        }

        private bool UpdateIsIntegrated()
        {
            string? connString = Environment.GetEnvironmentVariable("SqlConnectionString");
            ArgumentNullException.ThrowIfNull(connString);
            ArgumentNullException.ThrowIfNull(this.ExternalReference);

            using SqlConnection conn = new(connString);
            {
                try
                {
                    conn.Open();
                    using SqlCommand cmd = conn.CreateCommand();
                    {
                        cmd.CommandType = CommandType.Text;
                        cmd.CommandText = "UPDATE cosium.Payments SET IntegrationRequired = 0, IntegrationDate = sysutcdatetime() WHERE Id = @id";
                        cmd.Parameters.AddWithValue("@id", this.ExternalReference);
                        cmd.ExecuteNonQuery();
                        conn.Close();
                        return true;
                    }
                }
                catch
                {
                    return false;
                }
            }
        }

        public async Task ValidateExternalReferences(ILogger log)
        {
            var scope = InitScope(nameof(ValidateExternalReferences));
            AddEntityToScope(ref scope, this);
            log.BeginScope(scope);
            log.LogInformation("{procedureName} started", nameof(ValidateExternalReferences));

            await Cosium.Clinic.ValidateExternalReference(Clinic);
            await Cosium.Patient.ValidateExternalReference(Patient);
        }

        private static Dictionary<string, object> InitScope(string procedureName)
        {
            return new Dictionary<string, object>() {
                { "appName", "Cosium" },
                { "className", nameof(Payment) },
                { "procedureName", procedureName }
            };
        }

        private static void AddEntityToScope(ref Dictionary<string, object> scope, Payment payment)
        {
            scope["entityName"] = "Payment";
            scope["entityCode"] = payment.DocumentNumber ?? "";
        }
    }
}