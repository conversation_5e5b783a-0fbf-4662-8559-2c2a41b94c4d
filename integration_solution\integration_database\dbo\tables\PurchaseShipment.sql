﻿CREATE TABLE [dbo].[PurchaseShipment]
(
    [Id]                UNIQUEIDENTIFIER CONSTRAINT [DF_PurchaseShipment_Id] DEFAULT NEWID() NOT NULL,
    [DocumentNumber]    NVARCHAR(20) NOT NULL,
    [PurchaseReturnId]  UNIQUEIDENTIFIER NULL,
    [VendorId]          UNIQUEIDENTIFIER NULL,
    [ClinicId]          UNIQUEIDENTIFIER NOT NULL,
    [DocumentDate]      DATE NULL,
    [IsActive]          BIT CONSTRAINT [DF_PurchaseShipment_IsActive] DEFAULT((1)) NOT NULL,
    [CreatedOn]         DATETIME2 CONSTRAINT [DF_PurchaseShipment_CreatedOn] DEFAULT (sysutcdatetime())  NOT NULL,
    [ModifiedOn]        DATETIME2 CONSTRAINT [DF_PurchaseShipment_ModifiedOn] DEFAULT (sysutcdatetime())  NOT NULL,
    CONSTRAINT          [PK_PurchaseShipment_Id] PRIMARY KEY CLUSTERED ([Id] ASC),
    CONSTRAINT          [FK_PurchaseShipment_PurchaseReturn] FOREIGN KEY (PurchaseReturnId) REFERENCES [dbo].[PurchaseReturn](Id),
    CONSTRAINT          [FK_PurchaseShipment_Vendor] FOREIGN KEY (VendorId) REFERENCES [dbo].[Vendor](Id),
    CONSTRAINT          [FK_PurchaseShipment_Clinic] FOREIGN KEY (ClinicId) REFERENCES [dbo].[Clinic](Id)
)
GO

CREATE UNIQUE INDEX IX_PurchaseShipment_DocumentNumber
ON [dbo].[PurchaseShipment] ([DocumentNumber])
GO