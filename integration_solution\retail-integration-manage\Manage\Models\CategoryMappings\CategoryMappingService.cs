﻿using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using WSA.Retail.Integration.Configuration;
using WSA.Retail.Integration.Logging;
using WSA.Retail.Integration.Manage.Data.Repositories.Interfaces;

namespace WSA.Retail.Integration.Manage.Models.CategoryMappings;

public class CategoryMappingService(
    IOptions<AppSettings> options,
    ILogger<CategoryMappingService> logger,
    ICategoryMappingRepository repository) : ICategoryMappingService
{
    private readonly AppSettings _appSettings = options.Value;
    private readonly ILogger<CategoryMappingService> _logger = logger;
    private readonly ICategoryMappingRepository _repository = repository;

    public async Task<CategoryMapping?> GetAsync(Guid categoryId)
    {
        _logger.LogMethodStart(_appSettings.AppName);
        var mapping = await _repository.GetAsync(categoryId);
        return mapping;
    }

    public async Task<CategoryMapping?> UpsertAsync(CategoryMapping categoryMapping)
    {
        _logger.LogMethodStart(_appSettings.AppName);

        // Validate required properties
        ArgumentNullException.ThrowIfNull(categoryMapping.CategoryId);

        // Retrieve existing CategoryMapping from repository
        var existingMapping = await _repository.GetAsync(categoryMapping.CategoryId);

        if (existingMapping == null)
        {
            _logger.LogCustomInformation(_appSettings.AppName,
                $"CategoryMapping for CategoryId {categoryMapping.CategoryId} does not exist in the database and must be inserted.");

            var isSuccess = await _repository.InsertAsync(categoryMapping);
            if (isSuccess)
            {
                _logger.LogCustomInformation(_appSettings.AppName,
                     $"CategoryMapping for CategoryId {categoryMapping.CategoryId} was successfully inserted in the database.");
            }
            else
            {
                _logger.LogCustomWarning(_appSettings.AppName,
                    $"CategoryMapping for CategoryId {categoryMapping.CategoryId} failed to insert.");
                return null;
            }
        }
        else
        {
            categoryMapping.Id = existingMapping.Id;

            if (categoryMapping.HasChanges(existingMapping))
            {
                // Update existing CategoryMapping
                _logger.LogCustomInformation(_appSettings.AppName,
                    $"CategoryMapping for CategoryId {categoryMapping.CategoryId} has changed from database and must be updated.");

                var isSuccess = await _repository.UpdateAsync(categoryMapping);
                if (isSuccess)
                {
                    _logger.LogCustomInformation(_appSettings.AppName,
                        $"CategoryMapping for CategoryId {categoryMapping.CategoryId} was successfully updated in the database.");
                }
                else
                {
                    _logger.LogCustomWarning(_appSettings.AppName,
                        $"CategoryMapping for CategoryId {categoryMapping.CategoryId} failed to update.");
                    return null;
                }
            }
            else
            {
                _logger.LogCustomInformation(_appSettings.AppName,
                    $"CategoryMapping for CategoryId {categoryMapping.CategoryId} has not changed. No update needed.");
            }
        }

        return categoryMapping;
    }
}
