﻿using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System.Data;
using System.Text.Json;

namespace WSA.Retail.Integration.Cosium
{
    internal class Clinic : Model.Clinic
    {
        internal Clinic() { }

        internal Clinic(string requestBody): base(requestBody) { }

        internal Clinic(Model.Clinic? baseObject)
        {
            if (baseObject != null)
            {
                this.Id = baseObject.Id;
                this.ExternalSystemCode = baseObject.ExternalSystemCode;
                this.Code = baseObject.Code;
                this.ExternalCode = baseObject.ExternalCode;
                this.Name = baseObject.Name;
                this.Company = baseObject.Company;
                this.Address = baseObject.Address;
                this.Address2 = baseObject.Address2;
                this.City = baseObject.City;
                this.Region = baseObject.Region;
                this.PostalCode = baseObject.PostalCode;
                this.Country = baseObject.Country;
                this.Phone = baseObject.Phone;
            }
        }

        private static readonly string _entityCode = "CLINIC";

        internal static async Task ProcessNewRecordsAsync()
        {
            var log = Common.Logger();
            var scope = InitScope(nameof(ProcessNewRecordsAsync));
            log.BeginScope(scope);
            log.LogInformation("[{className}].[{procedureName}] started", ClassName(), nameof(ProcessNewRecordsAsync));

            List<Clinic?> clinics = await GetFromSource();
            log.LogInformation("[{className}].[{procedureName}] Fetched {recordCount} clinics from Cosium database",
                ClassName(), nameof(ProcessNewRecordsAsync), clinics?.Count);

            if (clinics?.Count > 0)
            {
                await PostListAsync(clinics);
            }
            else
            {
                log.LogInformation("[{className}].[{procedureName}] No new clinics are avalable in the Cosium database.",
                    ClassName(), nameof(ProcessNewRecordsAsync));
            }
        }

        private static async Task<List<Clinic?>> GetFromSource()
        {
            var log = Common.Logger();
            log.LogInformation("[{className}].[{procedureName}] started", ClassName(), nameof(GetFromSource));

            List<Clinic?> ObjectList = [];

            var context = Common.Context();
            try
            {
                var recordList = await context.Clinics.Where(x => x.IntegrationRequired == true).ToListAsync();
                if (recordList != null)
                {
                    if (recordList.Count > 0)
                    {
                        log.LogInformation("[{className}].[{procedureName}] SQL reader returned {rowCount} rows.",
                                                                ClassName(), nameof(GetFromSource), recordList.Count);

                        foreach (Clinics record in recordList)
                        {
                            if (record != null)
                            {
                                if (record.JSON != null)
                                {
                                    Clinic newObject = new(record.JSON)
                                    {
                                        ExternalSystemCode = Common.ExternalSystemCode()
                                    };
                                    ObjectList.Add(newObject);
                                }
                            }
                        }
                        return ObjectList;
                    }
                    else
                    {
                        log.LogInformation("[{className}].[{procedureName}] No new clinics were retrieved from the database.",
                            ClassName(), nameof(GetFromSource));
                    }
                }
            }
            catch (Exception ex)
            {
                log.LogError(ex, "[{className}].[{procedureName}] Attempt to fetch clinics from the database generated an exception:\r\n{object}",
                    ClassName(), nameof(GetFromSource), ex.Message);
            }

            return ObjectList;
        }

        internal static async Task<Clinic?> GetFromBaseAsync(string? code = null, string? externalCode = null)
        {
            var log = Common.Logger();
            log.LogInformation("[{className}].[{procedureName}] started", ClassName(), nameof(GetFromBaseAsync));

            Clinic? clinic = new(await Model.Clinic.GetAsync(Common.ExternalSystemCode(), code: code, externalCode: externalCode));
            if (clinic.Id != null)
            {
                return clinic;
            }
            return null;
        }

        internal static async Task<Clinic?> GetFromDatabaseById(int thisId)
        {
            var log = Common.Logger();
            log.LogInformation("[{className}].[{procedureName}] started", ClassName(), nameof(GetFromDatabaseById));

            var context = Common.Context();
            try
            {
                var oldRecord = await context.Clinics.FindAsync(thisId);
                ArgumentNullException.ThrowIfNull(oldRecord);
                ArgumentNullException.ThrowIfNull(oldRecord.JSON);
                Clinic? newObject = new(oldRecord.JSON);
                return newObject;
            }
            catch (Exception ex)
            {
                log.LogError(ex, "[{className}].[{procedureName}] Attempt to fetch clinics from the database generated an exception:\r\n{object}",
                    ClassName(), nameof(GetFromDatabaseById), ex.Message);
            }
            return null;
        }

        internal static async Task PostListAsync(List<Clinic?> clinics)
        {
            var log = Common.Logger();
            log.LogInformation("[{className}].[{procedureName}] started", ClassName(), nameof(PostListAsync));

            var entitySubscriber = await Model.EntitySubscriber.GetAsync(null, Common.ExternalSystemCode(), _entityCode);
            ArgumentNullException.ThrowIfNull(entitySubscriber);
            if (entitySubscriber.FromExternalSystem ?? false == true)
            {
                try
                {
                    foreach (var clinic in clinics)
                    {
                        if (clinic != null)
                        {
                            await clinic.PostAsync(entitySubscriber);
                        }
                    }
                }
                catch (Exception ex)
                {
                    log.LogError(ex, "[{className}].[{procedureName}] Encountered an exception:\r\n{object}",
                        ClassName(), nameof(PostListAsync), ex.Message);
                }
            }
        }

        internal async Task<Clinic?> PostAsync(Model.EntitySubscriber? entitySubscriber = null)
        {
            var log = Common.Logger();
            log.LogInformation("[{className}].[{procedureName}] started", ClassName(), nameof(PostAsync));

            if (entitySubscriber == null)
            {
                entitySubscriber = await Model.EntitySubscriber.GetAsync(null, Common.ExternalSystemCode(), _entityCode);
                ArgumentNullException.ThrowIfNull(entitySubscriber);
            }
            if (entitySubscriber.FromExternalSystem ?? false == true)
            {
                try
                {
                    if (this != null)
                    {
                        log.LogInformation("[{className}].[{procedureName}] Sending clinic: {code} to API\r\n{requestBody}",
                            ClassName(), nameof(PostAsync), this.Code, JsonSerializer.Serialize(this, Common.GetJsonOptions()));
                        var responseObject = await this.Upsert();
                        if (responseObject != null)
                        {
                            log.LogInformation("[{className}].[{procedureName}] Successfully updated patient: {code}", ClassName(), this.Code);
                            await UpdateIsIntegratedAsync();
                            return new(responseObject);
                        }
                        else
                        {
                            log.LogError("[{className}].[{procedureName}] Attempt to update clinic: {code} failed",
                                ClassName(), nameof(PostAsync), this.Code);
                        }
                    }
                }
                catch (Exception ex)
                {
                    log.LogError(ex, "[{className}].[{procedureName}] Encountered an exception:\r\n{object}",
                        ClassName(), nameof(PostAsync), ex.Message);
                }
            }
            return null;
        }

        private async Task<bool> UpdateIsIntegratedAsync()
        {
            var log = Common.Logger();
            log.LogInformation("[{className}].[{procedureName}] started", ClassName(), nameof(UpdateIsIntegratedAsync));

            var context = Common.Context();
            var oldRecord = await context.Clinics.FindAsync(this.ExternalCode);
            ArgumentNullException.ThrowIfNull(oldRecord);

            try
            {
                if (oldRecord.IntegrationRequired == true)
                {
                    oldRecord.IntegrationRequired = false;
                    context.Update(oldRecord);
                    await context.SaveChangesAsync();
                }
                return true;
            }
            catch
            {
                return false;
            }
        }

        internal static async Task ValidateExternalReference(Model.ExternalReference? externalReference)
        {
            var log = Common.Logger();
            log.LogInformation("[{className}].[{procedureName}] started", ClassName(), nameof(ValidateExternalReference));

            if (externalReference != null && externalReference.ExternalCode != null)
            {
                ArgumentNullException.ThrowIfNull(externalReference.ExternalCode);
                Clinic? clinic = await GetFromBaseAsync(externalCode: externalReference.ExternalCode);
                {
                    if (clinic == null)
                    {
                        clinic = await GetFromDatabaseById(int.Parse(externalReference.ExternalCode));
                        if (clinic != null)
                        {
                            clinic = await clinic.PostAsync();
                        }
                    }
                }

                ArgumentNullException.ThrowIfNull(clinic);
                ArgumentNullException.ThrowIfNull(clinic.Id);
                externalReference.Id = clinic.Id;
                externalReference.ExternalCode = clinic.ExternalCode;
                externalReference.Code = clinic.Code;
                externalReference.Name = clinic.Name;
            }
        }

        private static string ClassName()
        {
            return nameof(Clinic);
        }

        private static Dictionary<string, object> InitScope(string procedureName)
        {
            return Common.InitScope(ClassName(), procedureName);
        }
    }
}