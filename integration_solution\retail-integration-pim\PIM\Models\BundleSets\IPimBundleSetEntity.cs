﻿using WSA.Retail.Integration.PIM.Core;
using WSA.Retail.Integration.PIM.Models.Products;

namespace WSA.Retail.Integration.PIM.Models.BundleSets;

public interface IPimBundleSetEntity : IIdentifiable, INameable, IAuditInfo
{
    public string? Type { get; set; }
    public bool? IsDefault { get; set; }
    public bool? IsMandatory { get; set; }
    public bool? IsMasterData { get; set; }
    public int? Ranking { get; set; }
    public string? State { get; set; }
    public DateTime? CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }

    public abstract ICollection<PimProductEntity> Products { get; set; }
}
