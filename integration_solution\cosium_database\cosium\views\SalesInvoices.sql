﻿CREATE VIEW [cosium].[SalesInvoices]
AS
SELECT Source.id,
       CONCAT('SINV-', FORMAT(CAST(Source.id AS INT), '00000000')) AS 'DocumentNumber',
       Source.numfacture AS 'AlternateNumber',
       CAST(Source.datefacture AS datetime2(7)) AS 'DocumentDate',
       Source.centre AS 'ClinicExternalCode',
       CONCAT('PAT-', FORMAT(CAST(Source.refclient AS INT), '**********')) AS 'PatientCode',
       (SELECT SUM(articlefacture.totalarticlettc_dec) 
          FROM cosium.articlefacture 
         WHERE articlefacture.reffacture = source.id) AS 'AmountInclTax',
       (SELECT SUM(CAST(articlefacture.totalarticlettc_dec - (articlefacture.totalarticlettc_dec / (1 + articlefacture.tauxtva_dec/100)) AS DECIMAL(18,4))) 
          FROM cosium.articlefacture 
         WHERE articlefacture.reffacture = source.id) AS 'TaxAmount',
       Source.IntegrationRequired,
       Source.IntegrationDate,
       Source.CreatedOn,
       Source.ModifiedOn,
       t1.JSON

  FROM cosium.facture AS Source

       OUTER APPLY (SELECT (SELECT CONCAT('SINV-', FORMAT(CAST(facture.id AS INT), '00000000')) AS 'documentNumber',
                                   facture.id AS 'externalReference',
                                   facture.numfacture AS 'alternateNumber',
                                   CONCAT('PAT-', FORMAT(CAST(facture.refclient AS INT), '**********')) AS 'patient.code',
                                   client.id AS 'patient.externalCode',
                                   TRIM(TRIM(ISNULL(client.prenom, '')) + ' ' + TRIM(ISNULL(client.nom, ''))) AS 'patient.name', 
                                   site.id AS 'clinic.externalCode',
                                   site.nomsite AS 'clinic.name',
                                   facture.datefacture AS 'documentDate',
                                   (SELECT CAST(articlefacture.id AS INT) AS 'sequence',
                                           CONCAT('ITEM-', FORMAT(CAST(produit.id AS INT), '000000')) AS 'product.code',
                                           produit.id AS 'product.externalCode',
                                           produit.libelle AS 'product.name',
                                           articlefacture.libelle AS 'description',
                                           articlefacture.qtearticle_dec AS 'quantity',
                                           ISNULL(articles.numserie, '') AS 'serialNumber',
                                           IIF(articlefacture.qtearticle_dec = 0, (articlefacture.totalarticlettc_dec + articlefacture.remise_dec),
                                            CAST((articlefacture.totalarticlettc_dec + articlefacture.remise_dec) / articlefacture.qtearticle_dec AS DECIMAL(18,4))) AS 'unitPrice',                                          
                                           articlefacture.totalarticlettc_dec + articlefacture.remise_dec AS 'grossAmount',
                                           articlefacture.remise_dec AS 'discountAmount',
                                           CAST(articlefacture.totalarticlettc_dec / (1 + (articlefacture.tauxtva_dec/100)) AS DECIMAL(18,4)) AS 'amountExclTax',
                                           articlefacture.totalarticlettc_dec -  CAST(articlefacture.totalarticlettc_dec / (1 + (articlefacture.tauxtva_dec/100)) AS DECIMAL(18,4)) AS 'taxAmount',
                                           articlefacture.totalarticlettc_dec AS 'amountInclTax'

                                      FROM cosium.articlefacture

                                           LEFT JOIN cosium.articles
                                                  ON articlefacture.refarticle = articles.id

                                           LEFT JOIN cosium.produit
                                                  ON articlefacture.refproduit = produit.id

                                     WHERE articlefacture.reffacture = facture.id

                                       FOR JSON PATH
                                   ) AS 'salesInvoiceLines'

                              FROM cosium.facture

                                   LEFT JOIN cosium.client
                                          ON facture.refclient = client.id

                                   LEFT JOIN cosium.site
                                  ON facture.centre = site.id

                             WHERE facture.id = Source.id

                               FOR JSON PATH, WITHOUT_ARRAY_WRAPPER

                           ) AS JSON
                   ) AS t1

 WHERE Source.typefacture = 0