﻿CREATE VIEW [dbo].[Colors]
AS 

  WITH Source AS (
SELECT Color.Id,
       ExternalSystem.Code AS ExternalSystemCode,
       Color.Code,
       LatestCouplings.ExternalCode,
       Color.[Name],
       Color.HexCode,
       Color.CreatedOn,
       Color.ModifiedOn

  FROM dbo.Color

 CROSS JOIN dbo.ExternalSystem

 OUTER APPLY (
       SELECT TOP 1 Coupling.ExternalRecordId AS ExternalCode
         FROM dbo.Coupling
        WHERE Coupling.ExternalSystemId = ExternalSystem.Id
          AND Coupling.RecordId = Color.Id
        ORDER BY ModifiedOn DESC) AS LatestCouplings
     )

SELECT Source.Id,
       Source.ExternalSystemCode,
       Source.Code,
       Source.ExternalCode,
       Source.[Name],
       Source.HexCode,
       Source.[CreatedOn],
       Source.[ModifiedOn],
       (
       SELECT Source.Id AS 'id',
              Source.ExternalSystemCode AS 'externalSystemCode',
              Source.Code AS 'code',
              Source.ExternalCode AS 'externalCode',
              Source.[Name] AS 'name',
              Source.HexCode AS 'hexCode',
              Source.CreatedOn AS 'createdOn',
              Source.ModifiedOn AS 'modifiedOn'

           FOR JSON PATH, WITHOUT_ARRAY_WRAPPER
       ) AS JSON
  FROM Source