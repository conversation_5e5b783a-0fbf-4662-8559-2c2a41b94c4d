namespace WSA.Integration;

using Microsoft.Sales.Customer;
using WSA.Integration.Customer;

codeunit 50101 "Patient Events"
{
    [EventSubscriber(ObjectType::Table, Database::Customer, OnAfterInsertEvent, '', true, true)]
    local procedure SendEventOnAfterInsertEvent(
        var Rec: Record Customer;
        RunTrigger: Boolean)

    begin
        if Rec.IsTemporary then
            exit;

        if not (Rec."WSA Customer Type" = "WSA Customer Type"::Patient) then
            exit;

        if RunTrigger then
            SendPatientToIntegrationAPI(Rec);
    end;


    [EventSubscriber(ObjectType::Table, Database::Customer, OnAfterModifyEvent, '', true, true)]
    local procedure SendEventOnAfterModifyEvent(
        var Rec: Record Customer;
        var xRec: Record Customer;
        RunTrigger: Boolean)

    begin
        if Rec.IsTemporary then
            exit;

        if not (Rec."WSA Customer Type" = "WSA Customer Type"::Patient) then
            exit;

        if RunTrigger then
            SendPatientToIntegrationAPI(Rec);
    end;


    local procedure SendPatientToIntegrationAPI(Customer: Record Customer)
    var
        RetailIntegrationSetup: Record "Retail Integration Setup";
        IntegrationManagement: Codeunit "Integration Management";
        JObject: JsonObject;

    begin
        // RetailIntegrationSetup.SafeGet();
        // if not RetailIntegrationSetup.Enabled then
        //     exit;

        // if Customer."No." = '' then
        //     exit;

        // if Customer.Name = '' then
        //     exit;

        // if IsNullGuid(Customer.SystemId) then
        //     exit;

        // JObject.Add('code', Customer."No.");
        // JObject.Add('externalCode', Format(Customer.SystemId).TrimStart('{').TrimEnd('}'));
        // JObject.Add('name', Customer.Name);
        // JObject.Add('address', Customer.Address);
        // JObject.Add('address2', Customer."Address 2");
        // JObject.Add('city', Customer.City);
        // JObject.Add('region', Customer.County);
        // JObject.Add('country', Customer."Country/Region Code");
        // JObject.Add('postalCode', Customer."Post Code");
        // JObject.Add('phone', Customer."Phone No.");
        // JObject.Add('email', Customer."E-Mail");

        // if not IntegrationManagement.TrySendToApi(JObject, 'patients') then
        //     exit;
    end;
}
