﻿using System.Text.Json.Serialization;

namespace WSA.Retail.Integration.PIM.GraphQL;

public class GraphQLCategory
{
    [JsonPropertyName("categoryId")] public required Guid CategoryId { get; set; }
    [JsonPropertyName("categoryName")] public required string CategoryName { get; set; }
    [JsonPropertyName("coreBrandId")] public string? CoreBrandId { get; set; }
    [JsonPropertyName("isLeaf")] public bool IsLeaf { get; set; }
    //[JsonPropertyName("isVisible")] public bool IsVisible { get; set; }
    [JsonPropertyName("parentCategoryId")] public Guid? ParentCategoryId { get; set; }
    [JsonPropertyName("ranking")] public int Ranking { get; set; }
    [JsonPropertyName("state")] public required string State { get; set; }
    [JsonPropertyName("supportedBrandIds")] public List<string> SupportedBrandIds { get; set; } = [];
}
