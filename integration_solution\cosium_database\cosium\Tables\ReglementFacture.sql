﻿CREATE TABLE [cosium].[ReglementFacture] (
    [id]                NVARCHAR (50)  NOT NULL,
    [numcheque]         NVARCHAR (50)  NULL,
    [dateecheance]      NVARCHAR (50)  NULL,
    [montant]           NVARCHAR (50)  NULL,
    [dateencaissee]     NVARCHAR (50)  NULL,
    [typereglement]     NVARCHAR (50)  NULL,
    [refremisebanque]   NVARCHAR (50)  NULL,
    [refbanque]         NVARCHAR (50)  NULL,
    [nomemetteur]       NVARCHAR (200) NULL,
    [datecreation]      NVARCHAR (200) NULL,
    [refclient]         NVARCHAR (50)  NULL,
    [centre]            NVARCHAR (50)  NULL,
    [typeemetteur]      NVARCHAR (50)  NULL,
    [refcoordbancaires] NVARCHAR (50)  NULL,
    [refsymetrique]     NVARCHAR (50)  NULL,
    [reffournisseur]    NVARCHAR (50)  NULL,
    [reftiers]          NVARCHAR (50)  NULL,
    [multiclients]      NVARCHAR (50)  NULL,
    [alerterglt]        NVARCHAR (50)  NULL,
    [montantorigine]    NVARCHAR (50)  NULL,
    [numordrevrt]       NVARCHAR (50)  NULL,
    [dateexportcompta]  NVARCHAR (50)  NULL,
    [munom]             NVARCHAR (50)  NULL,
    [csnom]             NVARCHAR (100)  NULL,
    [datemodif]         NVARCHAR (50)  NULL,
    [CreatedOn]         DATETIME2 CONSTRAINT [DF_ReglementFacture_CreatedOn] DEFAULT (sysutcdatetime()) NOT NULL,
    [ModifiedOn]        DATETIME2 CONSTRAINT [DF_ReglementFacture_ModifiedOn] DEFAULT (sysutcdatetime()) NOT NULL,
    [IntegrationRequired]   BIT DEFAULT 1 NOT NULL,
    [IntegrationDate]       DATETIME2(7) NULL
    CONSTRAINT [PK_ReglementFacture] PRIMARY KEY CLUSTERED ([id] ASC)
);
GO

CREATE NONCLUSTERED INDEX [IX_ReglementFacture_ModifiedOn]
    ON [cosium].[ReglementFacture]([ModifiedOn])
GO

CREATE NONCLUSTERED INDEX [IX_ReglementFacture_Integrated]
    ON [cosium].[ReglementFacture]([IntegrationRequired], [dateencaissee], [id])
GO

CREATE TRIGGER [cosium].[ReglementFacture_UpdateModified]
ON [cosium].[ReglementFacture]
AFTER UPDATE 
AS
   UPDATE [cosium].[ReglementFacture]
   SET [ModifiedOn] = sysutcdatetime(),
       [IntegrationRequired] = IIF((d.[IntegrationDate] IS NULL) 
              AND ([ReglementFacture].[typereglement] IN (7,8,9,40,41))
              AND (ISNULL(d.[datemodif], '1900-01-01') < i.[datemodif])
              AND (ISNULL(i.[dateencaissee], '') <> '')
              , 1, [ReglementFacture].[IntegrationRequired])
   
   FROM inserted AS i
        JOIN deleted AS d
          ON i.id = d.id

   WHERE [cosium].[ReglementFacture].[id] = i.[id]
   GO
GO