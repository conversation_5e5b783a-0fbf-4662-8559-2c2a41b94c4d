﻿using System.Text.Json.Serialization;

namespace WSA.Retail.Integration.PIM.GraphQL;

public class PisBundleSetResponseWithProductsOutputType
{
    [JsonPropertyName("bundleSetId")] public Guid BundleSetId { get; set; } = Guid.Empty;

    [JsonPropertyName("bundleSetName")] public string? BundleSetName { get; set; }

    [JsonPropertyName("bundleSetType")] public BundleSetType? BundleSetType { get; set; }

    [JsonPropertyName("childProducts")] public List<PisBundleSetToProductResponseOutputType> ChildProducts { get; set; } = [];

    [JsonPropertyName("createdAt")] public DateTime CreatedAt { get; set; }

    [JsonPropertyName("isDefault")] public bool IsDefault { get; set; }

    [JsonPropertyName("isMandatory")] public bool IsMandatory { get; set; }

    [JsonPropertyName("isMasterData")] public bool IsMasterData { get; set; }

    [JsonPropertyName("ranking")] public int Ranking { get; set; }

    [JsonPropertyName("state")] public StateOutputType? State { get; set; }

    [JsonPropertyName("updatedAt")] public DateTime UpdatedAt { get; set; }
}