﻿CREATE TABLE [cosium].[mutuelle] (
    [id]                       NVARCHAR (50)  NOT NULL,
    [mucodeid]                 NVARCHAR (MAX) NULL,
    [munom]                    NVARCHAR (MAX) NULL,
    [muadresse]                NVARCHAR (MAX) NULL,
    [muadresse2]               NVARCHAR (MAX) NULL,
    [mucodepostal]             NVARCHAR (MAX) NULL,
    [muville]                  NVARCHAR (MAX) NULL,
    [mupays]                   NVARCHAR (MAX) NULL,
    [mutel]                    NVARCHAR (MAX) NULL,
    [mufax]                    NVARCHAR (MAX) NULL,
    [centre]                   NVARCHAR (MAX) NULL,
    [cache]                    NVARCHAR (MAX) NULL,
    [mucommentaire]            NVARCHAR (MAX) NULL,
    [muurl]                    NVARCHAR (MAX) NULL,
    [multientite]              NVARCHAR (MAX) NULL,
    [numero]                   NVARCHAR (MAX) NULL,
    [mucmu]                    NVARCHAR (MAX) NULL,
    [mucodecomplementaire]     NVARCHAR (MAX) NULL,
    [muemail]                  NVARCHAR (MAX) NULL,
    [mucommentaire2]           NVARCHAR (MAX) NULL,
    [muame]                    NVARCHAR (MAX) NULL,
    [numamcassocie]            NVARCHAR (MAX) NULL,
    [datemodif]                NVARCHAR (MAX) NULL,
    [refgestionnaire]          NVARCHAR (MAX) NULL,
    [mucode]                   NVARCHAR (MAX) NULL,
    [libelleabreviation]       NVARCHAR (MAX) NULL,
    [libellelong]              NVARCHAR (MAX) NULL,
    [api_id]                   NVARCHAR (MAX) NULL,
    [siren]                    NVARCHAR (MAX) NULL,
    [mucommentaireficheclient] NVARCHAR (MAX) NULL,
    [reforganisme]             NVARCHAR (MAX) NULL,
    [topm]                     NVARCHAR (MAX) NULL,
    [isoptoamc]                NVARCHAR (MAX) NULL,
    [optoamccotation]          NVARCHAR (MAX) NULL,
    [gestionseparee]           NVARCHAR (MAX) NULL,
    [isdre]                    NVARCHAR (MAX) NULL,
    [sstoahcbridge]            NVARCHAR (MAX) NULL,
    PRIMARY KEY CLUSTERED ([id] ASC)
);

