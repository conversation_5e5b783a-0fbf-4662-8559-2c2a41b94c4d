﻿using Microsoft.Extensions.Options;
using WSA.Retail.Integration.Manage.Configuration;
using WSA.Retail.Integration.Manage.Core;
using WSA.Retail.Integration.Models.Products;

namespace WSA.Retail.Integration.Manage.Models.Products;

public class ProductGetByQueryHandler(
    IOptions<AppSettings> appSettings,
    IProductService entityService)
    : BaseApiGetByQuery<
        Product,
        IProductService>(appSettings, entityService)
{
}