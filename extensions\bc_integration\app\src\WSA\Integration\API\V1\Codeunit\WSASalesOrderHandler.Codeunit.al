namespace WSA.Integration.API.V1;

using Microsoft.Integration.Graph;
using Microsoft.Sales.Document;
using WSA.Integration;


codeunit 50122 "WSA Sales Order Handler" implements "WSA Integration Request"
{
    TableNo = "WSA Integration Request Log";

    trigger OnRun()
    begin
        Code(Rec);
    end;


    procedure HandleRequest(var Request: Record "WSA Integration Request Log")
    begin
        if not Codeunit.Run(Codeunit::"WSA Sales Order Handler", Request) then begin
            Common.SetErrorResponse(Request, '');
        end;
    end;


    local procedure Code(var Request: Record "WSA Integration Request Log")
    var
        json: JsonObject;

    begin
        case Request.Method of
            Request.Method::post:
                HandlePost(Request);
            Request.Method::get:
                HandleGet(Request);
        end;
    end;


    local procedure HandlePost(var Request: Record "WSA Integration Request Log")
    var
        salesOrder: Record "Sales Header";

    begin
        if TryHandlePost(Request, salesOrder) then
            Common.SetCreatedResponse(Request, Common.SalesOrderToJson(salesOrder))
        else
            Common.SetErrorResponse(Request, GetLastErrorText());
    end;


    local procedure HandleGet(var Request: Record "WSA Integration Request Log")
    var
        salesOrder: Record "Sales Header";

    begin
        if not TryHandleGet(Request, salesOrder) then begin
            Common.SetErrorResponse(Request, '');
        end;
    end;


    [TryFunction]
    local procedure TryHandlePost(
        var Request: Record "WSA Integration Request Log";
        var SalesOrder: Record "Sales Header")

    var
        salesLine: Record "Sales Line";
        recRef: RecordRef;
        json: JsonObject;
        lines: JsonToken;
        line: JsonToken;
        serialNumber: Code[50];
        unitPrice: Decimal;
        amountExclTax: Decimal;
        amountInclTax: Decimal;
        discountAmount: Decimal;

    begin
        Request.TestField("Request Content");
        json := Common.GetJsonFromBlob(Request);

        if not SalesOrder.Get(SalesOrder."Document Type"::Order, Common.GetJsonValue(json, '$.number').AsCode()) then begin
            SalesOrder.Init();
            SalesOrder."Document Type" := SalesOrder."Document Type"::Order;
            SalesOrder."No." := Common.GetJsonValue(json, '$.number').AsCode();
            SalesOrder."External Document No." := Common.GetJsonValue(json, '$.number').AsCode();
            SalesOrder.Validate("Sell-to Customer No.", Common.GetJsonValue(json, '$.patient.code').AsCode());
            SalesOrder.Validate("Responsibility Center", Common.GetJsonValue(json, '$.clinic.code').AsCode());
            SalesOrder.Insert(false);
        end;

        recRef.GetTable(SalesOrder);
        Common.ValidateFieldFromJson(json, '$.orderDate', recRef, SalesOrder.FieldNo("Posting Date"), TempFieldSet);
        Common.ValidateFieldFromJson(json, '$.orderDate', recRef, SalesOrder.FieldNo("Document Date"), TempFieldSet);
        recRef.SetTable(SalesOrder);
        SalesOrder.Modify();

        if json.SelectToken('$.salesOrderLines', lines) then begin
            if lines.IsArray then begin
                foreach line in lines.AsArray() do begin
                    Clear(unitPrice);
                    Clear(amountExclTax);
                    Clear(amountInclTax);
                    Clear(discountAmount);

                    salesLine.Init();
                    salesLine."Document Type" := SalesOrder."Document Type";
                    salesLine."Document No." := SalesOrder."No.";
                    salesLine."Line No." := Common.GetJsonValue(line.AsObject(), '$.sequence').AsInteger();
                    salesLine.Insert();

                    salesLine.Validate("Sell-to Customer No.", SalesOrder."Sell-to Customer No.");
                    salesLine.Validate("Responsibility Center", SalesOrder."Responsibility Center");
                    salesLine.Validate(Type, salesLine.Type::Item);
                    recRef.GetTable(salesLine);
                    Common.ValidateFieldFromJson(line.AsObject(), '$.product.code', recRef, salesLine.FieldNo("No."), TempFieldSet);
                    Common.ValidateFieldFromJson(line.AsObject(), '$.description', recRef, salesLine.FieldNo(Description), TempFieldSet);
                    Common.ValidateFieldFromJson(line.AsObject(), '$.quantity', recRef, salesLine.FieldNo(Quantity), TempFieldSet);
                    recRef.SetTable(salesLine);

                    unitPrice := Common.GetJsonValue(line.AsObject(), '$.unitPrice').AsDecimal();
                    amountExclTax := Common.GetJsonValue(line.AsObject(), '$.amountExclTax').AsDecimal();
                    amountInclTax := Common.GetJsonValue(line.AsObject(), '$.amountInclTax').AsDecimal();

                    if (unitPrice <> salesLine."Unit Price") then
                        salesLine.Validate("Unit Price", unitPrice);

                    if (SalesOrder."Prices Including VAT") then begin
                        if (amountInclTax <> salesLine."Amount Including VAT") then begin
                            discountAmount := salesLine."Amount Including VAT" - amountInclTax;
                            salesLine.Validate("Amount Including VAT", amountInclTax);
                            salesLine.Validate("Line Discount Amount", discountAmount);
                        end;
                    end else begin
                        if (amountExclTax <> salesLine."Line Amount") then
                            salesLine.Validate("Line Amount", amountExclTax);

                        if (amountInclTax <> salesLine."Amount Including VAT") then
                            salesLine.Validate("Amount Including VAT", amountInclTax);
                    end;

                    if not Common.GetJsonValue(line.AsObject(), '$.serialNumber').IsNull then begin
                        serialNumber := Common.GetJsonValue(line.AsObject(), '$.serialNumber').AsCode();
                        if serialNumber <> '' then
                            salesLine.Validate("WSA Serial No.", serialNumber);
                    end;
                    salesLine.Modify();
                end;
            end;
        end;

        SalesOrder.Status := SalesOrder.Status::Released;
        SalesOrder.Modify();
    end;


    [TryFunction]
    local procedure TryHandleGet(
        var Request: Record "WSA Integration Request Log";
        var SalesOrder: Record "Sales Header")

    var
        recRef: RecordRef;
        json: JsonObject;

    begin
        Request.TestField("Request Content");
        json := Common.GetJsonFromBlob(Request);

        if not (Common.GetJsonValue(json, '$.id').IsNull) then begin
            if SalesOrder.GetBySystemId(Common.GetJsonValue(json, '$.id').AsText()) then begin
                Common.SetOkResponse(Request, Common.SalesOrderToJson(SalesOrder));
                exit;
            end;
        end;

        if not (Common.GetJsonValue(json, '$.number').IsNull) then begin
            SalesOrder.SetRange("Document Type", SalesOrder."Document Type"::Order);
            SalesOrder.SetRange("No.", Common.GetJsonValue(json, '$.number').AsCode());
            if SalesOrder.FindFirst() then begin
                Common.SetOkResponse(Request, Common.SalesOrderToJson(SalesOrder));
                exit;
            end
        end;

        Common.SetNoContentResponse(Request);
    end;


    var
        TempFieldSet: Record 2000000041 temporary;
        GraphMgtGeneralTools: Codeunit "Graph Mgt - General Tools";
        Common: Codeunit "WSA Common";
}
