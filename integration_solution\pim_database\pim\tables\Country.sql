﻿CREATE TABLE [pim].[Country]
(
    [Id]                    UNIQUEIDENTIFIER CONSTRAINT [DF_Country_Id] DEFAULT NEWID() NOT NULL,
    [Code]                  NVARCHAR(20) NOT NULL,
    [Name]                  NVARCHAR(100) NULL,
    [SystemCreatedOn]       DATETIME2 CONSTRAINT [DF_Country_SystemCreatedOn] DEFAULT (sysutcdatetime()) NOT NULL,
    [SystemModifiedOn]      DATETIME2 CONSTRAINT [DF_Country_SystemModifiedOn] DEFAULT (sysutcdatetime()) NOT NULL,
    CONSTRAINT              [PK_Country_Id] PRIMARY KEY CLUSTERED ([Id] ASC)
)
GO

CREATE UNIQUE INDEX IX_Country_Code
ON [pim].[Country] ([Code])
GO