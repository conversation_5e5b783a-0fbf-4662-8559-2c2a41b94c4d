﻿using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Logging;
using System.Data;
using System.Text.Json;
using System.Text;

namespace WSA.Retail.Integration.Cosium
{
    public static class PurchaseRecieptApi
    {
        public static async Task PurchaseReceiptPost(PurchaseReceipt purchaseReceipt, ILogger _logger)
        {
            _logger.LogInformation("[Cosium].[PurchaseRecieptApi].[PurchaseReceiptPost] Procedure started");

            try
            {
                using (HttpClient purchaseReceiptHttpClient = Common.GetHttpClient())
                {

                    var purchaseReceiptRequestContent = new StringContent(
                        JsonSerializer.Serialize(purchaseReceipt, Common.GetJsonOptions()),
                        Encoding.UTF8,
                        "application/json"
                    );

                    using (var purchaseReceiptPostRequest = new HttpRequestMessage(HttpMethod.Post, "purchaseReceipts")
                    {
                        Content = purchaseReceiptRequestContent
                    })
                    {
                        HttpResponseMessage purchaseReceiptHttpResponse = await purchaseReceiptHttpClient.SendAsync(purchaseReceiptPostRequest);

                        _logger.LogTrace(
                            "[Cosium].[PurchaseRecieptApi].[PurchaseReceiptPost] Returning response:\r\n{response}",
                            JsonSerializer.Serialize(purchaseReceiptHttpResponse, Common.GetJsonOptions())
                        );

                        if (purchaseReceiptHttpResponse.IsSuccessStatusCode)
                        {
                            _logger.LogInformation(
                                "[Cosium].[PurchaseRecieptApi].[PurchaseReceiptPost] completed for purchaseOrder: {purchaseOrder}",
                                JsonSerializer.Serialize(purchaseReceipt)
                            );
                        }
                        else
                        {
                            _logger.LogWarning(
                                "[Cosium].[PurchaseRecieptApi].[PurchaseReceiptPost] Attempted to post failed for purchaseOrder: {purchaseOrder}",
                                JsonSerializer.Serialize(purchaseReceipt)
                            );
                        }
                    }

                }
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[Cosium].[PurchaseRecieptApi].[PurchaseReceiptPost] Encountered an exception:\r\n{message}",
                    ex.Message
                );
            }
        }
    }
}
