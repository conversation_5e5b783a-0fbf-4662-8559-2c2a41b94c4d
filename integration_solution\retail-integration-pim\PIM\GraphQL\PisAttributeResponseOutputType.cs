﻿using System.Text.Json.Serialization;

namespace WSA.Retail.Integration.PIM.GraphQL;

public class PisAttributeResponseOutputType
{
    [JsonPropertyName("attributeCode")] public required string AttributeCode { get; set; }

    [JsonPropertyName("dataType")] public string? DataType { get; set; }

    [JsonPropertyName("name")] public string? Name { get; set; }

    [JsonPropertyName("value")] public string? Value { get; set; }

    [JsonPropertyName("valueCode")] public string? ValueCode { get; set; }

    [JsonPropertyName("values")] public List<string> Values { get; set; } = [];
}