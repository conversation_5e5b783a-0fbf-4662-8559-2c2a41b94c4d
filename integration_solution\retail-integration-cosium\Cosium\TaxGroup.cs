﻿using Microsoft.Extensions.Logging;

namespace WSA.Retail.Integration.Cosium
{
    internal class TaxGroup:Model.TaxGroup
    {
        internal TaxGroup() { }

        internal TaxGroup(string requestBody): base(requestBody) { }

        internal TaxGroup(Model.TaxGroup? baseObject)
        {
            if (baseObject != null)
            {
                this.Id = baseObject.Id;
                this.ExternalSystemCode = baseObject.ExternalSystemCode;
                this.Code = baseObject.Code;
                this.ExternalCode = baseObject.ExternalCode;
                this.Name = baseObject.Name;
                this.Rate = baseObject.Rate;
            }
        }

        internal TaxGroup(TauxTVA tauxTVA)
        {
            ExternalSystemCode = Common.ExternalSystemCode();
            ExternalCode = tauxTVA.Id;
            Rate = decimal.Parse(tauxTVA.Taux ?? "0");
        }

        private static readonly string _entityCode = "TAXGROUP";


        internal static async Task<TaxGroup?> GetFromBaseAsync(string? code = null, string? externalCode = null)
        {
            var log = Common.Logger();
            log.LogInformation("[{className}].[{procedureName}] started", ClassName(), nameof(GetFromBaseAsync));

            TaxGroup? record = new(await Model.TaxGroup.GetAsync(Common.ExternalSystemCode(), code: code, externalCode: externalCode));
            if (record.Id != null)
            {
                return record;
            }
            return null;
        }

        internal static async Task<TaxGroup?> GetFromCosiumById(int thisId)
        {
            var log = Common.Logger();
            log.LogInformation("[{className}].[{procedureName}] started", ClassName(), nameof(GetFromCosiumById));

            var context = Common.Context();
            try
            {
                var oldRecord = await context.TauxTVA.FindAsync(thisId);
                ArgumentNullException.ThrowIfNull(oldRecord);
                ArgumentNullException.ThrowIfNull(oldRecord.Id);
                TaxGroup? newObject = new(oldRecord);
                return newObject;
            }
            catch (Exception ex)
            {
                log.LogError(ex, "[{className}].[{procedureName}] Attempt to fetch taxGroup from the database generated an exception:\r\n{object}",
                    ClassName(), nameof(GetFromCosiumById), ex.Message);
            }
            return null;
        }


        internal static async Task ValidateExternalReference(Model.ExternalReference? externalReference)
        {
            var log = Common.Logger();
            log.LogInformation("[{className}].[{procedureName}] started", ClassName(), nameof(ValidateExternalReference));

            if (externalReference != null && externalReference.ExternalCode != null)
            {
                ArgumentNullException.ThrowIfNull(externalReference.ExternalCode);
                TaxGroup? record = await GetFromBaseAsync(externalCode: externalReference.ExternalCode);

                ArgumentNullException.ThrowIfNull(record);
                ArgumentNullException.ThrowIfNull(record.Id);
                externalReference.Id = record.Id;
                externalReference.ExternalCode = record.ExternalCode;
                externalReference.Code = record.Code;
                externalReference.Name = record.Name;
            }
        }

        private static string ClassName()
        {
            return nameof(TaxGroup);
        }

        private static Dictionary<string, object> InitScope(string procedureName)
        {
            return Common.InitScope(ClassName(), procedureName);
        }
    }
}