namespace WSA.Integration.API.V1;

using Microsoft.Finance.GeneralLedger.Setup;
using WSA.Integration.Customer;
using Microsoft.Foundation.PaymentTerms;
using Microsoft.Sales.Customer;
using System.IO;


codeunit 50108 "APIV1 - Patients E2E"
{
    Subtype = Test;
    TestPermissions = Disabled;

    trigger OnRun()
    begin
        // [FEATURE] Patients API
    end;


    [Test]
    procedure TestGetSimplePatient()
    var
        Customer: Record "Customer";
        Response: Text;
        TargetURL: Text;

    begin
        // [SCENARIO] User can get a simple patient with a GET request to the service.
        Initialize();

        // [GIVEN] A patient exists in the system.
        CreateMockPatient(Customer);

        // [WHEN] The user makes a GET request for a given patient.
        TargetURL := LibraryGraphMgt.CreateTargetURL(Customer.SystemId, Page::"API V1 - Patients", ServiceNameTxt);
        LibraryGraphMgt.GetFromWebService(Response, TargetURL);

        // [THEN] The response text contains the patient information.
        VerifySimpleProperties(Response, Customer);
    end;


    [Test]
    procedure TestCreatePatientWithTemplate()
    var
        Patient: Record "Customer";
        CustomerPostingGroup: Record "Customer Posting Group";
        GenBusinessPostingGroup: Record "Gen. Business Posting Group";
        PaymentTerms: Record "Payment Terms";
        TempPatient: Record "Customer" temporary;
        CustomerJSON: Text;
        TargetURL: Text;
        RequestBody: Text;
        Response: Text;

    begin
        // [SCENARIO] User can create a new Customer through a POST method.
        Initialize();
        GetComplexProperties(CustomerPostingGroup, GenBusinessPostingGroup, PaymentTerms);
        CreatePatientTemplate(
            Patient,
            CustomerPostingGroup."Code",
            GenBusinessPostingGroup."Code",
            PaymentTerms."Code");

        // [GIVEN] The user has constructed a detailed customer JSON object to send to the service
        RequestBody := GetMockPatientJSON();
        CreateMockPatient(RequestBody, TempPatient);
        TempPatient."Customer Posting Group" := CustomerPostingGroup."Code";
        TempPatient."Gen. Bus. Posting Group" := GenBusinessPostingGroup.Code;
        TempPatient."Payment Terms Code" := PaymentTerms."Code";
        TempPatient.Modify();

        // [WHEN] The user posts the JSON to the service.
        TargetURL := LibraryGraphMgt.CreateTargetURL('', Page::"API V1 - Patients", ServiceNameTxt);
        LibraryGraphMgt.PostToWebService(TargetURL, RequestBody, Response);

        // [THEN] The customer has been created in the database with all the details
        Patient.Get(TempPatient."No.");
        VerifyComplexProperties(Response, Patient, CustomerPostingGroup, GenBusinessPostingGroup, PaymentTerms);
    end;


    [Test]
    procedure TestModifyPatient()
    var
        Patient: Record "Customer";
        TempPatient: Record "Customer" temporary;
        JObject: JsonObject;
        JToken: JsonToken;
        RequestBody: Text;
        ResponseBody: Text;
        TargetURL: Text;

    begin
        // [SCENARIO] User can modify a patient through a PATCH request.
        Initialize();

        // [GIVEN] A patient customer exists.
        RequestBody := GetMockPatientJSON();
        CreateMockPatient(RequestBody, Patient);

        // [GIVEN] The user has constructed a detailed patient JSON object to send to the service.
        TempPatient.TransferFields(Patient);
        TempPatient.Name := LibraryUtility.GenerateGUID();
        RequestBody := PatientToJson(TempPatient);

        // [WHEN] The user makes a patch request to the service.
        TargetURL := LibraryGraphMgt.CreateTargetURL(Patient.SystemId, Page::"API V1 - Patients", ServiceNameTxt);
        LibraryGraphMgt.PatchToWebService(TargetURL, RequestBody, ResponseBody);

        // [THEN] The response text contains the new values.
        VerifySimpleProperties(ResponseBody, TempPatient);

        // [THEN] The record in the database contains the new values.
        Patient.Get(Patient."No.");
        VerifySimpleProperties(ResponseBody, Patient);
    end;


    local procedure Initialize()
    var
        CustomerPostingGroup: Record "Customer Posting Group";
        GenBusinessPostingGroup: Record "Gen. Business Posting Group";
        PaymentTerms: Record "Payment Terms";
        ConfigTemplateRules: Record "Config. Tmpl. Selection Rules";

    begin
        if IsInitialized then
            exit;

        if CustomerPostingGroup.IsEmpty() then
            LibrarySales.CreateCustomerPostingGroup(CustomerPostingGroup);

        if GenBusinessPostingGroup.IsEmpty() then
            LibraryERM.CreateGenBusPostingGroup(GenBusinessPostingGroup);

        if PaymentTerms.IsEmpty() then
            LibraryERM.CreatePaymentTerms(PaymentTerms);

        ConfigTemplateRules.SetRange("Table ID", Database::Customer);
        ConfigTemplateRules.SetRange("Page ID", 0);
        ConfigTemplateRules.SetRange(Order, 0);
        if ConfigTemplateRules.FindFirst() then begin
            ConfigTemplateRules.Order := 99;
            ConfigTemplateRules.Modify(false);
        end;

        IsInitialized := true;
        Commit();
    end;


    local procedure CreateMockPatient(var Patient: Record "Customer")
    var
        Json: Text;

    begin
        Json := GetMockPatientJSON();
        CreateMockPatient(Json, Patient);
    end;


    local procedure CreateMockPatient(Json: Text; var Patient: Record "Customer")
    var
        JObject: JsonObject;

    begin
        JObject.ReadFrom(Json);

        Patient.Init();
        Patient."No." := GetJsonValue(JObject, 'code');
        Patient."External Code" := GetJsonValue(JObject, 'externalCode');
        Patient.Name := GetJsonValue(JObject, 'name');
        Patient.Address := GetJsonValue(JObject, 'address');
        Patient.City := GetJsonValue(JObject, 'city');
        Patient.County := GetJsonValue(JObject, 'region');
        Patient."Country/Region Code" := GetJsonValue(JObject, 'country');
        Patient."Post Code" := GetJsonValue(JObject, 'postalCode');
        Patient."Phone No." := GetJsonValue(JObject, 'phone');
        Patient."E-Mail" := GetJsonValue(JObject, 'email');

        Patient."WSA Customer Type" := Patient."WSA Customer Type"::Patient;
        Patient.Insert(true);
        Patient.Get(Patient."No.");

        Commit(); // Need to commit in order to unlock tables and allow web service to pick up changes.
    end;


    local procedure PatientToJson(var Patient: Record "Customer"): Text
    var
        JSON: Text;

    begin
        JSON := LibraryGraphMgt.AddPropertytoJSON(JSON, 'id', Format(Patient.SystemId).TrimStart('{').TrimEnd('}'));
        JSON := LibraryGraphMgt.AddPropertytoJSON(JSON, 'code', Patient."No.");
        JSON := LibraryGraphMgt.AddPropertytoJSON(JSON, 'externalCode', Patient."External Code");
        JSON := LibraryGraphMgt.AddPropertytoJSON(JSON, 'name', Patient.Name);
        JSON := LibraryGraphMgt.AddPropertytoJSON(JSON, 'address', Patient.Address);
        JSON := LibraryGraphMgt.AddPropertytoJSON(JSON, 'address2', Patient."Address 2");
        JSON := LibraryGraphMgt.AddPropertytoJSON(JSON, 'city', Patient.City);
        JSON := LibraryGraphMgt.AddPropertytoJSON(JSON, 'region', Patient.County);
        JSON := LibraryGraphMgt.AddPropertytoJSON(JSON, 'country', Patient."Country/Region Code");
        JSON := LibraryGraphMgt.AddPropertytoJSON(JSON, 'postalCode', Patient."Post Code");
        JSON := LibraryGraphMgt.AddPropertytoJSON(JSON, 'phone', Patient."Phone No.");
        JSON := LibraryGraphMgt.AddPropertytoJSON(JSON, 'email', Patient."E-Mail");
    end;


    local procedure GetJsonValue(JObject: JsonObject; PropertyName: Text): Text
    var
        JToken: JsonToken;

    begin
        JObject.Get(PropertyName, JToken);
        if JToken.AsValue().IsNull then
            exit('');
        exit(JToken.AsValue().AsText());
    end;


    local procedure VerifySimpleProperties(JSON: Text; Customer: Record "Customer")
    begin
        Assert.AreNotEqual('', JSON, EmptyJSONErr);
        LibraryGraphMgt.VerifyIDInJson(JSON);
        LibraryGraphMgt.VerifyPropertyInJSON(JSON, 'code', Customer."No.");
        LibraryGraphMgt.VerifyPropertyInJSON(JSON, 'externalCode', Customer."External Code");
        LibraryGraphMgt.VerifyPropertyInJSON(JSON, 'name', Customer.Name);
        LibraryGraphMgt.VerifyPropertyInJSON(JSON, 'address', Customer.Address);
        LibraryGraphMgt.VerifyPropertyInJSON(JSON, 'address2', Customer."Address 2");
        LibraryGraphMgt.VerifyPropertyInJSON(JSON, 'city', Customer.City);
        LibraryGraphMgt.VerifyPropertyInJSON(JSON, 'region', Customer.County);
        LibraryGraphMgt.VerifyPropertyInJSON(JSON, 'country', Customer."Country/Region Code");
        LibraryGraphMgt.VerifyPropertyInJSON(JSON, 'postalCode', Customer."Post Code");
        LibraryGraphMgt.VerifyPropertyInJSON(JSON, 'phone', Customer."Phone No.");
        LibraryGraphMgt.VerifyPropertyInJSON(JSON, 'email', Customer."E-Mail");
    end;


    local procedure VerifyComplexProperties(
        JSON: Text;
        Customer: Record "Customer";
        CustomerPostingGroup: Record "Customer Posting Group";
        GenBusinessPostingGroup: Record "Gen. Business Posting Group";
        PaymentTerms: Record "Payment Terms")

    begin
        VerifySimpleProperties(JSON, Customer);

        Assert.AreEqual(CustomerPostingGroup.Code, Customer."Customer Posting Group", 'Customer should have the correct customer posting group.');
        Assert.AreEqual(GenBusinessPostingGroup.Code, Customer."Gen. Bus. Posting Group", 'Customer should have the correct gen. business posting group.');
        Assert.AreEqual(PaymentTerms.Code, Customer."Payment Terms Code", 'Customer should have the correct payment terms code.');
    end;


    local procedure GetComplexProperties(
        var CustomerPostingGroup: Record "Customer Posting Group";
        var GenBusinessPostingGroup: Record "Gen. Business Posting Group";
        var PaymentTerms: Record "Payment Terms")

    var
        ConfigTemplateHeader: Record "Config. Template Header";
        ConfigTemplateLine: Record "Config. Template Line";

    begin
        if not GetPatientTemplate(ConfigTemplateHeader) then begin
            CustomerPostingGroup.Next(LibraryRandom.RandIntInRange(1, CustomerPostingGroup.Count));
            GenBusinessPostingGroup.Next(LibraryRandom.RandIntInRange(1, GenBusinessPostingGroup.Count));
            PaymentTerms.Next(LibraryRandom.RandIntInRange(1, PaymentTerms.Count));
            exit;
        end;

        ConfigTemplateLine.SetRange("Template Code", ConfigTemplateHeader.Code);
        ConfigTemplateLine.SetRange("Table ID", Database::Customer);

        ConfigTemplateLine.SetRange("Field ID", CustomerPostingGroup.FieldNo("Code"));
        CustomerPostingGroup.Get(ConfigTemplateLine."Default Value");

        ConfigTemplateLine.SetRange("Field ID", GenBusinessPostingGroup.FieldNo("Code"));
        CustomerPostingGroup.Get(ConfigTemplateLine."Default Value");

        ConfigTemplateLine.SetRange("Field ID", PaymentTerms.FieldNo("Code"));
        CustomerPostingGroup.Get(ConfigTemplateLine."Default Value");
    end;


    local procedure GetMockPatientJSON(): Text;
    var
        Client: HttpClient;
        Response: HttpResponseMessage;
        ResponseBody: Text;
        JObject: JsonObject;

    begin
        Client.DefaultRequestHeaders.Add('X-API-Key', 'cfcaadd0');
        Client.Get('https://my.api.mockaroo.com/patient', Response);
        Response.Content.ReadAs(ResponseBody);
        exit(ResponseBody);
    end;


    local procedure GetPatientTemplate(var ConfigTemplateHeader: Record "Config. Template Header"): Boolean
    var
        ConfigTmplSelectionRules: Record "Config. Tmpl. Selection Rules";

    begin
        ConfigTmplSelectionRules.SetRange("Table ID", Database::Customer);
        ConfigTmplSelectionRules.SetRange("Page ID", page::"API V1 - Patients");
        if ConfigTmplSelectionRules.FindFirst() then begin
            ConfigTemplateHeader.Get(ConfigTmplSelectionRules."Template Code");
            exit(true);
        end;
    end;


    local procedure CreatePatientTemplate(
        Customer: Record Customer;
        CustomerPostingGroupCode: Code[20];
        GenBusinessPostingGroupCode: Code[20];
        PaymentTermsCode: Code[10])

    var
        ConfigTemplateHeader: Record "Config. Template Header";
        ConfigTemplateLine: Record "Config. Template Line";
        ConfigTmplSelectionRules: Record "Config. Tmpl. Selection Rules";
        LibraryRapidStart: Codeunit "Library - Rapid Start";

    begin
        if GetPatientTemplate(ConfigTemplateHeader) then
            exit;

        LibraryRapidStart.CreateConfigTemplateHeader(ConfigTemplateHeader);
        ConfigTemplateHeader."Table ID" := Database::Customer;
        ConfigTemplateHeader.Modify();

        LibraryRapidStart.CreateConfigTemplateLine(ConfigTemplateLine, ConfigTemplateHeader.Code);
        ConfigTemplateLine."Field ID" := Customer.FieldNo("Customer Posting Group");
        ConfigTemplateLine."Default Value" := CustomerPostingGroupCode;
        ConfigTemplateLine.Modify(true);

        LibraryRapidStart.CreateConfigTemplateLine(ConfigTemplateLine, ConfigTemplateHeader.Code);
        ConfigTemplateLine."Field ID" := Customer.FieldNo("Gen. Bus. Posting Group");
        ConfigTemplateLine."Default Value" := GenBusinessPostingGroupCode;
        ConfigTemplateLine.Modify(true);

        LibraryRapidStart.CreateConfigTemplateLine(ConfigTemplateLine, ConfigTemplateHeader.Code);
        ConfigTemplateLine."Field ID" := Customer.FieldNo("Payment Terms Code");
        ConfigTemplateLine."Default Value" := PaymentTermsCode;
        ConfigTemplateLine.Modify(true);

        LibraryRapidStart.CreateTemplateSelectionRule(
            ConfigTmplSelectionRules, Customer.FieldNo("WSA Customer Type"), Format(Customer."WSA Customer Type"::Patient), 0, Page::"API V1 - Patients", ConfigTemplateHeader);

        Commit(); // Need to commit in order to unlock tables and allow web service to pick up changes.
    end;


    var
        Assert: Codeunit Assert;
        LibraryGraphMgt: Codeunit "Library - Graph Mgt";
        LibraryUtility: Codeunit "Library - Utility";
        LibraryERM: Codeunit "Library - ERM";
        LibrarySales: Codeunit "Library - Sales";
        LibraryRandom: Codeunit "Library - Random";
        IsInitialized: Boolean;
        ServiceNameTxt: Label 'patients';
        EmptyJSONErr: Label 'JSON should not be empty.';
}
