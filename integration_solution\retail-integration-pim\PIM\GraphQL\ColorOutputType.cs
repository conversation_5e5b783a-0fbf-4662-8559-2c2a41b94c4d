﻿using System.Text.Json.Serialization;

namespace WSA.Retail.Integration.PIM.GraphQL;

public class ColorOutputType
{
    [JsonPropertyName("agileName")] public string? AgileName { get; set; }
    [JsonPropertyName("attributeValueCode")] public string? AttributeValueCode { get; set; }
    [JsonPropertyName("hexCode")] public string? HexCode { get; set; }
    [JsonPropertyName("metadataBrandWorldWise")] public List<ColorBrandWorldOutputType> MetadataBrandWorldWise { get; set; } = [];
    [JsonPropertyName("ranking")] public int Ranking { get; set; }
    [JsonPropertyName("source")] public ImageSourceEnumOutputType? Source { get; set; }
}
