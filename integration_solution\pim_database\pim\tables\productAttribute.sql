﻿CREATE TABLE [pim].[ProductAttribute]
(
    [Id]                    UNIQUEIDENTIFIER CONSTRAINT [DF_ProductAttribute_id] DEFAULT NEWID() NOT NULL,
    [ProductId]             UNIQUEIDENTIFIER NOT NULL,
    [AttributeId]           UNIQUEIDENTIFIER NOT NULL,
    [Value]                 NVARCHAR(100) NULL,
    [SystemCreatedOn]       DATETIME2 CONSTRAINT [DF_ProductAttribute_SystemCreatedOn] DEFAULT (sysutcdatetime()) NOT NULL,
    [SystemModifiedOn]      DATETIME2 CONSTRAINT [DF_ProductAttribute_SystemModifiedOn] DEFAULT (sysutcdatetime()) NOT NULL,
    CONSTRAINT              [PK_ProductAttribute_Id] PRIMARY KEY CLUSTERED ([Id] ASC)
)
GO

CREATE UNIQUE INDEX IX_ProductAttribute_ProductId
ON [pim].[ProductAttribute] ([ProductId], [AttributeId])
GO