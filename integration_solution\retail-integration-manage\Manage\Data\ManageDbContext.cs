﻿using Microsoft.EntityFrameworkCore;
using WSA.Retail.Integration.Manage.Models.CategoryMappings;
using WSA.Retail.Integration.Manage.Models.Countries;
using WSA.Retail.Integration.Manage.Models.PurchaseOrders;

namespace WSA.Retail.Integration.Manage.Data
{
    public partial class ManageDbContext(DbContextOptions<ManageDbContext> options) : DbContext(options)
    {
        public DbSet<CategoryMappingEntity> CategoryMappingEntity { get; set; }
        public DbSet<InventoryCountryEntity> InventoryCountryEntity { get; set; }
        public DbSet<OrderEventEntity> OrderEventEntity { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            modelBuilder.HasDefaultSchema("manage");
            modelBuilder.ApplyConfiguration(new CategoryMappingConfiguration());
            modelBuilder.ApplyConfiguration(new InventoryCountryConfiguration());
            modelBuilder.ApplyConfiguration(new OrderEventConfiguration());

            base.OnModelCreating(modelBuilder);
        }
    }
}