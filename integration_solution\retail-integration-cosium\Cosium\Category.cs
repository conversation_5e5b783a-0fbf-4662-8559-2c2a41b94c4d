﻿using System.Text.Json;
using System.Text.Json.Nodes;
using WSA.Retail.Integration.Model;

namespace WSA.Retail.Integration.Cosium
{
    internal class Category : Model.Category
    {
        internal Category() { }

        internal Category(string requestBody): base(requestBody) { }

        internal static Dictionary<string, Category> CachedData = [];

        private static HttpClient? _client;

        internal void AddCachedData()
        {
            ArgumentNullException.ThrowIfNull(this.ExternalCode);
            CachedData.TryGetValue(this.ExternalCode, out var oldData);
            if (oldData == null)
            {
                CachedData.Add(this.ExternalCode, this);
            }
            else
            {
                if (HasChanged(oldData, this))
                {
                    CachedData[this.ExternalCode] = this;
                }
            }
        }

        internal static async Task<Category?> GetAsync(string? code = null, string? externalCode = null)
        {
            Category? category = null;
            HttpRequestMessage request;
            _client ??= Common.GetHttpClient();
            if (code != null)
            {
                category = CachedData.Values.Where(x => x.Code == code).FirstOrDefault();
                if (category != null)
                {
                    return category;
                }
                request = new HttpRequestMessage(HttpMethod.Get, "categories?code=" + code);
            }
            else
            {
                ArgumentNullException.ThrowIfNull(externalCode);
                CachedData.TryGetValue(externalCode, out category);
                if (category != null)
                {
                    return category;
                }
                request = new HttpRequestMessage(HttpMethod.Get, "categories?externalCode=" + externalCode);
            }

            var response = await _client.SendAsync(request);
            if (response != null)
            {
                var responseBody = await response.Content.ReadAsStringAsync();
                switch (response.StatusCode)
                {
                    case System.Net.HttpStatusCode.OK:

                        if (responseBody != null)
                        {
                            var jsonArray = JsonNode.Parse(responseBody)?.AsArray();
                            if (jsonArray != null)
                            {
                                List<Category?> records = [];
                                foreach(JsonNode? json in jsonArray)
                                {
                                    if (json != null)
                                    {
                                        Category record = new(json.ToJsonString(Common.GetJsonOptions()));
                                        record.AddCachedData();
                                        records.Add(record);
                                    }
                                }
                                if (records != null)
                                {
                                    return records.FirstOrDefault();
                                }
                            }
                        }
                        break;

                    case System.Net.HttpStatusCode.NoContent:
                        return null;

                    default: return null;
                }
            }
            return null;
        }
    }
}
