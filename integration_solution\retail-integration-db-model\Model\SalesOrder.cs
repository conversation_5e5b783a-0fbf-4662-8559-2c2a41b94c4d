﻿using System.Text.Json.Serialization;

namespace WSA.Retail.Integration.Model;

public class SalesOrder
{
    [JsonPropertyName("id")]
    public Guid? Id { get; set; }

    [JsonPropertyName("externalSystemCode")]
    public string? ExternalSystemCode { get; set; }

    [JsonPropertyName("number")]
    public string? Number { get; set; }

    [JsonPropertyName("externalReference")]
    public string? ExternalReference { get; set; }

    [JsonPropertyName("patient")]
    public ExternalReference? Patient { get; set; }

    [JsonPropertyName("clinic")]
    public ExternalReference? Clinic { get; set; }

    [JsonPropertyName("orderDate")]
    public DateOnly? OrderDate { get; set; }

    [JsonPropertyName("salesOrderLines")]
    public List<SalesOrderLine?> SalesOrderLines { get; set; }


    public SalesOrder() 
    {
        SalesOrderLines = [];
    }
}
