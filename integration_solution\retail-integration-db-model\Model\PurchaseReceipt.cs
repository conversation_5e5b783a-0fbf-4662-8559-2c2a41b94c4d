﻿using System.Text.Json.Serialization;

namespace WSA.Retail.Integration.Model;

public class PurchaseReceipt
{
    [JsonPropertyName("id")]
    public Guid? Id { get; set; }

    [JsonPropertyName("externalSystemCode")]
    public string? ExternalSystemCode { get; set; }

    [JsonPropertyName("documentNumber")]
    public string? DocumentNumber { get; set; }

    [JsonPropertyName("externalReference")]
    public string? ExternalReference { get; set; }

    [JsonPropertyName("alternateNumber")]
    public string? AlternateNumber { get; set; }

    [JsonPropertyName("purchaseOrder")]
    public ExternalDocumentReference? PurchaseOrder { get; set; }

    [Json<PERSON>ropertyName("vendor")]
    public ExternalReference? Vendor { get; set; }

    [JsonPropertyName("clinic")]
    public ExternalReference? Clinic { get; set; }

    [JsonPropertyName("documentDate")]
    public DateOnly? DocumentDate { get; set; }

    [JsonPropertyName("purchaseReceiptLines")]
    public List<PurchaseReceiptLine?> PurchaseReceiptLines { get; set; }


    public PurchaseReceipt() 
    {
        PurchaseReceiptLines = [];
    }
}
