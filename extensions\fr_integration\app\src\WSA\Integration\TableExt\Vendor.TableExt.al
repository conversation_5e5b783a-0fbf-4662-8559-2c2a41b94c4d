namespace WSA.Integration;

using Microsoft.Purchases.Vendor;

tableextension 50102 Vendor extends Vendor
{
    fields
    {
        field(50100; "External Code"; Code[100])
        {
            Caption = 'External Code';
            DataClassification = SystemMetadata;
        }

        field(50101; "Integrate with POS"; Boolean)
        {
            Caption = 'Integrate with POS';
            DataClassification = SystemMetadata;
        }
    }

    keys
    {
        key(ExternalCode; "External Code")
        {
        }
    }
}
