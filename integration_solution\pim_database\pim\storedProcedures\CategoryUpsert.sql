﻿CREATE PROCEDURE [pim].[CategoryUpsert]
(
       @id UNIQUEIDENTIFIER,
       @name NVARCHAR(100),
       @parentCategoryId UNIQUEIDENTIFIER = NULL
)
AS
BEGIN
    SET NOCOUNT ON

       DECLARE @source TABLE (
               Id UNIQUEIDENTIFIER NOT NULL,
               Name NVARCHAR(100) NULL,
               ParentCategoryId UNIQUEIDENTIFIER NULL);

       INSERT INTO @source
       VALUES (@id, @name, @parentCategoryId)

       SET XACT_ABORT ON
       BEGIN TRANSACTION

        MERGE pim.Category AS Target
        USING @source AS Source
           ON Source.Id = Target.Id
         WHEN NOT MATCHED BY Target THEN
              INSERT (
                     Id,
                     Name,
                     ParentCategoryId)

              VALUES (
                     Source.Id,
                     Source.Name,
                     Source.ParentCategoryId)

         WHEN MATCHED AND (
                     (Target.Name <> Source.Name) OR (Target.Name IS NULL AND Source.Name IS NOT NULL)
                  OR (Target.ParentCategoryId <> Source.ParentCategoryId) OR (Target.ParentCategoryId IS NULL AND Source.ParentCategoryId IS NOT NULL)
                  )
  
         THEN UPDATE 
                 SET Name = Source.Name,
                     ParentCategoryId = Source.ParentCategoryId; 

       COMMIT TRANSACTION;


       SELECT DISTINCT
              Category.Id,
              Category.Name,
              Category.ParentCategoryId

         FROM @source AS Source

              INNER JOIN pim.Category
                      ON Category.Id = Source.Id
END
GO