﻿CREATE TABLE [pim].[ProductBundleSet]
(
    [Id]                    UNIQUEIDENTIFIER CONSTRAINT [DF_ProductBundleSet_id] DEFAULT NEWID() NOT NULL,
    [ProductId]             UNIQUEIDENTIFIER NOT NULL,
    [BundleSetId]           UNIQUEIDENTIFIER NOT NULL,
    [IsAvailable]           BIT NULL,
    [IsDefault]             BIT NULL,
    [IsPhasedOut]           BIT NULL,
    [Ranking]               INT NULL,
    [Sku]                   NVARCHAR(50) NULL,
    [State]                 NVARCHAR(20) NULL,
    [CreatedAt]             DATETIME2 NULL,
    [UpdatedAt]             DATETIME2 NULL,
    [SystemCreatedOn]       DATETIME2 CONSTRAINT [DF_ProductBundleSet_SystemCreatedOn] DEFAULT (sysutcdatetime()) NOT NULL,
    [SystemModifiedOn]      DATETIME2 CONSTRAINT [DF_ProductBundleSet_SystemModifiedOn] DEFAULT (sysutcdatetime()) NOT NULL,
    CONSTRAINT              [PK_ProductBundleSet_Id] PRIMARY KEY CLUSTERED ([Id] ASC)
)
GO

CREATE UNIQUE INDEX IX_ProductBundleSet_ProductId
ON [pim].[ProductBundleSet] ([ProductId], [BundleSetId])
GO