namespace WSA.Integration.API.V1;

using Microsoft.Integration.Graph;
using Microsoft.Inventory.Item;
using WSA.Integration;


codeunit 50117 "WSA Category Handler" implements "WSA Integration Request"
{
    TableNo = "WSA Integration Request Log";

    trigger OnRun()
    begin
        Code(Rec);
    end;


    procedure HandleRequest(var Request: Record "WSA Integration Request Log")
    begin
        if not Codeunit.Run(Codeunit::"WSA Category Handler", Request) then begin
            Common.SetErrorResponse(Request, '');
        end;
    end;


    local procedure Code(var Request: Record "WSA Integration Request Log")
    var
        json: JsonObject;

    begin
        case Request.Method of
            Request.Method::post:
                HandlePost(Request);
            Request.Method::get:
                HandleGet(Request);
        end;
    end;


    local procedure HandlePost(var Request: Record "WSA Integration Request Log")
    var
        category: Record "Item Category";

    begin
        if TryHandlePost(Request, category) then
            Common.SetCreatedResponse(Request, Common.CategoryToJson(category))
        else
            Common.SetErrorResponse(Request, '');
    end;


    local procedure HandleGet(var Request: Record "WSA Integration Request Log")
    var
        category: Record "Item Category";

    begin
        if not TryHandleGet(Request, category) then
            Common.SetErrorResponse(Request, '');
    end;


    [TryFunction]
    local procedure TryHandlePost(
        var Request: Record "WSA Integration Request Log";
        var Category: Record "Item Category")

    var
        recRef: RecordRef;
        json: JsonObject;

    begin
        Request.TestField("Request Content");
        json := Common.GetJsonFromBlob(Request);

        if not Category.Get(Common.GetJsonValue(json, '$.code').AsCode()) then begin
            Category.Init();
            Category.Code := Common.GetJsonValue(json, '$.code').AsCode();
            Category.Insert(false);
        end;

        recRef.GetTable(Category);
        Common.ValidateFieldFromJson(json, '$.name', recRef, Category.FieldNo(Description), TempFieldSet);

        GraphMgtGeneralTools.ProcessNewRecordFromAPI(recRef, TempFieldSet, CurrentDateTime());

        recRef.SetTable(Category);
        Category.Modify();
    end;


    [TryFunction]
    local procedure TryHandleGet(
        var Request: Record "WSA Integration Request Log";
        var Category: Record "Item Category")

    var
        recRef: RecordRef;
        json: JsonObject;

    begin
        Request.TestField("Request Content");
        json := Common.GetJsonFromBlob(Request);

        if not (Common.GetJsonValue(json, '$.id').IsNull) then begin
            if Category.GetBySystemId(Common.GetJsonValue(json, '$.id').AsText()) then begin
                Common.SetOkResponse(Request, Common.CategoryToJson(Category));
                exit;
            end;
        end;

        if not (Common.GetJsonValue(json, '$.code').IsNull) then begin
            Category.SetRange(Code, Common.GetJsonValue(json, '$.code').AsCode());
            if Category.FindFirst() then begin
                Common.SetOkResponse(Request, Common.CategoryToJson(Category));
                exit;
            end
        end;

        if not (Common.GetJsonValue(json, '$.name').IsNull) then begin
            Category.SetRange(Description, Common.GetJsonValue(json, '$.name').AsCode());
            if Category.FindFirst() then begin
                Common.SetOkResponse(Request, Common.CategoryToJson(Category));
                exit;
            end
        end;

        Common.SetNoContentResponse(Request);
    end;


    var
        TempFieldSet: Record 2000000041 temporary;
        GraphMgtGeneralTools: Codeunit "Graph Mgt - General Tools";
        Common: Codeunit "WSA Common";
}
