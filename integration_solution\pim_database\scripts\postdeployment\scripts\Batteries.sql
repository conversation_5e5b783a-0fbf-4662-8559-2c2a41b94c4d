﻿DECLARE @batteries TABLE (
       Code NVARCHAR(20), 
       Name NVARCHAR(100))

INSERT INTO @batteries VALUES
       ('675', '675'),
       ('10', '10'),
       ('LILON', 'Lilon'),
       ('312', '312'),
       ('13', '13'),
       ('RECHARGEABLE', 'RECHARGEAB<PERSON>'),
       ('N.A.', 'N.A.')

 MERGE dbo.Battery AS Target
 USING @batteries AS Source
    ON Source.Code = Target.Code

  WHEN NOT MATCHED BY Target THEN
INSERT (Code, [Name])
VALUES (Source.Code, Source.[Name])

 WHEN MATCHED AND (Source.Name != Target.Name) THEN
UPDATE
   SET Target.Name = Source.Name
;


DECLARE @batteryCoupling TABLE (Code NVARCHAR(20), ExternalCode NVARCHAR(255))
INSERT INTO @batteryCoupling VALUES 
('675', '675'),
('10', '10'),
('LILON', 'LILON'),
('312', '312'),
('13', '13'),
('RECHAR<PERSON>ABLE', 'RECHAR<PERSON><PERSON><PERSON>'),
('N.A.', 'N.A.');

  WITH Source AS (
SELECT (SELECT TOP 1 Id FROM dbo.ExternalSystem WHERE Code = 'PIM') AS ExternalSystemId,
       (SELECT TOP 1 Id FROM dbo.Entity WHERE Code = 'BATTERY') AS EntityId,
       Battery.Id AS RecordId,
       m1.ExternalCode AS ExternalRecordId
  FROM dbo.Battery
 INNER JOIN @batteryCoupling AS m1
    ON Battery.Code = m1.Code
)

INSERT INTO dbo.Coupling (ExternalSystemId, EntityId, RecordId, ExternalRecordId)
SELECT ExternalSystemId, EntityId, RecordId, ExternalRecordId
  FROM Source

WHERE NOT EXISTS (SELECT * FROM dbo.Coupling 
                   WHERE ExternalSystemId = Source.ExternalSystemId
                     AND EntityId = Source.EntityId
                     AND RecordId = Source.RecordId)