﻿openapi: 3.0.1

info:
  title: v1.0
  description: API's to interact with PIM data elements
  version: '1.0'

servers:
  - url: 'https://retail-integration-we-dev.azure-api.net/pim/v1.0'
  
paths:
  /attributes:
    post:
      tags:
        - attributes
      summary: AttributePost
      description: Post attribute
      operationId: post-attribute
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/post-attribute-request-schema'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/post-attribute-response-schema'

  /categories:
    post:
      tags:
        - categories
      summary: CategoryPost
      description: Post Category
      operationId: post-category
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/post-category-schema'
            example:
              id: 805c8cc4-9dc1-4dd0-aabf-b10efb7f3790
              name: Signia
              parentCategoryId: null

      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/post-category-schema'
              example:
                  id: 805c8cc4-9dc1-4dd0-aabf-b10efb7f3790
                  name: Signia
                  parentCategoryId: null

  /colors:
    post:
      tags:
        - colors
      summary: ColorPost
      description: Post Color
      operationId: post-color
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/post-color-request-schema'
            example:
              code: BG
              name: beige
              hexCode: #d69c4d

      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/post-color-response-schema'
              example:
                  id: 805c8cc4-9dc1-4dd0-aabf-b10efb7f3790
                  code: BG
                  name: beige
                  hexCode: #d69c4d

  /products:
    post:
      tags:
        - products
      summary: Upsert product
      operationId: post-product
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/post-product-schema'
            example:
              id: 5cb2b1cd-1a78-49c7-afe4-93de440f3b18
              name: HA PURE CHARGE&GO 7X BG
              colorId: 805c8cc4-9dc1-4dd0-aabf-b10efb7f3790
              isAvailable: true
              isPhasedOut: false
              listPrice: 0
              productSource: INFOR
              productType: PRODUCT_VARIANT
              ranking: 401
              releaseDate: "2022-08-26T10:56:00"
              sku: "10963600"
              state: UPDATED
              createdAt: "2021-09-29T15:00:57.19092"
              updatedAt: "2024-05-22T01:20:51.047681"

      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/post-product-schema'
              example:
                id: 5cb2b1cd-1a78-49c7-afe4-93de440f3b18
                name: HA PURE CHARGE&GO 7X BG
                colorId: 805c8cc4-9dc1-4dd0-aabf-b10efb7f3790
                isAvailable: true
                isPhasedOut: false
                listPrice: 0
                productSource: INFOR
                productType: PRODUCT_VARIANT
                ranking: 401
                releaseDate: "2022-08-26T10:56:00"
                sku: "10963600"
                state: UPDATED
                createdAt: "2021-09-29T15:00:57.19092"
                updatedAt: "2024-05-22T01:20:51.047681"

  /products/{productId}:
    get:
      tags:
        - products
      summary: Get product
      parameters:
        - in: path
          name: productId
          schema:
            type: string
            format: uuid
          required: true
      operationId: get-product
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/post-product-schema'
              example:
                id: 5cb2b1cd-1a78-49c7-afe4-93de440f3b18
                name: HA PURE CHARGE&GO 7X BG
                colorId: 805c8cc4-9dc1-4dd0-aabf-b10efb7f3790
                isAvailable: true
                isPhasedOut: false
                listPrice: 0
                productSource: INFOR
                productType: PRODUCT_VARIANT
                ranking: 401
                releaseDate: "2022-08-26T10:56:00"
                sku: "10963600"
                state: UPDATED
                createdAt: "2021-09-29T15:00:57.19092"
                updatedAt: "2024-05-22T01:20:51.047681"

components:
  schemas:
    post-attribute-request-schema:
      type: object
      required:
        - code
        - name
      properties:
        code:
          type: string
          maxLength: 30
          minLength: 1
        name:
          maxLength: 100
          minLength: 1
          type: string
        dataType:
          type: string
          maxLength: 100
          nullable: true

    post-attribute-response-schema:
      type: object
      required:
        - id
        - code
        - name
      properties:
        id:
          type: string
          format: uuid
        code:
          type: string
          maxLength: 30
          minLength: 1
        name:
          maxLength: 100
          minLength: 1
          type: string
        dataType:
          type: string
          maxLength: 100
          nullable: true

    post-category-schema:
      required:
        - id
        - name
      type: object
      properties:
        id:
          type: string
          format: uuid
        name:
          maxLength: 100
          minLength: 1
          type: string
        parentCategoryId:
          type: string
          format: uuid
          nullable: true

    post-color-request-schema:
      type: object
      required:
        - code
        - name
      properties:
        code:
          type: string
          maxLength: 20
          minLength: 1
        name:
          maxLength: 100
          minLength: 1
          type: string
        hexCode:
          type: string
          maxLength: 20
          nullable: true

    post-color-response-schema:
      type: object
      required:
        - id
        - code
        - name
      properties:
        id:
          type: string
          format: uuid
        code:
          type: string
          maxLength: 20
          minLength: 1
        name:
          maxLength: 100
          minLength: 1
          type: string
        hexCode:
          type: string
          maxLength: 20
          nullable: true

    post-product-schema:
      required:
        - id
        - name
      type: object
      properties:
        id:
          type: string
          format: uuid
        name:
          maxLength: 100
          minLength: 1
          type: string
        colorId:
          type: string
          format: uuid
          nullable: true
        isAvailable:
          type: boolean
          nullable: true
        isPhasedOut:
          type: boolean
          nullable: true
        listPrice:
          type: number
          nullable: true
        productSource:
          type: string
          maxLength: 20
          nullable: true
        productType:
          type: string
          maxLength: 20
          nullable: true
        ranking:
          type: integer
          nullable: true
        releaseDate:
          type: string
          format: date
          nullable: true
        sku:
          type: string
          nullable: true
        state:
          type: string
          nullable: true
        createdAt:
          type: string
          format: date-time
          nullable: true
        updatedAt:
          type: string
          format: date-time
          nullable: true

