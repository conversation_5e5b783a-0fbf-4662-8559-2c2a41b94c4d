﻿using WSA.Retail.Integration.PIM.Models.Countries;
using WSA.Retail.Integration.PIM.Models.Products;

namespace WSA.Retail.Integration.PIM.Models.Brands;

public class PimBrandEntity : IPimBrandEntity, WSA.Retail.Integration.Core.IIdentifiable
{
    public required Guid Id { get; set; } = Guid.Empty;
    public required string Code { get; set; }
    public string? Name { get; set; }
    public required DateTime SystemCreatedOn { get; set; }
    public required DateTime SystemModifiedOn { get; set; }

    public virtual ICollection<PimProductEntity> Products { get; set; } = [];

    public virtual ICollection<PimCountryEntity> Countries { get; set; } = [];
}