﻿CREATE TABLE [cosium].[categorierdv](
	[id] [int] NOT NULL,
	[nomcategorie] [varchar](50) NULL,
	[couleur] [varchar](50) NULL,
	[refentite] [varchar](50) NULL,
	[famille] [varchar](50) NULL,
	[cachecat] [smallint] NULL,
	[dureedefaut] [varchar](50) NULL,
	[objetdefaut] [varchar](30) NULL,
	[datemodif] [varchar](50) NULL,
	[CreatedOn] [datetime2](7) NOT NULL,
	[ModifiedOn] [datetime2](7) NOT NULL,
 CONSTRAINT [PK_categorierdv] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [cosium].[categorierdv] ADD  CONSTRAINT [DF_categorierdv_CreatedOn]  DEFAULT (sysutcdatetime()) FOR [CreatedOn]
GO

ALTER TABLE [cosium].[categorierdv] ADD  CONSTRAINT [DF_categorierdv_ModifiedOn]  DEFAULT (sysutcdatetime()) FOR [ModifiedOn]
GO
