﻿using Moq;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using WSA.Retail.Integration.Configuration;
using WSA.Retail.Integration.Data;
using WSA.Retail.Integration.Models.Patients;
using WSA.Retail.Integration.Tests.Configuration;
using WSA.Retail.Integration.Models.Couplings;
using Azure.Storage.Queues;
using WSA.Retail.Integration.Core;

namespace WSA.Retail.Integration.Tests.Data.Repositories;

public class PatientRepositoryTests
{
    private readonly string _dbName;

    private readonly Mock<IOptions<AppSettings>> _optionsMock;
    private readonly Mock<ILogger<PatientRepositoryService>> _loggerMock;
    private readonly Mock<IDbContextFactory<IntegrationContext>> _dbContextFactoryMock;
    private readonly Mock<IMasterDataQueryBuilder<Patient, PatientEntity>> _queryBuilder;
    private readonly PatientRepositoryService _repository;

    public PatientRepositoryTests()
    {
        _optionsMock = new Mock<IOptions<AppSettings>>();
        _optionsMock.Setup(o => o.Value).Returns(TestAppSettings.CreateDefault());
        _loggerMock = new Mock<ILogger<PatientRepositoryService>>();
        _queryBuilder = new Mock<IMasterDataQueryBuilder<Patient, PatientEntity>>();


        _dbName = $"TestDb_{Guid.NewGuid()}";
        _dbContextFactoryMock = new Mock<IDbContextFactory<IntegrationContext>>();
        _dbContextFactoryMock
            .Setup(f => f.CreateDbContext())
            .Returns(() => TestDbContextHelper.GetInMemoryDbContext(_dbName));
        var queueClients = new Dictionary<string, QueueClient>();
        _repository = new PatientRepositoryService(
            _optionsMock.Object,
            _loggerMock.Object,
            _dbContextFactoryMock.Object,
            _queryBuilder.Object);

        SeedDatabase();
    }

    private void SeedDatabase()
    {
        using var context = TestDbContextHelper.GetInMemoryDbContext(_dbName);

        // Add patients with various patterns
        var patients = new List<PatientEntity>();
        var couplings = new List<CouplingEntity>();

        var patient1 = new PatientEntity()
        {
            Id = Guid.NewGuid(),
            Code = "PAT1",
            Name = "Patient1 Name",
            AlternateCode = "ALTERNATECODE",
            CreatedOn = DateTime.UtcNow,
            ModifiedOn = DateTime.UtcNow
        };
        patients.Add(patient1);

        couplings.Add(new()
        {
            Id = Guid.NewGuid(),
            ExternalSystemId = TestReferenceData.TestSystem1Id,
            EntityId = TestReferenceData.PatientId,
            RecordId = patient1.Id,
            ExternalRecordId = "EXT1-PAT1",
            CreatedOn = DateTime.UtcNow,
            ModifiedOn = DateTime.UtcNow
        });

        couplings.Add(new()
        {
            Id = Guid.NewGuid(),
            ExternalSystemId = TestReferenceData.TestSystem2Id,
            EntityId = TestReferenceData.PatientId,
            RecordId = patient1.Id,
            ExternalRecordId = "EXT2-PAT1",
            CreatedOn = DateTime.UtcNow,
            ModifiedOn = DateTime.UtcNow
        });

        context.PatientEntity.AddRange(patients);
        context.CouplingEntity.AddRange(couplings);
        context.SaveChanges();
    }

 
    [Fact]
    public async Task GetAsync_GetByCodeShouldReturnPatient()
    {
        // Act
        var result1 = await _repository.GetAsync(
            externalSystemCode: "TEST1",
            code: "PAT1");

        var result2 = await _repository.GetAsync(
            externalSystemCode: "TEST2",
            code: "PAT1");

        // Assert
        Assert.NotNull(result1);
        Assert.Equal("Patient1 Name", result1.Name);
        Assert.Equal("TEST1", result1.ExternalSystemCode);
        Assert.Equal("EXT1-PAT1", result1.ExternalCode);

        Assert.NotNull(result2);
        Assert.Equal("Patient1 Name", result2.Name);
        Assert.Equal("TEST2", result2.ExternalSystemCode);
        Assert.Equal("EXT2-PAT1", result2.ExternalCode);
    }

    [Fact]
    public async Task GetAsync_GetByExtCodeShouldReturnPatient()
    {
        // Act
        var result1 = await _repository.GetAsync(
            externalSystemCode: "TEST1",
            externalCode: "EXT1-PAT1");

        var result2 = await _repository.GetAsync(
            externalSystemCode: "TEST2",
            externalCode: "EXT2-PAT1");

        // Assert
        Assert.NotNull(result1);
        Assert.Equal("Patient1 Name", result1.Name);
        Assert.Equal("TEST1", result1.ExternalSystemCode);
        Assert.Equal("EXT1-PAT1", result1.ExternalCode);

        Assert.NotNull(result2);
        Assert.Equal("Patient1 Name", result2.Name);
        Assert.Equal("TEST2", result2.ExternalSystemCode);
        Assert.Equal("EXT2-PAT1", result2.ExternalCode);
    }
}