﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using WSA.Retail.Integration.Logging;
using WSA.Retail.Integration.Manage.Configuration;
using WSA.Retail.Integration.Models.Categories;
using WSA.Retail.Integration.Utilities;

namespace WSA.Retail.Integration.Manage.EventProcessing;

class CategoryEventHandler(
    IOptions<AppSettings> appSettings,
    ILogger<CategoryEventHandler> logger,
    ICategoryService categoryService)
{
    private readonly AppSettings _appSettings = appSettings.Value;
    private readonly ILogger<CategoryEventHandler> _logger = logger;
    private readonly ICategoryService _categoryService = categoryService;

    [Function("CategoryGetById")]
    public async Task<IActionResult> GetByIdAsync([HttpTrigger(AuthorizationLevel.Function, "get", Route = "categories/{id}")] HttpRequest req, string id)
    {
        _logger.LogMethodStart(_appSettings.AppName);

        bool raiseEvent = bool.Parse(Common.GetQueryValue(req.Query, "raiseEvent") ?? "false");

        try
        {
            var recordList = await _categoryService.GetListAsync(
                _appSettings.ExternalSystemCode,
                id: Guid.Parse(id));

            if (recordList != null && recordList.Count > 0)
            {
                if (raiseEvent)
                {
                    var taskList = new List<Task>();
                    foreach (Category record in recordList)
                    {
                        taskList.Add(_categoryService.RaiseMockEventAsync(record));
                    }
                    await Task.WhenAll(taskList);
                }

                return new OkObjectResult(recordList.First());
            }
            else if (recordList != null && recordList.Count == 0)
            {
                return new OkObjectResult(null);
            }
            else
            {
                return new BadRequestResult();
            }
        }
        catch (Exception ex)
        {
            return new BadRequestObjectResult(ex.Message);
            throw;
        }
    }

    [Function("CategoryGetByQuery")]
    public async Task<IActionResult> GetByQueryAsync([HttpTrigger(AuthorizationLevel.Function, "get", Route = "categories")] HttpRequest req)
    {
        _logger.LogMethodStart(_appSettings.AppName);

        string? idString = Common.GetQueryValue(req.Query, "id");
        string? code = Common.GetQueryValue(req.Query, "code");
        string? externalCode = Common.GetQueryValue(req.Query, "externalCode");
        string? name = Common.GetQueryValue(req.Query, "name");
        bool raiseEvent = bool.Parse(Common.GetQueryValue(req.Query, "raiseEvent") ?? "false");

        try
        {
            var recordList = await _categoryService.GetListAsync(
                _appSettings.ExternalSystemCode,
                code: code,
                externalCode: externalCode,
                name: name);

            if (recordList != null && recordList.Count > 0)
            {
                if (raiseEvent)
                {
                    var taskList = new List<Task>();
                    foreach (Category record in recordList)
                    {
                        taskList.Add(_categoryService.RaiseMockEventAsync(record));
                    }
                    await Task.WhenAll(taskList);
                }

                return new OkObjectResult(recordList);
            }
            else if (recordList != null && recordList.Count == 0)
            {
                return new OkObjectResult(null);
            }
            else
            {
                return new BadRequestResult();
            }
        }
        catch (Exception ex)
        {
            return new BadRequestObjectResult(ex.Message);
            throw;
        }
    }
}
