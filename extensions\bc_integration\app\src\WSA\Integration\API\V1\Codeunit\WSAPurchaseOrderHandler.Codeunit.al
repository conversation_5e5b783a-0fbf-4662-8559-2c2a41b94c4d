namespace WSA.Integration.API.V1;

using Microsoft.Integration.Graph;
using Microsoft.Inventory.Item;
using Microsoft.Purchases.Document;
using Microsoft.Purchases.Vendor;
using WSA.Integration;


codeunit 50120 "WSA Purchase Order Handler" implements "WSA Integration Request"
{
    TableNo = "WSA Integration Request Log";

    trigger OnRun()
    begin
        Code(Rec);
    end;


    procedure HandleRequest(var Request: Record "WSA Integration Request Log")
    begin
        if not Codeunit.Run(Codeunit::"WSA Purchase Order Handler", Request) then begin
            Common.SetErrorResponse(Request, '');
        end;
    end;


    local procedure Code(var Request: Record "WSA Integration Request Log")
    var
        json: JsonObject;

    begin
        case Request.Method of
            Request.Method::post:
                HandlePost(Request);
            Request.Method::get:
                HandleGet(Request);
        end;
    end;


    local procedure HandlePost(var Request: Record "WSA Integration Request Log")
    var
        purchaseOrder: Record "Purchase Header";

    begin
        if TryHandlePost(Request, purchaseOrder) then
            Common.SetCreatedResponse(Request, Common.PurchaseOrderToJson(purchaseOrder))
        else
            Common.SetErrorResponse(Request, '');
    end;


    local procedure HandleGet(var Request: Record "WSA Integration Request Log")
    var
        purchaseOrder: Record "Purchase Header";

    begin
        if not TryHandleGet(Request, purchaseOrder) then
            Common.SetErrorResponse(Request, '');
    end;


    [TryFunction]
    local procedure TryHandlePost(
        var Request: Record "WSA Integration Request Log";
        var PurchaseOrder: Record "Purchase Header")

    var
        purchaseLine: Record "Purchase Line";
        recRef: RecordRef;
        json: JsonObject;
        lines: JsonToken;
        line: JsonToken;

    begin
        Request.TestField("Request Content");
        json := Common.GetJsonFromBlob(Request);

        UpdateMetadData(json, Request);
        Commit();

        ValidateExternalReferences(json);

        if not PurchaseOrder.Get(PurchaseOrder."Document Type"::Order, Common.GetJsonValue(json, '$.documentNumber').AsCode()) then begin
            PurchaseOrder.Init();
            PurchaseOrder."Document Type" := PurchaseOrder."Document Type"::Order;
            PurchaseOrder."No." := Common.GetJsonValue(json, '$.documentNumber').AsCode();
            PurchaseOrder.Validate("Buy-from Vendor No.", Common.GetJsonValue(json, '$.vendor.code').AsCode());
            PurchaseOrder.Validate("Responsibility Center", Common.GetJsonValue(json, '$.clinic.code').AsCode());
            PurchaseOrder.Insert(false);
        end;

        if PurchaseOrder.Status <> PurchaseOrder.Status::Open then
            PurchaseOrder.Status := PurchaseOrder.Status::Open;

        recRef.GetTable(PurchaseOrder);
        Common.ValidateFieldFromJson(json, '$.alternateNumber', recRef, PurchaseOrder.FieldNo("Vendor Order No."), TempFieldSet);
        Common.ValidateFieldFromJson(json, '$.documentDate', recRef, PurchaseOrder.FieldNo("Posting Date"), TempFieldSet);
        Common.ValidateFieldFromJson(json, '$.documentDate', recRef, PurchaseOrder.FieldNo("Document Date"), TempFieldSet);
        recRef.SetTable(PurchaseOrder);
        PurchaseOrder.Modify();

        if json.SelectToken('$.purchaseOrderLines', lines) then begin
            if lines.IsArray then begin
                foreach line in lines.AsArray() do begin
                    if not purchaseLine.Get(PurchaseOrder."Document Type", PurchaseOrder."No.",
                            Common.GetJsonValue(line.AsObject(), '$.sequence').AsInteger()) then begin
                        purchaseLine.Init();
                        purchaseLine."Document Type" := PurchaseOrder."Document Type";
                        purchaseLine."Document No." := PurchaseOrder."No.";
                        purchaseLine."Line No." := Common.GetJsonValue(line.AsObject(), '$.sequence').AsInteger();
                        purchaseLine.Insert();
                    end;

                    if (purchaseLine.Type <> purchaseLine.Type::Item) then
                        purchaseLine.Validate(Type, purchaseLine.Type::Item);
                    recRef.GetTable(purchaseLine);
                    Common.ValidateFieldFromJson(line.AsObject(), '$.product.code', recRef, purchaseLine.FieldNo("No."), TempFieldSet);
                    Common.ValidateFieldFromJson(line.AsObject(), '$.description', recRef, purchaseLine.FieldNo(Description), TempFieldSet);
                    Common.ValidateFieldFromJson(line.AsObject(), '$.quantity', recRef, purchaseLine.FieldNo(Quantity), TempFieldSet);
                    recRef.SetTable(purchaseLine);

                    purchaseLine.Validate("Qty. to Receive", 0);
                    purchaseLine.Validate("Qty. to Invoice", 0);
                    purchaseLine.Modify();
                end;
            end;
        end;

        PurchaseOrder.Status := PurchaseOrder.Status::Released;
        PurchaseOrder.Modify();

        Request."Purchase Order No." := PurchaseOrder."No.";
        Request.Modify();
    end;


    [TryFunction]
    local procedure TryHandleGet(
        var Request: Record "WSA Integration Request Log";
        var PurchaseOrder: Record "Purchase Header")

    var
        recRef: RecordRef;
        json: JsonObject;

    begin
        Request.TestField("Request Content");
        json := Common.GetJsonFromBlob(Request);

        if not (Common.GetJsonValue(json, '$.id').IsNull) then begin
            if PurchaseOrder.GetBySystemId(Common.GetJsonValue(json, '$.id').AsText()) then begin
                Request."Purchase Order No." := PurchaseOrder."No.";
                Request.Modify();
                Common.SetOkResponse(Request, Common.PurchaseOrderToJson(PurchaseOrder));
                exit;
            end;
        end;

        if not (Common.GetJsonValue(json, '$.documentNumber').IsNull) then begin
            PurchaseOrder.SetRange("Document Type", PurchaseOrder."Document Type"::Order);
            PurchaseOrder.SetRange("No.", Common.GetJsonValue(json, '$.documentNumber').AsCode());
            if PurchaseOrder.FindFirst() then begin
                Request."Purchase Order No." := PurchaseOrder."No.";
                Request.Modify();
                Common.SetOkResponse(Request, Common.PurchaseOrderToJson(PurchaseOrder));
                exit;
            end
        end;

        Common.SetNoContentResponse(Request);
    end;

    local procedure UpdateMetadData(json: JsonObject; var Request: Record "WSA Integration Request Log")
    var
        jValue: JsonValue;

    begin
        jValue := Common.GetJsonValue(json, '$.documentNumber');
        if not jValue.IsNull then begin
            Request."Purchase Order No." := jValue.AsCode();
            Request."Document No." := jValue.AsCode();
        end;

        jValue := Common.GetJsonValue(json, '$.documentDate');
        if not jValue.IsNull then Request."Document Date" := jValue.AsDate();

        jValue := Common.GetJsonValue(json, '$.clinic.code');
        if not jValue.IsNull then Request."Clinic No." := jValue.AsCode();

        jValue := Common.GetJsonValue(json, '$.vendor.code');
        if not jValue.IsNull then Request."Vendor No." := jValue.AsCode();

        Request.Modify();
    end;

    local procedure ValidateExternalReferences(json: JsonObject)
    begin
        ValidateVendor(json);
        ValidateProducts(json);
    end;


    local procedure ValidateVendor(json: JsonObject): Boolean
    var
        Vendor: Record Vendor;
        IntegrationManagement: Codeunit "Integration Management";
        jValue: JsonValue;

    begin
        jValue := Common.GetJsonValue(json, '$.vendor.code');
        if jValue.IsNull() then
            exit(false);

        if not Vendor.Get(jValue.AsCode()) then
            exit(IntegrationManagement.GetVendor(jValue.AsCode(), Vendor));
    end;


    local procedure ValidateProducts(json: JsonObject): Boolean
    var
        jToken: JsonToken;
        lineToken: JsonToken;

    begin
        Json.SelectToken('$.purchaseOrderLines', jToken);
        foreach lineToken in jToken.AsArray do begin
            ValidateProduct(lineToken.AsObject());
        end;
    end;


    local procedure ValidateProduct(json: JsonObject): Boolean
    var
        Item: Record Item;
        IntegrationManagement: Codeunit "Integration Management";
        jValue: JsonValue;

    begin
        jValue := Common.GetJsonValue(json, '$.product.code');
        if jValue.IsNull() then
            exit(false);

        if not Item.Get(jValue.AsCode()) then
            exit(IntegrationManagement.GetProduct(jValue.AsCode(), Item));
    end;



    var
        TempFieldSet: Record 2000000041 temporary;
        GraphMgtGeneralTools: Codeunit "Graph Mgt - General Tools";
        Common: Codeunit "WSA Common";
}
