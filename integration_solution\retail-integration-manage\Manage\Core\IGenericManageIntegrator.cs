﻿using WSA.Retail.Integration.Core;

namespace WSA.Retail.Integration.Manage.Core;

public interface IGenericManageIntegrator<
    TEntity,
    TResponseEntity>

    where TEntity : IMasterData
    where TResponseEntity : class

{
    public Task<TResponseEntity?> GetFromManageAsync(Guid id);

    public Task<List<TResponseEntity>> GetListFromManageAsync();

    public Task<TEntity?> UpdateManageAsync(TEntity domainEntity);

    public Task<TEntity?> PostToManageAsync(TEntity domainEntity);

    public Task<TEntity?> PutToManageAsync(TEntity existingDomainEntity, TResponseEntity existingManageEntity);
}
