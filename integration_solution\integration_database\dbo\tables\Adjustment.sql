﻿CREATE TABLE [dbo].[Adjustment]
(
    [Id]                UNIQUEIDENTIFIER CONSTRAINT [DF_Adjustment_Id] DEFAULT NEWID() NOT NULL,
    [DocumentNumber]    NVARCHAR(20) NOT NULL,
    [ProductId]         UNIQUEIDENTIFIER NULL,
    [ClinicId]          UNIQUEIDENTIFIER NULL,
    [DocumentDate]      DATE NULL,
    [Quantity]          DECIMAL NULL,
    [SerialNumber]      NVARCHAR(30) NULL,
    [IntegrateWithPos]  BIT NULL,
    [CreatedOn]         DATETIME2 CONSTRAINT [DF_Adjustment_CreatedOn] DEFAULT (sysutcdatetime())  NOT NULL,
    [ModifiedOn]        DATETIME2 CONSTRAINT [DF_Adjustment_ModifiedOn] DEFAULT (sysutcdatetime())  NOT NULL,
    CONSTRAINT [PK_Adjustment_Id] PRIMARY KEY CLUSTERED ([Id] ASC),
    CONSTRAINT [FK_Adjustment_Product] FOREIGN KEY (ProductId) REFERENCES [dbo].[Product](Id),
    CONSTRAINT [FK_Adjustment_Clinic] FOREIGN KEY (ClinicId) REFERENCES [dbo].[Clinic](Id)
)
GO

CREATE UNIQUE INDEX IX_Adjustment_DocumentNumber
ON [dbo].[Adjustment] ([DocumentNumber])
GO

CREATE INDEX IX_Adjustment_IntegrateWithPos_CreatedOn
ON [dbo].[Adjustment] ([IntegrateWithPos], [CreatedOn])