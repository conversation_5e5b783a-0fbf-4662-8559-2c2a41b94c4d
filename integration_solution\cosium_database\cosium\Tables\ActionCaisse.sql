﻿CREATE TABLE [cosium].[ActionCaisse] (
    [id]                  NVARCHAR (50)  NOT NULL,
    [refcentre]           NVARCHAR (50)  NULL,
    [totalespece]         NVARCHAR (50)  NULL,
    [actioncaisse]        NVARCHAR (50)  NULL,
    [codecomptablecaisse] NVARCHAR (50)  NULL,
    [tvacaisse]           NVARCHAR (50)  NULL,
    [attentecaisse]       NVARCHAR (50)  NULL,
    [descriptioncaisse]   NVARCHAR (MAX) NULL,
    [datecreation]        NVARCHAR (50)  NULL,
    [datecaisse]          NVARCHAR (50)  NULL,
    [refcoordbancaires]   NVARCHAR (50)  NULL,
    [datedepotbanque]     NVARCHAR (50)  NULL,
    [numerobordereau]     NVARCHAR (50)  NULL,
    [dateexportcaisse]    NVARCHAR (50)  NULL,
    [datemodif]           NVARCHAR (50)  NULL,
    [CreatedOn]           DATETIME2 CONSTRAINT [DF_ActionCaisse_CreatedOn] DEFAULT (sysutcdatetime()) NOT NULL,
    [ModifiedOn]          DATETIME2 CONSTRAINT [DF_ActionCaisse_ModifiedOn] DEFAULT (sysutcdatetime()) NOT NULL,
    PRIMARY KEY CLUSTERED ([id] ASC)
);
GO

CREATE TRIGGER [cosium].ActionCaisse_UpdateModified
ON [cosium].[ActionCaisse]
AFTER UPDATE 
AS
   UPDATE [cosium].[ActionCaisse]
   SET [ModifiedOn] = sysutcdatetime()
   FROM Inserted AS i
   WHERE [cosium].[ActionCaisse].[id] = i.[id]
GO