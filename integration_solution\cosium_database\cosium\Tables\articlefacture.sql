﻿CREATE TABLE [cosium].[articlefacture] (
    [id]                                  NVARCHAR (50)  NOT NULL,
    [reffacture]                          NVARCHAR (50)  NULL,
    [refarticle]                          NVARCHAR (50)  NULL,
    [qtearticle]                          NVARCHAR (50)  NULL,
    [qtearticle_dec]                      DECIMAL(18,4)  NULL,
    [prixunitht]                          NVARCHAR (50)  NULL,
    [prixunitht_dec]                      DECIMAL(18,4)  NULL,
    [prixunitttc]                         NVARCHAR (50)  NULL,
    [prixunitttc_dec]                     DECIMAL(18,4)  NULL,
    [remise]                              NVARCHAR (50)  NULL,
    [remise_dec]                          DECIMAL(18,4)  NULL,
    [typeremise]                          NVARCHAR (50)  NULL,
    [totalarticlettc]                     NVARCHAR (50)  NULL,
    [totalarticlettc_dec]                 DECIMAL(18,4)  NULL,
    [tauxtva]                             NVARCHAR (50)  NULL,
    [tauxtva_dec]                         DECIMAL(18,4)  NULL,
    [codeproduit]                         NVARCHAR (50)  NULL,
    [libelle]                             NVARCHAR (255) NULL,
    [codecomptable]                       NVARCHAR (50)  NULL,
    [refarticlefactureorigine]            NVARCHAR (50)  NULL,
    [numserieaf]                          NVARCHAR (50)  NULL,
    [refproduit]                          NVARCHAR (50)  NULL,
    [cote]                                NVARCHAR (50)  NULL,
    [refvendeur]                          NVARCHAR (50)  NULL,
    [commentaireaf]                       NVARCHAR (100) NULL,
    [typeremisemajoration]                NVARCHAR (50)  NULL,
    [htouttc]                             NVARCHAR (50)  NULL,
    [soldemanuellement]                   NVARCHAR (50)  NULL,
    [displaylpp]                          NVARCHAR (50)  NULL,
    [libelleremise]                       NVARCHAR (128) NULL,
    [reffamille]                          NVARCHAR (50)  NULL,
    [aftaxedeee]                          NVARCHAR (50)  NULL,
    [afcouleur]                           NVARCHAR (256) NULL,
    [datemodif]                           NVARCHAR (50)  NULL,
    [rang]                                NVARCHAR (50)  NULL,
    [origincarttype]                      NVARCHAR (50)  NULL,
    [supplierorderforminvoiceditem_id]    NVARCHAR (50)  NULL,
    [supplierdeliverynoteinvoiceditem_id] NVARCHAR (50)  NULL,
    [purchaseinvoiceditem_id]             NVARCHAR (50)  NULL,
    [CreatedOn]                           DATETIME2 CONSTRAINT [DF_articlefacture_CreatedOn] DEFAULT (sysutcdatetime()) NOT NULL,
    [ModifiedOn]                          DATETIME2 CONSTRAINT [DF_articlefacture_ModifiedOn] DEFAULT (sysutcdatetime()) NOT NULL,
    PRIMARY KEY CLUSTERED ([id] ASC)
);
GO

CREATE INDEX IX_articlefacture_reffacture
ON [cosium].[articlefacture] ([reffacture], [id])
GO;

CREATE TRIGGER [cosium].articlefacture_UpdateModified
ON [cosium].[articlefacture]
AFTER UPDATE 
AS
   UPDATE [cosium].[articlefacture]
   SET [ModifiedOn] = sysutcdatetime()
   FROM Inserted AS i
   WHERE [cosium].[articlefacture].[id] = i.[id]
GO