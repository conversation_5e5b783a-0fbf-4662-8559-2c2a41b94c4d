﻿CREATE VIEW [dbo].[PaymentMethods]
AS

  WITH Source AS (
SELECT PaymentMethod.Id,
       ExternalSystem.Code AS ExternalSystemCode,
       PaymentMethod.Code,
       LatestCouplings.ExternalCode,
       PaymentMethod.[Name],
       PaymentMethod.CreatedOn,
       PaymentMethod.ModifiedOn

  FROM dbo.PaymentMethod

 CROSS JOIN dbo.ExternalSystem

 OUTER APPLY (
       SELECT TOP 1 Coupling.ExternalRecordId AS ExternalCode
         FROM dbo.Coupling
        WHERE Coupling.ExternalSystemId = ExternalSystem.Id
          AND Coupling.RecordId = PaymentMethod.Id
        ORDER BY ModifiedOn DESC) AS LatestCouplings
     )

SELECT Source.Id,
       Source.ExternalSystemCode,
       Source.Code,
       Source.ExternalCode,
       Source.[Name],
       Source.[CreatedOn],
       Source.[ModifiedOn],
       (
       SELECT Source.Id AS 'id',
              Source.ExternalSystemCode AS 'externalSystemCode',
              Source.Code AS 'code',
              Source.ExternalCode AS 'externalCode',
              Source.[Name] AS 'name',
              Source.CreatedOn AS 'createdOn',
              Source.ModifiedOn AS 'modifiedOn'

           FOR JSON PATH, WITHOUT_ARRAY_WRAPPER
       ) AS JSON
  FROM Source