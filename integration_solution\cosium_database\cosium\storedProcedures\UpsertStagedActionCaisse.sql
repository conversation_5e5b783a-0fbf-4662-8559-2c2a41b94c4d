﻿CREATE PROCEDURE [cosium].[UpsertStagedActionCaisse]
AS

        SET XACT_ABORT ON
        BEGIN TRANSACTION
     
       UPDATE cosium.ActionCaisse
          SET [refcentre] = Source.[refcentre],
              [totalespece] = Source.[totalespece],
              [actioncaisse] = Source.[actioncaisse],
              [codecomptablecaisse] = Source.[codecomptablecaisse],
              [tvacaisse] = Source.[tvacaisse],
              [attentecaisse] = Source.[attentecaisse],
              [descriptioncaisse] = Source.[descriptioncaisse],
              [datecreation] = Source.[datecreation],
              [datecaisse] = Source.[datecaisse],
              [refcoordbancaires] = Source.[refcoordbancaires],
              [datedepotbanque] = Source.[datedepotbanque],
              [numerobordereau] = Source.[numerobordereau],
              [dateexportcaisse] = Source.[dateexportcaisse],
              [datemodif] = Source.[datemodif]

         FROM cosium.ActionCaisse

              INNER JOIN cosium.staging_ActionCaisse AS Source
                      ON ActionCaisse.id = Source.id

        WHERE ActionCaisse.datemodif <> Source.datemodif;

       INSERT INTO cosium.ActionCaisse (
              [id],
              [refcentre],
              [totalespece],
              [actioncaisse],
              [codecomptablecaisse],
              [tvacaisse],
              [attentecaisse],
              [descriptioncaisse],
              [datecreation],
              [datecaisse],
              [refcoordbancaires],
              [datedepotbanque],
              [numerobordereau],
              [dateexportcaisse],
              [datemodif]
              )

       SELECT Source.[id],
              Source.[refcentre],
              Source.[totalespece],
              Source.[actioncaisse],
              Source.[codecomptablecaisse],
              Source.[tvacaisse],
              Source.[attentecaisse],
              Source.[descriptioncaisse],
              Source.[datecreation],
              Source.[datecaisse],
              Source.[refcoordbancaires],
              Source.[datedepotbanque],
              Source.[numerobordereau],
              Source.[dateexportcaisse],
              Source.[datemodif]

         FROM cosium.staging_ActionCaisse AS Source

        WHERE NOT EXISTS (SELECT * FROM cosium.ActionCaisse WHERE id = Source.id)

     TRUNCATE TABLE cosium.staging_ActionCaisse
       
       COMMIT TRANSACTION;