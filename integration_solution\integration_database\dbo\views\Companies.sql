﻿CREATE VIEW [dbo].[Companies]
AS

  WITH Source AS (
SELECT Company.Id,
       ExternalSystem.Code AS ExternalSystemCode,
       Company.Code,
       LatestCouplings.ExternalCode,
       Company.[Name],
       Company.[Address],
       Company.Address2,
       Company.City,
       Company.Region,
       Company.Country,
       Company.PostalCode,
       Company.CreatedOn,
       Company.ModifiedOn

  FROM dbo.Company

 CROSS JOIN dbo.ExternalSystem

 OUTER APPLY (
       SELECT TOP 1 Coupling.ExternalRecordId AS ExternalCode
         FROM dbo.Coupling
        WHERE Coupling.ExternalSystemId = ExternalSystem.Id
          AND Coupling.RecordId = Company.Id
        ORDER BY ModifiedOn DESC) AS LatestCouplings
     )

SELECT Source.Id,
       Source.ExternalSystemCode,
       Source.Code,
       Source.ExternalCode,
       Source.[Name],
       Source.[Address],
       Source.Address2,
       Source.City,
       Source.Region,
       Source.Country,
       Source.PostalCode,
       Source.CreatedOn,
       Source.ModifiedOn,
       (
       SELECT Source.Id AS 'id',
              Source.ExternalSystemCode AS 'externalSystemCode',
              Source.Code AS 'code',
              Source.ExternalCode AS 'externalCode',
              Source.[Name] AS 'name',
              Source.[Address] AS 'address',
              Source.Address2 AS 'address2',
              Source.City AS 'city',
              Source.Region AS 'region',
              Source.Country AS 'country',
              Source.PostalCode AS 'postalCode',
              Source.CreatedOn AS 'createdOn',
              Source.ModifiedOn AS 'modifiedOn'

           FOR JSON PATH, WITHOUT_ARRAY_WRAPPER
       ) AS JSON

  FROM Source