﻿using Microsoft.Extensions.Logging;
using System.Text.Json;
using WSA.Retail.Integration.Core;
using WSA.Retail.Integration.Logging;

namespace WSA.Retail.Integration.Manage.EventProcessing;

public class EventHandlerBase<TEvent, TModel>(
    ILogger<TEvent> logger)
    : LoggingBase<TEvent>(logger), IEventHandler
    where TEvent : class, IIdentifiable
    where TModel : class, IIdentifiable
{
    public async Task<bool> HandleFromQueueAsync(EventHubMessage message)
    {
        LogMethodStart();

        var eventData = await GetEventDataFromMessageAsync(message);
        if (eventData == null)
        {
            LogCustomError("Event data is null after deserialization.");
            return false;
        }

        var modelData = await GetModelDataFromManageApiAsync(eventData);
        if (modelData == null)
        {
            LogCustomError("Model data is null after getting from Manage.");
            return false;
        }

        var existingModelData = await GetModelDataFromRepositoryAsync(modelData);
        if (existingModelData != null)
        {
            modelData.Id = existingModelData.Id;
        }

        modelData = await ValidateModelDataExternalReferencesAsync(modelData);

        //modelData = await Verify

        return true;
    }

    public async Task<bool> HandleToQueueAsync(Events.Event ev)
    {
        return false;
        /*
        var eventData = Deserialize<TModel>(ev);
        return await HandleToQueueInternalAsync(eventData);*/
    }

    private async Task<TEvent?> GetEventDataFromMessageAsync(EventHubMessage message)
    {
        var before = await OnBeforeDeserializeAsync(message);
        var eventData = before.IsHandled ? before.Value : Deserialize(message);
        return await OnAfterDeserialize(message, eventData);
    }

    protected virtual async Task<HookResult<TEvent>> OnBeforeDeserializeAsync(EventHubMessage message)
    {
        await Task.Yield();
        return HookResult<TEvent>.ContinueWith(null);
    }

    protected virtual TEvent? Deserialize(EventHubMessage message)
    {
        LogMethodStart();
        var msg = message.Message?.ToString();
        if (msg != null)
        {
            var eventData = JsonSerializer.Deserialize<TEvent>(msg, Utilities.Common.GetJsonOptions());
            return eventData;
        }
        return default;
    }

    protected virtual async Task<TEvent?> OnAfterDeserialize(EventHubMessage message, TEvent? eventData)
    {
        await Task.Yield();
        return eventData;
    }

    private async Task<TModel?> GetModelDataFromManageApiAsync(TEvent eventData)
    {
        var before = await OnBeforeGetModelDataFromManageApiAsync(eventData);
        var modelData = before.IsHandled ? before.Value : await GetFromApiAsync(eventData.Id);
        return await OnAfterGetModelDataFromManageApiAsync(eventData, modelData);
    }

    protected virtual async Task<HookResult<TModel>> OnBeforeGetModelDataFromManageApiAsync(TEvent? eventData)
    {
        await Task.Yield();
        return HookResult<TModel>.ContinueWith(null);
    }

    protected virtual async Task<TModel?> GetFromApiAsync(Guid id)
    {
        await Task.Yield();
        return default;
    }
    protected virtual async Task<TModel?> OnAfterGetModelDataFromManageApiAsync(TEvent? eventData, TModel? modelData)
    {
        await Task.Yield();
        return modelData;
    }

    private async Task<TModel?> GetModelDataFromRepositoryAsync(TModel modelData)
    {
        var before = await OnBeforeGetModelDataFromRepositoryAsync(modelData);
        var existingModelData = before.IsHandled ? before.Value : await GetFromRepositoryAsync(modelData);
        return await OnAfterGetModelDataFromRepositoryAsync(existingModelData, modelData);
    }

    protected virtual async Task<HookResult<TModel>> OnBeforeGetModelDataFromRepositoryAsync(TModel? modelData)
    {
        await Task.Yield();
        return HookResult<TModel>.ContinueWith(null);
    }

    protected virtual async Task<TModel?> GetFromRepositoryAsync(TModel modelData)
    {
        await Task.Yield();
        return default;
    }
    protected virtual async Task<TModel?> OnAfterGetModelDataFromRepositoryAsync(TModel? existingModelData, TModel? newModelData)
    {
        await Task.Yield();
        return newModelData;
    }

    private async Task<TModel?> ValidateModelDataExternalReferencesAsync(TModel modelData)
    {
        var before = await OnBeforeValidateModelDataExternalReferencesAsync(modelData);
        var existingModelData = before.IsHandled ? before.Value : await ValidateExternalReferencesAsync(modelData);
        return await OnAfterValidateModelDataExternalReferencesAsync(existingModelData, modelData);
    }

    protected virtual async Task<HookResult<TModel>> OnBeforeValidateModelDataExternalReferencesAsync(TModel? modelData)
    {
        await Task.Yield();
        return HookResult<TModel>.ContinueWith(null);
    }

    protected virtual async Task<TModel?> ValidateExternalReferencesAsync(TModel modelData)
    {
        await Task.Yield();
        return default;
    }
    protected virtual async Task<TModel?> OnAfterValidateModelDataExternalReferencesAsync(TModel? existingModelData, TModel? newModelData)
    {
        await Task.Yield();
        return newModelData;
    }
}
