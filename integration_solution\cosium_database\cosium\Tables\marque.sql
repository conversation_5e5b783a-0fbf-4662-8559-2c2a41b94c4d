﻿CREATE TABLE [cosium].[marque] (
    [id]                NVARCHAR (50)  NOT NULL,
    [nommarque]         NVARCHAR (50)  NULL,
    [codemarque]        NVARCHAR (50)  NULL,
    [specialite]        NVARCHAR (50)  NULL,
    [adresse]           NVARCHAR (50)  NULL,
    [adresse2]          NVARCHAR (50)  NULL,
    [codepostal]        NVARCHAR (50)  NULL,
    [ville]             NVARCHAR (50)  NULL,
    [pays]              NVARCHAR (50)  NULL,
    [tel]               NVARCHAR (50)  NULL,
    [telp]              NVARCHAR (50)  NULL,
    [fax]               NVARCHAR (50)  NULL,
    [cache]             NVARCHAR (50)  NULL,
    [commentairemarque] NVARCHAR (MAX) NULL,
    [secteurlibre]      NVARCHAR (50)  NULL,
    [libelleinterne]    NVARCHAR (50)  NULL,
    [modificationdate]  NVARCHAR (50)  NULL,
    [CreatedOn]         DATETIME2 CONSTRAINT [DF_marque_CreatedOn] DEFAULT (sysutcdatetime()) NOT NULL,
    [ModifiedOn]        DATETIME2 CONSTRAINT [DF_marque_ModifiedOn] DEFAULT (sysutcdatetime()) NOT NULL,
    CONSTRAINT [PK_marque] PRIMARY KEY CLUSTERED ([id] ASC)
);
GO

CREATE INDEX IX_marque_codemarque
ON [cosium].[marque] (codemarque)
GO

CREATE INDEX IX_marque_ModifiedOn
ON [cosium].[marque] ([ModifiedOn])
GO

CREATE TRIGGER [cosium].marque_UpdateModified
ON [cosium].[marque]
AFTER UPDATE 
AS
   UPDATE [cosium].[marque]
   SET [ModifiedOn] = sysutcdatetime()
   FROM Inserted AS i
   WHERE [cosium].[marque].[id] = i.[id]
GO