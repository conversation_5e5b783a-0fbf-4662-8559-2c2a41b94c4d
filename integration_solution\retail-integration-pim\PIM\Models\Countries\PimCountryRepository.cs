﻿using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using WSA.Retail.Integration.Configuration;
using WSA.Retail.Integration.Logging;
using WSA.Retail.Integration.PIM.Data;

namespace WSA.Retail.Integration.PIM.Models.Countries;

public class PimCountryRepository(
    IOptions<AppSettings> options,
    ILogger<PimCountryRepository> logger,
    IDbContextFactory<PimDbContext> dbContextFactory)
    : IPimCountryRepository
{
    private readonly AppSettings _appSettings = options.Value;
    private readonly ILogger<PimCountryRepository> _logger = logger;
    private readonly IDbContextFactory<PimDbContext> _dbContextFactory = dbContextFactory;

    public async Task<List<PimCountryEntity>> GetListAsync()
    {
        _logger.LogMethodStart();
        using var context = _dbContextFactory.CreateDbContext();
        var list = await context.PimCountries
            .Include(x => x.Brands)
            .ToListAsync();
        return list ?? [];
    }





}