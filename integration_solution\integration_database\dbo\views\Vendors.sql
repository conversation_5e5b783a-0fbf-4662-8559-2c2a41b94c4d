﻿CREATE VIEW [dbo].[Vendors]
AS 

SELECT Source.Id,
       Source.Code,
       c1.ExternalRecordId AS ExternalCode,
       Source.[Name],
       ExternalSystem.Id AS ExternalSystemId,
       ExternalSystem.Code AS ExternalSystemCode,
       Source.[ModifiedOn],
       t1.JSON

  FROM dbo.Vendor AS Source

 OUTER APPLY dbo.ExternalSystem

 OUTER APPLY (SELECT ExternalRecordId FROM dbo.Coupling WHERE RecordId = Source.Id AND ExternalSystemId = ExternalSystem.Id) AS c1

 OUTER APPLY (SELECT(
              SELECT Vendor.Id AS 'id',
                     ExternalSystem.Code AS 'externalSystemCode',
                     UPPER(Vendor.Code) AS 'code',
                     c1.ExternalRecordId AS 'externalCode',
                     Vendor.[Name] AS 'name',
                     Vendor.[Address] AS 'address',
                     Vendor.Address2 AS 'address2',
                     Vendor.City AS 'city',
                     Vendor.Region AS 'region',
                     Vendor.Country AS 'country',
                     Vendor.PostalCode AS 'postalCode',
                     Vendor.Phone AS 'phone',
                     Vendor.Email AS 'email',
                     Vendor.AccountNo AS 'accountNo',
                     Vendor.IntegrateWithPOS AS 'integrateWithPos'

                FROM dbo.Vendor

                WHERE Vendor.Id = Source.Id

                  FOR JSON PATH, WITHOUT_ARRAY_WRAPPER
                      ) AS JSON
                      ) AS t1