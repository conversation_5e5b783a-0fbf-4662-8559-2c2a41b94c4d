﻿CREATE TABLE [dbo].[Vendor]
(
    [Id]                UNIQUEIDENTIFIER CONSTRAINT [DF_Vendor_Id] DEFAULT NEWID() NOT NULL,
    [Code]              NVARCHAR(20) NOT NULL,
    [Name]              NVARCHAR(100) NULL,
    [AlternateCode]     NVARCHAR(50) NULL,
    [Address]           NVARCHAR(100) NULL,
    [Address2]          NVARCHAR(50) NULL,
    [City]              NVARCHAR(30) NULL,
    [Region]            NVARCHAR(30) NULL,
    [Country]           NVARCHAR(10) NULL,
    [PostalCode]        NVARCHAR(20) NULL,
    [Phone]             NVARCHAR(30) NULL,
    [Email]             NVARCHAR(50) NULL,
    [AccountNo]         NVARCHAR(20) NULL,
    [IntegrateWithPOS]  BIT CONSTRAINT [DF_Vendor_IsPosVendor] DEFAULT ((0)) NULL,
    [CreatedOn]         DATETIME2 CONSTRAINT [DF_Vendor_CreatedOn] DEFAULT (sysutcdatetime()) NOT NULL,
    [ModifiedOn]        DATETIME2 CONSTRAINT [DF_Vendor_ModifiedOn] DEFAULT (sysutcdatetime()) NOT NULL,
    CONSTRAINT          [PK_Vendor_Id] PRIMARY KEY CLUSTERED ([Id] ASC)
)
GO

CREATE UNIQUE INDEX IX_Vendor_Code
ON [dbo].[Vendor] ([Code])
GO

CREATE INDEX IX_Vendor_Name
ON [dbo].[Vendor] ([Name])
GO