namespace WSA.Integration;

page 50193 "Retail Integration Setup"
{
    Caption = 'Retail Integration Setup';
    PageType = Card;
    SourceTable = "Retail Integration Setup";
    DeleteAllowed = false;
    InsertAllowed = false;
    UsageCategory = Administration;
    ApplicationArea = All;

    layout
    {
        area(content)
        {
            group(General)
            {
                Caption = 'General';

                field(Enabled; Rec.Enabled)
                {
                    ToolTip = 'Enable or disable the integration with the API';
                }

                group("API Settings")
                {
                    Caption = 'API Settings';

                    field("API Base URL"; Rec."API Base URL")
                    {
                        ToolTip = 'The base URL of the API';
                        ExtendedDatatype = URL;
                    }

                    field("API Key"; Rec."API Key")
                    {
                        ToolTip = 'The API key to authenticate with the API';
                    }

                    field("External System Code"; Rec."External System Code")
                    {
                        ToolTip = 'The code of the external system';
                    }

                    group("Event Grid Settings")
                    {
                        Caption = 'Event Grid Settings';

                        field("Event Grid Topic Endpoint"; Rec."Event Grid Topic Endpoint")
                        {
                            ToolTip = 'The endpoint of the Event Grid topic';
                            ExtendedDatatype = URL;
                        }

                        field("Event Grid Access Key"; Rec."Event Grid Access Key")
                        {
                            ToolTip = 'The key of the Event Grid topic';
                        }

                        field("Handle Events Asyncronously"; Rec."Handle Events Asyncronously")
                        {
                            ToolTip = 'Handle events asyncronously';
                        }
                    }
                }
                group("Journals")
                {
                    Caption = 'Journals';

                    group("Payment Journal")
                    {
                        Caption = 'Payment Journal';

                        field(PaymentTemplate; Rec."Payment Journal Template")
                        {
                            ToolTip = 'The journal template to use for payment journals';
                        }

                        field(PaymentBatch; Rec."Payment Journal Batch")
                        {
                            ToolTip = 'The journal batch to use for payment journals';
                        }
                    }

                    group("Claim Journal")
                    {
                        Caption = 'Claim Journal';

                        field(ClaimTemplate; Rec."Claim Journal Template")
                        {
                            ToolTip = 'The journal template to use for claim journals';
                        }

                        field(ClaimBatch; Rec."Claim Journal Batch")
                        {
                            ToolTip = 'The journal batch to use for claim journals';
                        }
                    }

                    group("Item Adjustment Journal")
                    {
                        Caption = 'Item Adjustment Journal';

                        field(AdjustmentTemplate; Rec."Item Adjustment Template")
                        {
                            ToolTip = 'The journal template to use for item adjustment journals';
                        }

                        field(AdjustmentBatch; Rec."Item Adjustment Batch")
                        {
                            ToolTip = 'The journal batch to use for item adjustment journals';
                        }
                    }
                }
            }
        }
    }

    trigger OnOpenPage()
    begin
        Rec.Reset();
        if not Rec.Get() then begin
            Rec.Init();
            Rec.Insert();
        end;
    end;
}
