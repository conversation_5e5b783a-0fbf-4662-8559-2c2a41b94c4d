using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using WSA.Retail.Integration.DbModel;
using WSA.Retail.Integration.Model;

namespace WSA.Retail.Integration.Cosium
{
    public class TimerTrigger(ILoggerFactory loggerFactory, IntegrationContext integrationContext, CosiumContext cosiumContext)
    {
        private readonly ILogger _logger = loggerFactory.CreateLogger<TimerTrigger>();
        private readonly IntegrationContext _integrationContext = integrationContext;
        private readonly CosiumContext _cosiumContext = cosiumContext;

        [Function("TimerTrigger")]
        public async Task Run([TimerTrigger("%Schedule%", RunOnStartup = true)] TimerInfo myTimer)
        {
            var scope = InitScope(nameof(Run));
            _logger.BeginScope(scope);
            _logger.LogInformation("[{className}].[{procedureName}] started", ClassName(), nameof(Run));

            Model.Common.SetLogger(_logger);
            Model.Common.SetContext(_integrationContext);

            Common.SetLogger(_logger);
            Common.SetContext(_cosiumContext);
            

            if (myTimer.ScheduleStatus is not null)
            {
                await MasterDataPost();
                await TransactionPost();

                _logger.BeginScope(scope);
                _logger.LogInformation("Next scheduled execution at: {DateTime}", myTimer.ScheduleStatus.Next);
            }
        }

        public async Task MasterDataPost()
        {
            _logger.LogInformation("[{className}].[{procedureName}] started", ClassName(), nameof(MasterDataPost));

            //await Clinic.ProcessNewRecordsAsync(_logger);
            await Patient.ProcessNewRecordsAsync();
            //await Vendor.ProcessNewRecordsAsync(_logger);

            // Await all simple entity tasks
            //await Task.WhenAll(clinicTask, patientTask, vendorTask);
            await Product.ProcessNewRecordsAsync(_logger);
        }

        public async Task TransactionPost()
        {
            var scope = InitScope(nameof(TransactionPost));
            _logger.BeginScope(scope);
            _logger.LogInformation("[{className}].[{procedureName}] started", ClassName(), nameof(TransactionPost));

            //await PurchaseOrder.ProcessNewRecordsAsync(_logger);
            //await PurchaseReceipt.ProcessNewRecordsAsync(_logger);
            //await SalesInvoice.ProcessNewRecordsAsync(_logger);
            //await SalesCredit.ProcessNewRecordsAsync(_logger);
            await Payment.ProcessNewRecordsAsync(_logger);
        }

        private static string ClassName()
        {
            return nameof(Clinic);
        }

        private static Dictionary<string, object> InitScope(string procedureName)
        {
            return new Dictionary<string, object>() {
                { "appName", "Cosium" },
                { "className", nameof(TimerTrigger) },
                { "procedureName", procedureName }
            };
        }
    }
}
