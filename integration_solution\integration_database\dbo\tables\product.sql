﻿CREATE TABLE [dbo].[Product]
(
    [Id]                    UNIQUEIDENTIFIER CONSTRAINT [DF_Product_Id] DEFAULT NEWID() NOT NULL,
    [Code]                  NVARCHAR(20) NOT NULL,
    [Name]                  NVARCHAR(100) NULL,
    [AlternateCode]         NVARCHAR(50) NULL,
    [PimProductId]          NVARCHAR(20) NULL,
    [ProductCategoryId]     UNIQUEIDENTIFIER NULL,
    [VendorId]              UNIQUEIDENTIFIER NULL,
    [ManufacturerId]        UNIQUEIDENTIFIER NULL,
    [ColorId]               UNIQUEIDENTIFIER NULL,
    [TaxGroupId]            UNIQUEIDENTIFIER NULL,
    [VendorItemNo]          NVARCHAR(20) NULL,
    [GTIN]                  NVARCHAR(20) NULL,
    [ImageUrl]              NVARCHAR(255) NULL,
    [IsSerialized]          BIT NULL,
    [IsInventory]           BIT NULL,
    [IsTaxable]             BIT NULL,
    [UnitCost]              DECIMAL(18,4) NULL,
    [ProductModelId]        UNIQUEIDENTIFIER NULL,
    [BatteryId]             UNIQUEIDENTIFIER NULL,
    [CreatedOn]             DATETIME2 CONSTRAINT [DF_Product_CreatedOn] DEFAULT (sysutcdatetime()) NOT NULL,
    [ModifiedOn]            DATETIME2 CONSTRAINT [DF_Product_ModifiedOn] DEFAULT (sysutcdatetime()) NOT NULL,
    CONSTRAINT              [PK_Product_Id] PRIMARY KEY CLUSTERED ([Id] ASC),
    CONSTRAINT              [FK_Product_ProductCategory] FOREIGN KEY (ProductCategoryId) REFERENCES [dbo].[ProductCategory](Id),
    CONSTRAINT              [FK_Product_Vendor] FOREIGN KEY (VendorId) REFERENCES [dbo].[Vendor](Id),
    CONSTRAINT              [FK_Product_Manufacturer] FOREIGN KEY (ManufacturerId) REFERENCES [dbo].[Manufacturer](Id),
    CONSTRAINT              [FK_Product_Color] FOREIGN KEY (ColorId) REFERENCES [dbo].[Color](Id),
    CONSTRAINT              [FK_Product_TaxGroup] FOREIGN KEY (TaxGroupId) REFERENCES [dbo].[TaxGroup](Id),
    CONSTRAINT              [FK_Product_ProductModel] FOREIGN KEY (ProductModelId) REFERENCES [dbo].[ProductModel](Id),
    CONSTRAINT              [FK_Product_Battery] FOREIGN KEY (BatteryId) REFERENCES [dbo].[Battery](Id)
)
GO

CREATE UNIQUE INDEX IX_Product_Code
ON [dbo].[Product] ([Code])
GO

CREATE INDEX IX_Product_Name
ON [dbo].[Product] ([Name])
GO