﻿using Microsoft.Extensions.Logging;

namespace WSA.Retail.Integration.Cosium
{
    internal class EntitySubscriber : Model.EntitySubscriber
    {
        internal EntitySubscriber() { }

        internal EntitySubscriber(string requestbody): base(requestbody) { }

        internal static async Task<EntitySubscriber?> GetFromBaseAsync(ILogger log, string entityCode)
        {
            var scope = InitScope(nameof(GetFromBaseAsync));
            log.BeginScope(scope);
            log.LogInformation("{className}.{procedureName} started", ClassName(), nameof(GetFromBaseAsync));

            EntitySubscriber? record = new();
            if (await record.GetAsync(scope, log, Common.SqlConnectionString(), Common.ExternalSystemCode(), entityCode))
            {
                if (record.Id != null)
                {
                    return record;
                }
            }
            return null;
        }

        private static string ClassName()
        {
            return nameof(Clinic);
        }

        private static Dictionary<string, object> InitScope(string procedureName)
        {
            return new Dictionary<string, object>() {
                { "appName", "Cosium" },
                { "className", nameof(EntitySubscriber) },
                { "procedureName", procedureName }
            };
        }

    }
}
