﻿CREATE PROCEDURE [cosium].[UpsertStagedFacture]
AS

        SET XACT_ABORT ON
        BEGIN TRANSACTION
     
       UPDATE cosium.facture
          SET [refclient] = Source.[refclient],
              [numfacture] = Source.[numfacture],
              [datefacture] = Source.[datefacture],
              [numavoir] = Source.[numavoir],
              [totalht] = Source.[totalht],
              [totaltva] = Source.[totaltva],
              [totalnet] = Source.[totalnet],
              [restedu] = Source.[restedu],
              [totalttc] = Source.[totalttc],
              [centre] = Source.[centre],
              [typefacture] = Source.[typefacture],
              [facturefinale] = Source.[facturefinale],
              [adresseclient1] = Source.[adresseclient1],
              [adresseclient2] = Source.[adresseclient2],
              [adresseclient3] = Source.[adresseclient3],
              [adresseclient4] = Source.[adresseclient4],
              [adresseclient5] = Source.[adresseclient5],
              [numsecuclient] = Source.[numsecuclient],
              [mutuelleclient] = Source.[mutuelleclient],
              [numadherentclient] = Source.[numadherentclient],
              [nomclient] = Source.[nomclient],
              [nomassure] = Source.[nomassure],
              [commentaire] = Source.[commentaire],
              [datecreation] = Source.[datecreation],
              [alerte] = Source.[alerte],
              [partro] = Source.[partro],
              [partrc] = Source.[partrc],
              [numadeliaudioclient] = Source.[numadeliaudioclient],
              [refvendeur] = Source.[refvendeur],
              [htorttc] = Source.[htorttc],
              [caissesecuclient] = Source.[caissesecuclient],
              [tauxrbstss] = Source.[tauxrbstss],
              [prescripteur] = Source.[prescripteur],
              [dateordonnance] = Source.[dateordonnance],
              [refprescripteur] = Source.[refprescripteur],
              [datenaissance] = Source.[datenaissance],
              [article115] = Source.[article115],
              [telclient] = Source.[telclient],
              [dateentreefacture] = Source.[dateentreefacture],
              [regul] = Source.[regul],
              [datenaisslunairefact] = Source.[datenaisslunairefact],
              [assurenomfact] = Source.[assurenomfact],
              [assureprenomfact] = Source.[assureprenomfact],
              [assuretitrefact] = Source.[assuretitrefact],
              [dateexpcomptafact] = Source.[dateexpcomptafact],
              [datearchivage] = Source.[datearchivage],
              [creepar] = Source.[creepar],
              [datemodif] = Source.[datemodif],
              [datevalidationdevis] = Source.[datevalidationdevis],
              [reflienmedecinadresseprescripteurfacture] = Source.[reflienmedecinadresseprescripteurfacture],
              [downpayment] = Source.[downpayment]

         FROM cosium.facture

              INNER JOIN cosium.staging_facture AS Source
                      ON facture.id = Source.id

        WHERE facture.datemodif <> Source.datemodif;

       INSERT INTO cosium.facture (
              [id],
              [refclient],
              [numfacture],
              [datefacture],
              [numavoir],
              [totalht],
              [totaltva],
              [totalnet],
              [restedu],
              [totalttc],
              [centre],
              [typefacture],
              [facturefinale],
              [adresseclient1],
              [adresseclient2],
              [adresseclient3],
              [adresseclient4],
              [adresseclient5],
              [numsecuclient],
              [mutuelleclient],
              [numadherentclient],
              [nomclient],
              [nomassure],
              [commentaire],
              [datecreation],
              [alerte],
              [partro],
              [partrc],
              [numadeliaudioclient],
              [refvendeur],
              [htorttc],
              [caissesecuclient],
              [tauxrbstss],
              [prescripteur],
              [dateordonnance],
              [refprescripteur],
              [datenaissance],
              [article115],
              [telclient],
              [dateentreefacture],
              [regul],
              [datenaisslunairefact],
              [assurenomfact],
              [assureprenomfact],
              [assuretitrefact],
              [dateexpcomptafact],
              [datearchivage],
              [creepar],
              [datemodif],
              [datevalidationdevis],
              [reflienmedecinadresseprescripteurfacture],
              [downpayment]
              )

       SELECT Source.[id],
              Source.[refclient],
              Source.[numfacture],
              Source.[datefacture],
              Source.[numavoir],
              Source.[totalht],
              Source.[totaltva],
              Source.[totalnet],
              Source.[restedu],
              Source.[totalttc],
              Source.[centre],
              Source.[typefacture],
              Source.[facturefinale],
              Source.[adresseclient1],
              Source.[adresseclient2],
              Source.[adresseclient3],
              Source.[adresseclient4],
              Source.[adresseclient5],
              Source.[numsecuclient],
              Source.[mutuelleclient],
              Source.[numadherentclient],
              Source.[nomclient],
              Source.[nomassure],
              Source.[commentaire],
              Source.[datecreation],
              Source.[alerte],
              Source.[partro],
              Source.[partrc],
              Source.[numadeliaudioclient],
              Source.[refvendeur],
              Source.[htorttc],
              Source.[caissesecuclient],
              Source.[tauxrbstss],
              Source.[prescripteur],
              Source.[dateordonnance],
              Source.[refprescripteur],
              Source.[datenaissance],
              Source.[article115],
              Source.[telclient],
              Source.[dateentreefacture],
              Source.[regul],
              Source.[datenaisslunairefact],
              Source.[assurenomfact],
              Source.[assureprenomfact],
              Source.[assuretitrefact],
              Source.[dateexpcomptafact],
              Source.[datearchivage],
              Source.[creepar],
              Source.[datemodif],
              Source.[datevalidationdevis],
              Source.[reflienmedecinadresseprescripteurfacture],
              Source.[downpayment]

         FROM cosium.staging_facture AS Source

        WHERE NOT EXISTS (SELECT * FROM cosium.facture WHERE id = Source.id)

     TRUNCATE TABLE cosium.staging_facture
       
       COMMIT TRANSACTION;