﻿namespace WSA.Retail.Integration.PIM.Models.Attributes
{
    public class PimAttributeEntity : IPimAttributeEntity, WSA.Retail.Integration.Core.IIdentifiable
    {
        public Guid Id { get; set; } = Guid.Empty;
        public required string Code { get; set; }
        public string? Name { get; set; }
        public string? DataType { get; set; }
        public DateTime SystemCreatedOn { get; set; }
        public DateTime SystemModifiedOn { get; set; }
    }
}
