namespace WSA.Integration;

using Microsoft.Purchases.Vendor;

codeunit 50103 "Vendor Events"
{
    [EventSubscriber(ObjectType::Table, Database::Vendor, OnAfterInsertEvent, '', true, true)]
    local procedure SendEventOnAfterInsertEvent(
        var Rec: Record Vendor;
        RunTrigger: Boolean)

    begin
        if Rec.IsTemporary then
            exit;

        if RunTrigger then
            SendVendorToIntegrationAPI(Rec);
    end;


    [EventSubscriber(ObjectType::Table, Database::Vendor, OnAfterModifyEvent, '', true, true)]
    local procedure SendEventOnAfterModifyEvent(
        var Rec: Record Vendor;
        var xRec: Record Vendor;
        RunTrigger: Boolean)

    begin
        if Rec.IsTemporary then
            exit;

        if RunTrigger then
            if HasChanged(Rec, xRec) then
                SendVendorToIntegrationAPI(Rec);
    end;


    local procedure SendVendorToIntegrationAPI(Vendor: Record Vendor)
    var
        RetailIntegrationSetup: Record "Retail Integration Setup";
        IntegrationManagement: Codeunit "Integration Management";
        JObject: JsonObject;

    begin
        RetailIntegrationSetup.SafeGet();
        if not RetailIntegrationSetup.Enabled then
            exit;

        if Vendor."No." = '' then
            exit;

        if Vendor.Name = '' then
            exit;

        if IsNullGuid(Vendor.SystemId) then
            exit;

        JObject.Add('code', Vendor."No.");
        JObject.Add('externalCode', Format(Vendor.SystemId).TrimStart('{').TrimEnd('}'));
        JObject.Add('name', Vendor.Name);
        JObject.Add('address', Vendor.Address);
        JObject.Add('address2', Vendor."Address 2");
        JObject.Add('city', Vendor.City);
        JObject.Add('region', Vendor.County);
        JObject.Add('country', Vendor."Country/Region Code");
        JObject.Add('postalCode', Vendor."Post Code");
        JObject.Add('phone', Vendor."Phone No.");
        JObject.Add('email', Vendor."E-Mail");
        JObject.Add('accountNo', Vendor."Our Account No.");
        JObject.Add('integrateWithPOS', Vendor."Integrate with POS");

        if not IntegrationManagement.TrySendToApi(JObject, 'vendors') then
            exit;
    end;


    local procedure HasChanged(
         Rec: Record "Vendor";
         xRec: Record "Vendor"): Boolean

    begin
        if xRec."No." = '' then
            exit(false);

        if Rec.Name <> xRec.Name then
            exit(true);

        if Rec.Address <> xRec.Address then
            exit(true);

        if Rec."Address 2" <> xRec."Address 2" then
            exit(true);

        if Rec."Country/Region Code" <> xRec."Country/Region Code" then
            exit(true);

        if Rec.County <> xRec.County then
            exit(true);

        if Rec."Post Code" <> xRec."Post Code" then
            exit(true);

        if Rec."Phone No." <> xRec."Phone No." then
            exit(true);

        if Rec."E-Mail" <> xRec."E-Mail" then
            exit(true);

        if Rec."Our Account No." <> xRec."Our Account No." then
            exit(true);
    end;
}
