﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Internal;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Text.Json;
using WSA.Retail.Integration.Events;
using WSA.Retail.Integration.Logging;
using WSA.Retail.Integration.Manage.API;
using WSA.Retail.Integration.Manage.Configuration;
using WSA.Retail.Integration.Manage.Data;
using WSA.Retail.Integration.Manage.Models.PurchaseOrders;
using WSA.Retail.Integration.Models.Clinics;
using WSA.Retail.Integration.Models.ProductModels;
using WSA.Retail.Integration.Models.Products;
using WSA.Retail.Integration.Models.PurchaseOrders;
using WSA.Retail.Integration.Models.Vendors;
using WSA.Retail.Integration.Utilities;

namespace WSA.Retail.Integration.Manage.EventProcessing;

public class PurchaseOrderEventHandler(
    IOptions<AppSettings> appSettings,
    ILogger<PurchaseOrderEventHandler> logger,
    ManageAPI manageApi,
    IDbContextFactory<ManageDbContext> contextFactory,
    IPurchaseOrderService purchaseOrderService,
    IClinicService clinicService,
    IVendorService vendorService,
    IProductService productService,
    IProductModelService productModelService) 
    : LoggingBase<PurchaseOrderEventHandler>(logger), IEventHandler
{
    private readonly AppSettings _appSettings = appSettings.Value;
    private readonly ILogger<PurchaseOrderEventHandler> _logger = logger;
    private readonly ManageAPI _manageApi = manageApi;
    private readonly IDbContextFactory<ManageDbContext> _contextFactory = contextFactory;
    private readonly IPurchaseOrderService _purchaseOrderService = purchaseOrderService;
    private readonly IClinicService _clinicService = clinicService;
    private readonly IVendorService _vendorService = vendorService;
    private readonly IProductService _productService = productService;
    private readonly IProductModelService _productModelService = productModelService;


    public async Task<bool> HandleFromQueueAsync(EventHubMessage message)
    {
        LogMethodStart();

        var msg = message.Message?.ToString();
        if (msg != null)
        {
            var purchaseOrderEvent = JsonSerializer.Deserialize<PurchaseOrderEventHubEvent>(msg, Common.GetJsonOptions());
            if (purchaseOrderEvent == null) return false;
            if (purchaseOrderEvent.Id == Guid.Empty) return false;

            // Get the purchase order from the api
            PurchaseOrder? purchaseOrder = null;
            OrderResponse? response = null;
            try
            {
                response = await _manageApi.OrdersGET2Async(purchaseOrderEvent.Id);
                purchaseOrder = response.ToPurchaseOrder(_appSettings.ExternalSystemCode);
            }
            catch (Exception ex)
            {
                _logger.LogCustomError(ex, _appSettings.AppName, nameof(HandleFromQueueAsync));
                return false;
            }

            // Get an existing order to prefill some of the data points.  Shouldn't exist, but just in case.
            var existingPurchaseOrder = await _purchaseOrderService.GetAsync(
                externalSystemCode: _appSettings.ExternalSystemCode,
                externalReference: purchaseOrder.ExternalReference);

            if (existingPurchaseOrder != null)
            {
                purchaseOrder.Clinic = existingPurchaseOrder.Clinic;
                purchaseOrder.Vendor = existingPurchaseOrder.Vendor;
                purchaseOrder.DocumentNumber = existingPurchaseOrder.DocumentNumber;
                purchaseOrder.DocumentDate = existingPurchaseOrder.DocumentDate;
            }

            // Lookup clinic
            if (purchaseOrder.Clinic?.Code == null && purchaseOrder.Clinic?.ExternalCode != null)
            {
                var clinic = await _clinicService.GetExternalReferenceAsync(
                    externalSystemCode: _appSettings.ExternalSystemCode,
                    externalCode: purchaseOrder.Clinic.ExternalCode);
                if (clinic == null)
                {
                    _logger.LogCustomWarning(_appSettings.AppName,
                        $"Clinic {purchaseOrder.Vendor?.ExternalCode} for PurchaseOrder {purchaseOrder.DocumentNumber} "
                        + "was not found in the base data store.  Verify Clinic.");
                    return false;
                }
                purchaseOrder.Clinic = clinic;
            }

            // Lookup vendor
            if (purchaseOrder.Vendor?.Code == null && purchaseOrder.Vendor?.ExternalCode != null)
            {
                var vendor = await _vendorService.GetExternalReferenceAsync(
                    externalSystemCode: _appSettings.ExternalSystemCode,
                    externalCode: purchaseOrder.Vendor.ExternalCode);
                if (vendor == null)
                {
                    _logger.LogCustomWarning(_appSettings.AppName,
                        $"Vendor {purchaseOrder.Vendor?.ExternalCode} for PurchaseOrder {purchaseOrder.DocumentNumber} "
                        + "was not found in the base data store.  Verify Vendor.");
                    return false;
                }
                purchaseOrder.Vendor = vendor;
            }

            purchaseOrder.DocumentDate ??= DateTime.Now;

            int lastSequence = existingPurchaseOrder?.Lines?.Count ?? 0;

            // Handle lines
            foreach (var purchaseOrderLine in purchaseOrder.Lines)
            {
                // Prefill data if it exists from an existing order.  Again, shouldn't exist, but just in case.
                var existingPurchaseOrderLine = existingPurchaseOrder?.Lines
                    .Where(x => x.ExternalReference == purchaseOrderLine.ExternalReference)
                    .FirstOrDefault();

                if (existingPurchaseOrderLine != null)
                {
                    purchaseOrderLine.Sequence = existingPurchaseOrderLine.Sequence;
                    purchaseOrderLine.Product = existingPurchaseOrderLine.Product;
                }

                purchaseOrderLine.Sequence ??= ++lastSequence;

                // Lookup product
                if (purchaseOrderLine.Product?.Code == null && purchaseOrderLine.Product?.ExternalCode != null)
                {
                    // Try the product first.
                    var existingProduct = await _productService.GetExternalReferenceAsync(
                        externalSystemCode: _appSettings.ExternalSystemCode,
                        externalCode: purchaseOrderLine.Product.ExternalCode);

                    if (existingProduct != null)
                    {
                        purchaseOrderLine.Product = existingProduct;
                    }
                    else
                    {
                        // Fall back to the product model.
                        var existingProductModel = await _productModelService.GetExternalReferenceAsync(
                            externalSystemCode: _appSettings.ExternalSystemCode,
                            externalCode: purchaseOrderLine.Product.ExternalCode);

                        if (existingProductModel != null)
                        {
                            // Need the response line from the API for color.
                            var responseLine = response.LineItems
                                .Where(x => x.Id == Guid.Parse(purchaseOrderLine.ExternalReference!))
                                .FirstOrDefault();

                            if (responseLine != null) // This should never be null, since purchaseOrderLine was derived from responseLine
                            {
                                if (responseLine.Color == null) // If this is null, there's a master data mismatch.
                                {
                                    _logger.LogCustomWarning(_appSettings.AppName,
                                        $"PurchaseOrderLine {responseLine.Id} for PurchaseOrder {purchaseOrder.DocumentNumber} "
                                        + "did not have a related base Product, and does not include a color.  Verify product.");
                                }
                                else
                                {
                                    /*
                                    var childProducts = await _productModelService.GetChildProductsAsync(
                                        externalSystemCode: _appSettings.ExternalSystemCode,
                                        productModelId: (Guid)existingProductModel.Id!);

                                    if (childProducts == null)
                                    {
                                        _logger.LogCustomInformation(_appSettings.AppName, nameof(HandleFromQueueAsync),
                                            $"No child products found for ProductModel {existingProductModel.Code}");
                                    }
                                    else
                                    {
                                        var selectedChildProduct = childProducts
                                            .Where(x => x.Color?.ExternalCode == responseLine.Color.Id.ToString())
                                            .FirstOrDefault();

                                        if (selectedChildProduct == null) // If this is null, there's a master data mismatch.
                                        {
                                            _logger.LogCustomInformation(_appSettings.AppName, nameof(HandleFromQueueAsync),
                                                $"Color {responseLine.Color.Id} was not found as a child product for "
                                                + $"ProductModel {existingProductModel.Code}");
                                        }
                                        else
                                        {
                                            purchaseOrderLine.Product = new() // No reason to go back to the db here.
                                            {
                                                Id = selectedChildProduct.Id,
                                                Code = selectedChildProduct.Code,
                                                ExternalCode = selectedChildProduct.ExternalCode,
                                                Name = selectedChildProduct.Name
                                            };
                                        }
                                    }
                                    */
                                }
                            }
                        }
                    }
                }
            }

            // Verify required fields
            ArgumentException.ThrowIfNullOrWhiteSpace(purchaseOrder.DocumentNumber, nameof(purchaseOrder.DocumentNumber));
            ArgumentNullException.ThrowIfNull(purchaseOrder.DocumentDate, nameof(purchaseOrder.DocumentDate));
            ArgumentException.ThrowIfNullOrWhiteSpace(purchaseOrder.Clinic?.Code, nameof(purchaseOrder.Clinic.Code));
            ArgumentException.ThrowIfNullOrWhiteSpace(purchaseOrder.Vendor?.Code, nameof(purchaseOrder.Vendor.Code));
            foreach (var purchaseOrderLine in purchaseOrder.Lines)
            {
                ArgumentException.ThrowIfNullOrWhiteSpace(purchaseOrderLine.Product?.Code, nameof(purchaseOrderLine.Product.Code));
                ArgumentNullException.ThrowIfNull(purchaseOrderLine.Quantity, nameof(purchaseOrderLine.Quantity));
            }

            try
            {
                purchaseOrder = await _purchaseOrderService.UpsertAsync(purchaseOrder);
                if (purchaseOrder == null)
                {
                    _logger.LogCustomError(_appSettings.AppName, nameof(HandleFromQueueAsync),
                        $"Failed to upsert PurchaseOrder {purchaseOrderEvent.Id}");
                    return false;
                }
                else
                {
                    return true;
                }
            }
            catch (Exception ex)
            {
                _logger.LogCustomError(ex, _appSettings.AppName, nameof(HandleFromQueueAsync));
                return false;
            }
        }
        return false;
    }

    public async Task<bool> HandleToQueueAsync(Event ev)
    {
        return await Task.FromResult(true);
    }

    public async Task LogMessage(EventHubMessage message)
    {
        LogMethodStart();

        var msg = JsonSerializer.Serialize(message.Message, Common.GetJsonOptions());
        var purchaseOrderEvent = JsonSerializer.Deserialize<PurchaseOrderEventHubEvent>(msg!, Common.GetJsonOptions());
        if (purchaseOrderEvent == null)
        {
            LogCustomWarning("PurchaseOrderEventHubEvent deserialization failed.");
            return;
        }

        var dbContext = await _contextFactory.CreateDbContextAsync();
        var orderEventEntity = await dbContext.OrderEventEntity.FirstOrDefaultAsync(x => x.MessageId == message.MessageId);
        if (orderEventEntity != null)
        {
            bool isDirty = false;

            if (orderEventEntity.OrderId != purchaseOrderEvent.Id)
            {
                orderEventEntity.OrderId = purchaseOrderEvent.Id;
                isDirty = true;
            }

            if (orderEventEntity.ExternalNumber != purchaseOrderEvent.Number)
            {
                orderEventEntity.ExternalNumber = purchaseOrderEvent.Number;
                isDirty = true;
            }

            if (orderEventEntity.Timestamp != message.Timestamp)
            {
                orderEventEntity.Timestamp = message.Timestamp;
                isDirty = true;
            }

            var timeZoneId = _appSettings.LocalTimeZone;
            var localTimestamp = TimeZoneHelper.ConvertToTimeZone(message.Timestamp, timeZoneId);
            if (orderEventEntity.LocalTimestamp != localTimestamp)
            {
                orderEventEntity.LocalTimestamp = localTimestamp;
                isDirty = true;
            }

            if (!string.IsNullOrWhiteSpace(message.BlobPath) && orderEventEntity.BlobPath != message.BlobPath)
            {
                orderEventEntity.BlobPath = message.BlobPath;
                isDirty = true;
            }

            if (isDirty)
            {
                orderEventEntity.ModifiedOn = DateTime.UtcNow;
                await dbContext.SaveChangesAsync();
            }
        }
        else
        {
            orderEventEntity = new OrderEventEntity
            {
                Id = Guid.NewGuid(),
                EventType = message.Event,
                MessageId = message.MessageId,
                OrderId = purchaseOrderEvent.Id,
                ExternalNumber = purchaseOrderEvent.Number,
                Timestamp = message.Timestamp,
                LocalTimestamp = message.Timestamp,
                BlobPath = message.BlobPath ?? string.Empty,
                Status = 0
            };
            dbContext.OrderEventEntity.Add(orderEventEntity);
            await dbContext.SaveChangesAsync();
        }
    }
}