﻿using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Text.Json;
using WSA.Retail.Integration.Events;
using WSA.Retail.Integration.Logging;
using WSA.Retail.Integration.Manage.API;
using WSA.Retail.Integration.Manage.Configuration;
using WSA.Retail.Integration.Manage.Models.PurchaseReceipts;
using WSA.Retail.Integration.Models.Configuration;
using WSA.Retail.Integration.Models.PurchaseOrders;
using WSA.Retail.Integration.Models.PurchaseReceipts;
using WSA.Retail.Integration.Utilities;

namespace WSA.Retail.Integration.Manage.EventProcessing;

public class PurchaseReceiptEventHandler(
    IOptions<AppSettings> appSettings,
    ILogger<PurchaseReceiptEventHandler> logger,
    ManageAPI manageApi,
    IPurchaseReceiptService purchaseReceiptService,
    IPurchaseOrderService purchaseOrderService,
    IEntityService entityService) 
    : IEventHandler
{
    private readonly AppSettings _appSettings = appSettings.Value;
    private readonly ILogger<PurchaseReceiptEventHandler> _logger = logger;
    private readonly ManageAPI _manageApi = manageApi;
    private readonly IPurchaseReceiptService _purchaseReceiptService = purchaseReceiptService;
    private readonly IPurchaseOrderService _purchaseOrderService = purchaseOrderService;
    private readonly IEntityService _entityService = entityService;


    public async Task<bool> HandleFromQueueAsync(EventHubMessage message)
    {
        _logger.LogMethodStart(_appSettings.AppName);

        var msg = message.Message?.ToString();
        if (msg != null)
        {
            var purchaseReceiptEvent = JsonSerializer.Deserialize<OrderLineItemAcceptedEventHubEvent>(msg, Common.GetJsonOptions());
            if (purchaseReceiptEvent == null) return false;
            if (purchaseReceiptEvent.Id == Guid.Empty) return false;
            if (purchaseReceiptEvent.Order.Id == Guid.Empty) return false;

            var manageOrder = await GetPurchaseOrderFromApiAsync(purchaseReceiptEvent.Order.Id);
            if (manageOrder == null) return false;

            var manageOrderLine = manageOrder.LineItems.Where(x => x.Id == purchaseReceiptEvent.Id).FirstOrDefault();
            if (manageOrderLine == null) return false;

            var existingPurchaseOrder = await GetExistingPurchaseOrderFromDataStoreAsync(manageOrder.Id);
            if (existingPurchaseOrder == null) return false;

            var existingPurchaseLine = GetExistingPurchaseOrderLine(manageOrderLine.Id, existingPurchaseOrder);
            if (existingPurchaseLine == null) return false;

            ArgumentNullException.ThrowIfNull(existingPurchaseLine.Sequence);
            var existingReceiptLines = await GetReceiptLinesAsync(existingPurchaseOrder, (int)existingPurchaseLine.Sequence);
            if (existingReceiptLines?.Count != 0)
            {
                _logger.LogCustomError($"Base PurchaseOrder {existingPurchaseOrder.DocumentNumber} Already has receipts against it for line {existingPurchaseLine.Sequence}.");
                return false;
            }

            // Create receipt
            var purchaseReceipt = await CreateReceiptHeaderAsync(existingPurchaseOrder);
            purchaseReceipt.PurchaseReceiptLines = CreatePurchaseReceiptLines(
                existingPurchaseLine, 
                purchaseReceiptEvent.Quantity ?? 0, 
                manageOrderLine.SerialNumbers);

            // Verify required fields
            ArgumentException.ThrowIfNullOrWhiteSpace(purchaseReceipt.DocumentNumber, nameof(purchaseReceipt.DocumentNumber));
            ArgumentNullException.ThrowIfNull(purchaseReceipt.DocumentDate, nameof(purchaseReceipt.DocumentDate));
            ArgumentException.ThrowIfNullOrWhiteSpace(purchaseReceipt.Clinic?.Code, nameof(purchaseReceipt.Clinic.Code));
            ArgumentException.ThrowIfNullOrWhiteSpace(purchaseReceipt.Vendor?.Code, nameof(purchaseReceipt.Vendor.Code));
            foreach (var purchaseReceiptLine in purchaseReceipt.PurchaseReceiptLines)
            {
                ArgumentException.ThrowIfNullOrWhiteSpace(purchaseReceiptLine.Product?.Code, nameof(purchaseReceiptLine.Product.Code));
                ArgumentNullException.ThrowIfNull(purchaseReceiptLine.Quantity, nameof(purchaseReceiptLine.Quantity));
            }

            try
            {
                var upsertedPurchaseReceipt = await _purchaseReceiptService.UpsertAsync(purchaseReceipt);
                if (upsertedPurchaseReceipt == null)
                {
                    _logger.LogCustomError($"Failed to upsert PurchaseReceipt {purchaseReceiptEvent.Id}");
                    return false;
                }
                else
                {
                    return true;
                }
            }
            catch (Exception ex)
            {
                _logger.LogCustomError(ex);
                return false;
            }
        }
        return false;
    }

    public async Task<bool> HandleToQueueAsync(Event ev)
    {
        return await Task.FromResult(true);
    }

    private async Task<OrderResponse?> GetPurchaseOrderFromApiAsync(Guid orderId)
    {
        _logger.LogMethodStart();
        try
        {
            var response = await _manageApi.OrdersGET2Async(orderId);
            if (response == null)
            {
                _logger.LogWarning("No response received from API for order ID {OrderId}", orderId);
                return null;
            }
            return response;
        }
        catch (Exception ex)
        {
            _logger.LogCustomError(ex);
            return null;
        }
    }

    private async Task<PurchaseOrder?> GetExistingPurchaseOrderFromDataStoreAsync(Guid orderId)
    {
        _logger.LogMethodStart();
        var existingPurchaseOrder = await _purchaseOrderService.GetAsync(
        externalSystemCode: _appSettings.ExternalSystemCode,
        externalReference: orderId.ToString());

        if (existingPurchaseOrder == null)
        {
            _logger.LogCustomError(
                $"Base data store does not include PurchaseOrder with ExternalReference {orderId}.");
            return null;
        }
        return existingPurchaseOrder;
    }

    private PurchaseOrderLine? GetExistingPurchaseOrderLine(Guid receiptId, PurchaseOrder purchaseOrder)
    {
        _logger.LogMethodStart();
        var existingPurchaseLine = purchaseOrder.Lines
            .Where(x => x.ExternalReference == receiptId.ToString())
            .FirstOrDefault();

        if (existingPurchaseLine == null)
        {
            _logger.LogCustomError(
                $"Base PurchaseOrder does not include a line with ExternalReference {receiptId}.");
            return null;
        }

        return existingPurchaseLine;
    }

    private async Task<List<PurchaseReceiptLine>> GetReceiptLinesAsync(PurchaseOrder purchaseOrder, int sequence)
    {
        _logger.LogMethodStart(_appSettings.AppName);
        var existingReceiptLines = await _purchaseOrderService.GetReceiptLinesAsync(purchaseOrder);
        var filteredReceiptLines = existingReceiptLines
            .Where(x => x.PurchaseOrderLine != null)
            .Where(x => x.PurchaseOrderLine!.Sequence == sequence)
            .ToList();
        return filteredReceiptLines ?? [];
    }

    private async Task<PurchaseReceipt> CreateReceiptHeaderAsync(PurchaseOrder purchaseOrder)
    {
        _logger.LogMethodStart();
        var documentNumber = await _entityService.GetNextNumberAsync(EntityType.PurchaseReceipt.GetEntityCode());
        PurchaseReceipt purchaseReceipt = new()
        {
            Id = Guid.Empty,
            ExternalSystemCode = _appSettings.ExternalSystemCode,
            DocumentNumber = documentNumber,
            ExternalReference = "N/A",
            PurchaseOrder = new()
            {
                ExternalReference = purchaseOrder.ExternalReference
            },
            Vendor = purchaseOrder.Vendor,
            Clinic = purchaseOrder.Clinic,
            DocumentDate = DateTime.UtcNow,
            PurchaseReceiptLines = []
        };
        return purchaseReceipt;
    }

    private List<PurchaseReceiptLine> CreatePurchaseReceiptLines(
        PurchaseOrderLine purchaseOrderLine,
        int quantity,
        ICollection<string>? serialNumbers)
    {
        _logger.LogMethodStart();
        int lastSequence = 0;
        PurchaseReceiptLine CreateBaseLine(int sequence, decimal quantity, string? serialNumber = null) => new()
        {
            Id = Guid.Empty,
            Sequence = sequence,
            PurchaseOrderLine = new()
            {
                Id = purchaseOrderLine.Id,
                Sequence = purchaseOrderLine.Sequence
            },
            Product = purchaseOrderLine.Product,
            Quantity = quantity,
            SerialNumber = serialNumber
        };

        List<PurchaseReceiptLine> purchaseReceiptLines = [];
        if (serialNumbers?.Count > 0)
        {
            foreach (var serialNumber in serialNumbers)
            {
                purchaseReceiptLines.Add(CreateBaseLine(++lastSequence, 1, serialNumber));
            }
        }
        else
        {
            purchaseReceiptLines.Add(CreateBaseLine(++lastSequence, quantity));
        }
        return purchaseReceiptLines;
    }
}