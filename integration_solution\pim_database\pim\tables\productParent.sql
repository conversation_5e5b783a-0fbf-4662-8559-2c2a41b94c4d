﻿CREATE TABLE [pim].[ProductParent]
(
    [Id]                    UNIQUEIDENTIFIER CONSTRAINT [DF_ProductParent_Id] DEFAULT NEWID() NOT NULL,
    [ProductId]             UNIQUEIDENTIFIER NOT NULL,
    [ParentId]              UNIQUEIDENTIFIER NOT NULL,
    [SystemCreatedOn]       DATETIME2 CONSTRAINT [DF_ProductParent_SystemCreatedOn] DEFAULT (sysutcdatetime()) NOT NULL,
    [SystemModifiedOn]      DATETIME2 CONSTRAINT [DF_ProductParent_SystemModifiedOn] DEFAULT (sysutcdatetime()) NOT NULL,
    CONSTRAINT              [PK_ProductParent_Id] PRIMARY KEY CLUSTERED ([Id] ASC)
)
GO

CREATE UNIQUE INDEX IX_ProductParent_Id
ON [pim].[ProductParent] ([ProductId], [ParentId])
GO