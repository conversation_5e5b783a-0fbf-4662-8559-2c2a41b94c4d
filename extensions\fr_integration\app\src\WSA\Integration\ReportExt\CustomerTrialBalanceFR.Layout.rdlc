﻿<?xml version="1.0" encoding="utf-8"?>
<Report xmlns="http://schemas.microsoft.com/sqlserver/reporting/2016/01/reportdefinition" xmlns:rd="http://schemas.microsoft.com/SQLServer/reporting/reportdesigner">
  <AutoRefresh>0</AutoRefresh>
  <DataSources>
    <DataSource Name="DataSource">
      <ConnectionProperties>
        <DataProvider>SQL</DataProvider>
        <ConnectString />
      </ConnectionProperties>
      <rd:SecurityType>None</rd:SecurityType>
      <rd:DataSourceID>4f5ebdf5-1952-4364-8b31-485860e22184</rd:DataSourceID>
    </DataSource>
  </DataSources>
  <ReportSections>
    <ReportSection>
      <Body>
        <ReportItems>
          <Tablix Name="table2">
            <TablixBody>
              <TablixColumns>
                <TablixColumn>
                  <Width>1.5873cm</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>6.34921cm</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>2.53968cm</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>2.53968cm</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>2.53968cm</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>2.53968cm</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>2.53968cm</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>2.53968cm</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>0.2cm</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>0.2cm</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>0.2cm</Width>
                </TablixColumn>
              </TablixColumns>
              <TablixRows>
                <TablixRow>
                  <Height>0.3cm</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Customer_TABLECAPTION__________Filter">
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=First(Fields!Customer_TABLECAPTION__________Filter.Value)</Value>
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                            </Paragraph>
                          </Paragraphs>
                          <ZIndex>39</ZIndex>
                          <Style>
                            <Border>
                              <Width>0.25pt</Width>
                            </Border>
                            <VerticalAlign>Middle</VerticalAlign>
                          </Style>
                        </Textbox>
                        <ColSpan>6</ColSpan>
                      </CellContents>
                    </TablixCell>
                    <TablixCell />
                    <TablixCell />
                    <TablixCell />
                    <TablixCell />
                    <TablixCell />
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="textbox49">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style />
                                </TextRun>
                              </TextRuns>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>textbox49</rd:DefaultName>
                          <ZIndex>38</ZIndex>
                          <Style>
                            <Border>
                              <Width>0.25pt</Width>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                          </Style>
                        </Textbox>
                        <ColSpan>5</ColSpan>
                      </CellContents>
                    </TablixCell>
                    <TablixCell />
                    <TablixCell />
                    <TablixCell />
                    <TablixCell />
                  </TablixCells>
                </TablixRow>
                <TablixRow>
                  <Height>0.3cm</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="textbox15">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style />
                                </TextRun>
                              </TextRuns>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>textbox15</rd:DefaultName>
                          <ZIndex>37</ZIndex>
                          <Style>
                            <Border>
                              <Width>0.25pt</Width>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                          </Style>
                        </Textbox>
                        <ColSpan>11</ColSpan>
                      </CellContents>
                    </TablixCell>
                    <TablixCell />
                    <TablixCell />
                    <TablixCell />
                    <TablixCell />
                    <TablixCell />
                    <TablixCell />
                    <TablixCell />
                    <TablixCell />
                    <TablixCell />
                    <TablixCell />
                  </TablixCells>
                </TablixRow>
                <TablixRow>
                  <Height>0.846cm</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="textbox12">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style />
                                </TextRun>
                              </TextRuns>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>textbox12</rd:DefaultName>
                          <ZIndex>36</ZIndex>
                          <Style>
                            <Border>
                              <Width>0.25pt</Width>
                            </Border>
                            <VerticalAlign>Bottom</VerticalAlign>
                            <PaddingLeft>4pt</PaddingLeft>
                            <PaddingRight>4pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                        <ColSpan>2</ColSpan>
                      </CellContents>
                    </TablixCell>
                    <TablixCell />
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Balance_at_Starting_DateCaption">
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=First(Fields!Balance_at_Starting_DateCaption.Value)</Value>
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                            </Paragraph>
                          </Paragraphs>
                          <ZIndex>35</ZIndex>
                          <Style>
                            <Border>
                              <Style>Solid</Style>
                              <Width>0.25pt</Width>
                            </Border>
                            <VerticalAlign>Bottom</VerticalAlign>
                            <PaddingLeft>4pt</PaddingLeft>
                            <PaddingRight>4pt</PaddingRight>
                            <PaddingTop>1pt</PaddingTop>
                            <PaddingBottom>1pt</PaddingBottom>
                          </Style>
                        </Textbox>
                        <ColSpan>2</ColSpan>
                      </CellContents>
                    </TablixCell>
                    <TablixCell />
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Balance_Date_RangeCaption">
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=First(Fields!Balance_Date_RangeCaption.Value)</Value>
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                            </Paragraph>
                          </Paragraphs>
                          <ZIndex>34</ZIndex>
                          <Style>
                            <Border>
                              <Style>Solid</Style>
                              <Width>0.25pt</Width>
                            </Border>
                            <VerticalAlign>Bottom</VerticalAlign>
                            <PaddingLeft>4pt</PaddingLeft>
                            <PaddingRight>4pt</PaddingRight>
                            <PaddingTop>1pt</PaddingTop>
                            <PaddingBottom>1pt</PaddingBottom>
                          </Style>
                        </Textbox>
                        <ColSpan>2</ColSpan>
                      </CellContents>
                    </TablixCell>
                    <TablixCell />
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Balance_at_Ending_dateCaption">
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=First(Fields!Balance_at_Ending_dateCaption.Value)</Value>
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                            </Paragraph>
                          </Paragraphs>
                          <ZIndex>33</ZIndex>
                          <Style>
                            <Border>
                              <Style>Solid</Style>
                              <Width>0.25pt</Width>
                            </Border>
                            <VerticalAlign>Bottom</VerticalAlign>
                            <PaddingLeft>4pt</PaddingLeft>
                            <PaddingRight>4pt</PaddingRight>
                            <PaddingTop>1pt</PaddingTop>
                            <PaddingBottom>1pt</PaddingBottom>
                          </Style>
                        </Textbox>
                        <ColSpan>2</ColSpan>
                      </CellContents>
                    </TablixCell>
                    <TablixCell />
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="PageCaption">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!PageCaption.Value</Value>
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>Red</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                            </Paragraph>
                          </Paragraphs>
                          <ZIndex>32</ZIndex>
                          <Visibility>
                            <Hidden>true</Hidden>
                          </Visibility>
                          <Style>
                            <Border>
                              <Width>0.25pt</Width>
                            </Border>
                            <VerticalAlign>Bottom</VerticalAlign>
                            <PaddingLeft>4pt</PaddingLeft>
                            <PaddingRight>4pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Customer_Trial_BalanceCaption">
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!Customer_Trial_BalanceCaption.Value</Value>
                                  <Style>
                                    <Color>#ff0000</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                            </Paragraph>
                          </Paragraphs>
                          <ZIndex>31</ZIndex>
                          <Visibility>
                            <Hidden>true</Hidden>
                          </Visibility>
                          <Style>
                            <Border>
                              <Width>0.25pt</Width>
                            </Border>
                            <VerticalAlign>Bottom</VerticalAlign>
                            <PaddingLeft>4pt</PaddingLeft>
                            <PaddingRight>4pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="FORMAT_TODAY_0_4_">
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!FORMAT_TODAY_0_4_.Value</Value>
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>Red</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                            </Paragraph>
                          </Paragraphs>
                          <ZIndex>30</ZIndex>
                          <Visibility>
                            <Hidden>true</Hidden>
                          </Visibility>
                          <Style>
                            <Border>
                              <Width>0.25pt</Width>
                            </Border>
                            <VerticalAlign>Bottom</VerticalAlign>
                            <PaddingLeft>4pt</PaddingLeft>
                            <PaddingRight>4pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
                <TablixRow>
                  <Height>0.846cm</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="No_Caption">
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=First(Fields!No_Caption.Value)</Value>
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                            </Paragraph>
                          </Paragraphs>
                          <ZIndex>29</ZIndex>
                          <Style>
                            <Border>
                              <Style>Solid</Style>
                              <Width>0.25pt</Width>
                            </Border>
                            <VerticalAlign>Bottom</VerticalAlign>
                            <PaddingLeft>4pt</PaddingLeft>
                            <PaddingRight>4pt</PaddingRight>
                            <PaddingTop>1pt</PaddingTop>
                            <PaddingBottom>1pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="NameCaption">
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=First(Fields!NameCaption.Value)</Value>
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                            </Paragraph>
                          </Paragraphs>
                          <ZIndex>28</ZIndex>
                          <Style>
                            <Border>
                              <Style>Solid</Style>
                              <Width>0.25pt</Width>
                            </Border>
                            <VerticalAlign>Bottom</VerticalAlign>
                            <PaddingLeft>4pt</PaddingLeft>
                            <PaddingRight>4pt</PaddingRight>
                            <PaddingTop>1pt</PaddingTop>
                            <PaddingBottom>1pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="DebitCaption">
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=First(Fields!DebitCaption.Value)</Value>
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                            </Paragraph>
                          </Paragraphs>
                          <ZIndex>27</ZIndex>
                          <Style>
                            <Border>
                              <Style>Solid</Style>
                              <Width>0.25pt</Width>
                            </Border>
                            <VerticalAlign>Bottom</VerticalAlign>
                            <PaddingLeft>4pt</PaddingLeft>
                            <PaddingRight>4pt</PaddingRight>
                            <PaddingTop>1pt</PaddingTop>
                            <PaddingBottom>1pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="CreditCaption">
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=First(Fields!CreditCaption.Value)</Value>
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                            </Paragraph>
                          </Paragraphs>
                          <ZIndex>26</ZIndex>
                          <Style>
                            <Border>
                              <Style>Solid</Style>
                              <Width>0.25pt</Width>
                            </Border>
                            <VerticalAlign>Bottom</VerticalAlign>
                            <PaddingLeft>4pt</PaddingLeft>
                            <PaddingRight>4pt</PaddingRight>
                            <PaddingTop>1pt</PaddingTop>
                            <PaddingBottom>1pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="DebitCaption_Control1120030">
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=First(Fields!DebitCaption_Control1120030.Value)</Value>
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                            </Paragraph>
                          </Paragraphs>
                          <ZIndex>25</ZIndex>
                          <Style>
                            <Border>
                              <Style>Solid</Style>
                              <Width>0.25pt</Width>
                            </Border>
                            <VerticalAlign>Bottom</VerticalAlign>
                            <PaddingLeft>4pt</PaddingLeft>
                            <PaddingRight>4pt</PaddingRight>
                            <PaddingTop>1pt</PaddingTop>
                            <PaddingBottom>1pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="CreditCaption_Control1120032">
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=First(Fields!CreditCaption_Control1120032.Value)</Value>
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                            </Paragraph>
                          </Paragraphs>
                          <ZIndex>24</ZIndex>
                          <Style>
                            <Border>
                              <Style>Solid</Style>
                              <Width>0.25pt</Width>
                            </Border>
                            <VerticalAlign>Bottom</VerticalAlign>
                            <PaddingLeft>4pt</PaddingLeft>
                            <PaddingRight>4pt</PaddingRight>
                            <PaddingTop>1pt</PaddingTop>
                            <PaddingBottom>1pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="DebitCaption_Control1120034">
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=First(Fields!DebitCaption_Control1120034.Value)</Value>
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                            </Paragraph>
                          </Paragraphs>
                          <ZIndex>23</ZIndex>
                          <Style>
                            <Border>
                              <Style>Solid</Style>
                              <Width>0.25pt</Width>
                            </Border>
                            <VerticalAlign>Bottom</VerticalAlign>
                            <PaddingLeft>4pt</PaddingLeft>
                            <PaddingRight>4pt</PaddingRight>
                            <PaddingTop>1pt</PaddingTop>
                            <PaddingBottom>1pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="CreditCaption_Control1120036">
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=First(Fields!CreditCaption_Control1120036.Value)</Value>
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                            </Paragraph>
                          </Paragraphs>
                          <ZIndex>22</ZIndex>
                          <Style>
                            <Border>
                              <Style>Solid</Style>
                              <Width>0.25pt</Width>
                            </Border>
                            <VerticalAlign>Bottom</VerticalAlign>
                            <PaddingLeft>4pt</PaddingLeft>
                            <PaddingRight>4pt</PaddingRight>
                            <PaddingTop>1pt</PaddingTop>
                            <PaddingBottom>1pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="COMPANYNAME">
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!COMPANYNAME.Value</Value>
                                  <Style>
                                    <Color>#ff0000</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                            </Paragraph>
                          </Paragraphs>
                          <ZIndex>21</ZIndex>
                          <Visibility>
                            <Hidden>true</Hidden>
                          </Visibility>
                          <Style>
                            <Border>
                              <Width>0.25pt</Width>
                            </Border>
                            <VerticalAlign>Bottom</VerticalAlign>
                            <PaddingLeft>4pt</PaddingLeft>
                            <PaddingRight>4pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="STRSUBSTNO_Text003_USERID_">
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!UserCaption.Value</Value>
                                  <Style>
                                    <Color>#ff0000</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                            </Paragraph>
                          </Paragraphs>
                          <ZIndex>20</ZIndex>
                          <Visibility>
                            <Hidden>true</Hidden>
                          </Visibility>
                          <Style>
                            <Border>
                              <Width>0.25pt</Width>
                            </Border>
                            <VerticalAlign>Bottom</VerticalAlign>
                            <PaddingLeft>4pt</PaddingLeft>
                            <PaddingRight>4pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="STRSUBSTNO_Text004_PreviousStartDate_">
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!STRSUBSTNO_Text004_PreviousStartDate_.Value</Value>
                                  <Style>
                                    <Color>#ff0000</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                            </Paragraph>
                          </Paragraphs>
                          <ZIndex>19</ZIndex>
                          <Visibility>
                            <Hidden>true</Hidden>
                          </Visibility>
                          <Style>
                            <Border>
                              <Width>0.25pt</Width>
                            </Border>
                            <VerticalAlign>Bottom</VerticalAlign>
                            <PaddingLeft>4pt</PaddingLeft>
                            <PaddingRight>4pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
                <TablixRow>
                  <Height>0.3cm</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Customer__No__">
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!Customer__No__.Value</Value>
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>=IIF(IsNumeric(Fields!Customer__No__.Value),"Right","Left")</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <ZIndex>8</ZIndex>
                          <Style>
                            <Border>
                              <Width>0.25pt</Width>
                            </Border>
                            <LeftBorder>
                              <Style>Solid</Style>
                            </LeftBorder>
                            <RightBorder>
                              <Style>Solid</Style>
                            </RightBorder>
                            <PaddingLeft>4pt</PaddingLeft>
                            <PaddingRight>4pt</PaddingRight>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Customer_Name">
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!Customer_Name.Value</Value>
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                            </Paragraph>
                          </Paragraphs>
                          <ZIndex>7</ZIndex>
                          <Style>
                            <Border>
                              <Width>0.25pt</Width>
                            </Border>
                            <LeftBorder>
                              <Style>Solid</Style>
                            </LeftBorder>
                            <RightBorder>
                              <Style>Solid</Style>
                            </RightBorder>
                            <PaddingLeft>4pt</PaddingLeft>
                            <PaddingRight>4pt</PaddingRight>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="PreviousDebitAmountLCY_PreviousCreditAmountLCY">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Code.BlankNegAndZero(Fields!PreviousDebitAmountLCY_PreviousCreditAmountLCY.Value)</Value>
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                    <Format>=Fields!PreviousDebitAmountLCY_PreviousCreditAmountLCYFormat.Value</Format>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                            </Paragraph>
                          </Paragraphs>
                          <ZIndex>6</ZIndex>
                          <Style>
                            <Border>
                              <Width>0.25pt</Width>
                            </Border>
                            <LeftBorder>
                              <Style>Solid</Style>
                            </LeftBorder>
                            <RightBorder>
                              <Style>Solid</Style>
                            </RightBorder>
                            <PaddingLeft>4pt</PaddingLeft>
                            <PaddingRight>4pt</PaddingRight>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="PreviousCreditAmountLCY_PreviousDebitAmountLCY">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Code.BlankNegAndZero(Fields!PreviousCreditAmountLCY_PreviousDebitAmountLCY.Value)</Value>
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                    <Format>=Fields!PreviousCreditAmountLCY_PreviousDebitAmountLCYFormat.Value</Format>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                            </Paragraph>
                          </Paragraphs>
                          <ZIndex>5</ZIndex>
                          <Style>
                            <Border>
                              <Width>0.25pt</Width>
                            </Border>
                            <LeftBorder>
                              <Style>Solid</Style>
                            </LeftBorder>
                            <RightBorder>
                              <Style>Solid</Style>
                            </RightBorder>
                            <PaddingLeft>4pt</PaddingLeft>
                            <PaddingRight>4pt</PaddingRight>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="PeriodDebitAmountLCY">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Code.BlankZero(Fields!PeriodDebitAmountLCY.Value)</Value>
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                    <Format>=Fields!PeriodDebitAmountLCYFormat.Value</Format>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                            </Paragraph>
                          </Paragraphs>
                          <ZIndex>4</ZIndex>
                          <Style>
                            <Border>
                              <Width>0.25pt</Width>
                            </Border>
                            <LeftBorder>
                              <Style>Solid</Style>
                            </LeftBorder>
                            <RightBorder>
                              <Style>Solid</Style>
                            </RightBorder>
                            <PaddingLeft>4pt</PaddingLeft>
                            <PaddingRight>4pt</PaddingRight>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="PeriodCreditAmountLCY">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Code.BlankZero(Fields!PeriodCreditAmountLCY.Value)</Value>
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                    <Format>=Fields!PeriodCreditAmountLCYFormat.Value</Format>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                            </Paragraph>
                          </Paragraphs>
                          <ZIndex>3</ZIndex>
                          <Style>
                            <Border>
                              <Width>0.25pt</Width>
                            </Border>
                            <LeftBorder>
                              <Style>Solid</Style>
                            </LeftBorder>
                            <RightBorder>
                              <Style>Solid</Style>
                            </RightBorder>
                            <PaddingLeft>4pt</PaddingLeft>
                            <PaddingRight>4pt</PaddingRight>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="PreviousDebitAmountLCY_PeriodDebitAmountLCY___PreviousCreditAmountLCY_PeriodCreditAmountLCY_">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Code.BlankNegAndZero(Fields!PreviousDebitAmountLCY_PeriodDebitAmountLCY___PreviousCreditAmountLCY_PeriodCreditAmountLCY_.Value)</Value>
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                    <Format>=Fields!PreviousDebitAmountLCY_PeriodDebitAmountLCY___PreviousCreditAmountLCY_PeriodCreditAmountLCY_Format.Value</Format>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                            </Paragraph>
                          </Paragraphs>
                          <ZIndex>2</ZIndex>
                          <Style>
                            <Border>
                              <Width>0.25pt</Width>
                            </Border>
                            <LeftBorder>
                              <Style>Solid</Style>
                            </LeftBorder>
                            <RightBorder>
                              <Style>Solid</Style>
                            </RightBorder>
                            <PaddingLeft>4pt</PaddingLeft>
                            <PaddingRight>4pt</PaddingRight>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="PreviousCreditAmountLCY_PeriodCreditAmountLCY___PreviousDebitAmountLCY_PeriodDebitAmountLCY_">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Code.BlankNegAndZero(Fields!PreviousCreditAmountLCY_PeriodCreditAmountLCY___PreviousDebitAmountLCY_PeriodDebitAmountLCY_.Value)</Value>
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                    <Format>=Fields!PreviousCreditAmountLCY_PeriodCreditAmountLCY___PreviousDebitAmountLCY_PeriodDebitAmountLCY_Format.Value</Format>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                            </Paragraph>
                          </Paragraphs>
                          <ZIndex>1</ZIndex>
                          <Style>
                            <Border>
                              <Width>0.25pt</Width>
                            </Border>
                            <LeftBorder>
                              <Style>Solid</Style>
                            </LeftBorder>
                            <RightBorder>
                              <Style>Solid</Style>
                            </RightBorder>
                            <PaddingLeft>4pt</PaddingLeft>
                            <PaddingRight>4pt</PaddingRight>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="textbox45">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>textbox45</rd:DefaultName>
                          <Style>
                            <Border>
                              <Width>0.25pt</Width>
                            </Border>
                            <PaddingLeft>4pt</PaddingLeft>
                            <PaddingRight>4pt</PaddingRight>
                          </Style>
                        </Textbox>
                        <ColSpan>3</ColSpan>
                      </CellContents>
                    </TablixCell>
                    <TablixCell />
                    <TablixCell />
                  </TablixCells>
                </TablixRow>
                <TablixRow>
                  <Height>0.3cm</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Grand_totalCaption">
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=First(Fields!Grand_totalCaption.Value)</Value>
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Right</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <ZIndex>16</ZIndex>
                          <Style>
                            <Border>
                              <Style>Solid</Style>
                              <Width>0.25pt</Width>
                            </Border>
                            <PaddingLeft>4pt</PaddingLeft>
                            <PaddingRight>4pt</PaddingRight>
                          </Style>
                        </Textbox>
                        <ColSpan>2</ColSpan>
                      </CellContents>
                    </TablixCell>
                    <TablixCell />
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="PreviousDebitAmountLCY_PreviousCreditAmountLCY_Control1120069">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Code.BlankNegAndZero(Sum(Fields!PreviousDebitAmountLCY_PreviousCreditAmountLCY.Value))</Value>
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                    <Format>=First(Fields!PreviousDebitAmountLCY_PreviousCreditAmountLCY_Control1120069Format.Value)</Format>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                            </Paragraph>
                          </Paragraphs>
                          <ZIndex>15</ZIndex>
                          <Style>
                            <Border>
                              <Style>Solid</Style>
                              <Width>0.25pt</Width>
                            </Border>
                            <PaddingLeft>4pt</PaddingLeft>
                            <PaddingRight>4pt</PaddingRight>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="PreviousCreditAmountLCY_PreviousDebitAmountLCY_Control1120072">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Code.BlankNegAndZero(Sum(Fields!PreviousCreditAmountLCY_PreviousDebitAmountLCY.Value))</Value>
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                    <Format>=First(Fields!PreviousCreditAmountLCY_PreviousDebitAmountLCY_Control1120072Format.Value)</Format>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                            </Paragraph>
                          </Paragraphs>
                          <ZIndex>14</ZIndex>
                          <Style>
                            <Border>
                              <Style>Solid</Style>
                              <Width>0.25pt</Width>
                            </Border>
                            <PaddingLeft>4pt</PaddingLeft>
                            <PaddingRight>4pt</PaddingRight>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="PeriodDebitAmountLCY_Control1120075">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Sum(Fields!PeriodDebitAmountLCY.Value)</Value>
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                    <Format>=First(Fields!PeriodDebitAmountLCY_Control1120075Format.Value)</Format>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                            </Paragraph>
                          </Paragraphs>
                          <ZIndex>13</ZIndex>
                          <Style>
                            <Border>
                              <Style>Solid</Style>
                              <Width>0.25pt</Width>
                            </Border>
                            <PaddingLeft>4pt</PaddingLeft>
                            <PaddingRight>4pt</PaddingRight>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="PeriodCreditAmountLCY_Control1120078">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Sum(Fields!PeriodCreditAmountLCY.Value)</Value>
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                    <Format>=First(Fields!PeriodCreditAmountLCY_Control1120078Format.Value)</Format>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                            </Paragraph>
                          </Paragraphs>
                          <ZIndex>12</ZIndex>
                          <Style>
                            <Border>
                              <Style>Solid</Style>
                              <Width>0.25pt</Width>
                            </Border>
                            <PaddingLeft>4pt</PaddingLeft>
                            <PaddingRight>4pt</PaddingRight>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="PreviousDebitAmountLCY_PeriodDebitAmountLCY___PreviousCreditAmountLCY_PeriodCreditAmountLCY__Control1120081">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Code.BlankNegAndZero(Sum(Fields!PreviousDebitAmountLCY_PeriodDebitAmountLCY___PreviousCreditAmountLCY_PeriodCreditAmountLCY_.Value))</Value>
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                    <Format>=First(Fields!PreviousDebitAmountLCY_PeriodDebitAmountLCY___PreviousCreditAmountLCY_PeriodCreditAmountLCY__Control1120081Format.Value)</Format>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                            </Paragraph>
                          </Paragraphs>
                          <ZIndex>11</ZIndex>
                          <Style>
                            <Border>
                              <Style>Solid</Style>
                              <Width>0.25pt</Width>
                            </Border>
                            <PaddingLeft>4pt</PaddingLeft>
                            <PaddingRight>4pt</PaddingRight>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="PreviousCreditAmountLCY_PeriodCreditAmountLCY___PreviousDebitAmountLCY_PeriodDebitAmountLCY__Control1120084">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Code.BlankNegAndZero(Sum(Fields!PreviousCreditAmountLCY_PeriodCreditAmountLCY___PreviousDebitAmountLCY_PeriodDebitAmountLCY_.Value))</Value>
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                    <Format>=First(Fields!PreviousCreditAmountLCY_PeriodCreditAmountLCY___PreviousDebitAmountLCY_PeriodDebitAmountLCY__Control1120084Format.Value)</Format>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                            </Paragraph>
                          </Paragraphs>
                          <ZIndex>10</ZIndex>
                          <Style>
                            <Border>
                              <Style>Solid</Style>
                              <Width>0.25pt</Width>
                            </Border>
                            <PaddingLeft>4pt</PaddingLeft>
                            <PaddingRight>4pt</PaddingRight>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="textbox21">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style />
                                </TextRun>
                              </TextRuns>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>textbox21</rd:DefaultName>
                          <ZIndex>9</ZIndex>
                          <Style>
                            <Border>
                              <Width>0.25pt</Width>
                            </Border>
                            <PaddingLeft>4pt</PaddingLeft>
                            <PaddingRight>4pt</PaddingRight>
                          </Style>
                        </Textbox>
                        <ColSpan>3</ColSpan>
                      </CellContents>
                    </TablixCell>
                    <TablixCell />
                    <TablixCell />
                  </TablixCells>
                </TablixRow>
                <TablixRow>
                  <Height>0.07937cm</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="textbox1">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style />
                                </TextRun>
                              </TextRuns>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>textbox1</rd:DefaultName>
                          <ZIndex>18</ZIndex>
                          <Style>
                            <TopBorder>
                              <Style>Solid</Style>
                            </TopBorder>
                          </Style>
                        </Textbox>
                        <ColSpan>8</ColSpan>
                      </CellContents>
                    </TablixCell>
                    <TablixCell />
                    <TablixCell />
                    <TablixCell />
                    <TablixCell />
                    <TablixCell />
                    <TablixCell />
                    <TablixCell />
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="textbox9">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                </TextRun>
                              </TextRuns>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>textbox9</rd:DefaultName>
                          <ZIndex>17</ZIndex>
                        </Textbox>
                        <ColSpan>3</ColSpan>
                      </CellContents>
                    </TablixCell>
                    <TablixCell />
                    <TablixCell />
                  </TablixCells>
                </TablixRow>
              </TablixRows>
            </TablixBody>
            <TablixColumnHierarchy>
              <TablixMembers>
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
              </TablixMembers>
            </TablixColumnHierarchy>
            <TablixRowHierarchy>
              <TablixMembers>
                <TablixMember>
                  <KeepWithGroup>After</KeepWithGroup>
                  <RepeatOnNewPage>true</RepeatOnNewPage>
                  <KeepTogether>true</KeepTogether>
                </TablixMember>
                <TablixMember>
                  <KeepWithGroup>After</KeepWithGroup>
                  <RepeatOnNewPage>true</RepeatOnNewPage>
                  <KeepTogether>true</KeepTogether>
                </TablixMember>
                <TablixMember>
                  <KeepWithGroup>After</KeepWithGroup>
                  <RepeatOnNewPage>true</RepeatOnNewPage>
                  <KeepTogether>true</KeepTogether>
                </TablixMember>
                <TablixMember>
                  <KeepWithGroup>After</KeepWithGroup>
                  <RepeatOnNewPage>true</RepeatOnNewPage>
                  <KeepTogether>true</KeepTogether>
                </TablixMember>
                <TablixMember>
                  <Group Name="table2_Group1">
                    <GroupExpressions>
                      <GroupExpression>=1</GroupExpression>
                    </GroupExpressions>
                  </Group>
                  <TablixMembers>
                    <TablixMember>
                      <Group Name="table2_Details_Group">
                        <DataElementName>Detail</DataElementName>
                      </Group>
                      <TablixMembers>
                        <TablixMember />
                      </TablixMembers>
                      <DataElementName>Detail_Collection</DataElementName>
                      <DataElementOutput>Output</DataElementOutput>
                      <KeepTogether>true</KeepTogether>
                    </TablixMember>
                    <TablixMember>
                      <KeepWithGroup>Before</KeepWithGroup>
                      <KeepTogether>true</KeepTogether>
                    </TablixMember>
                  </TablixMembers>
                </TablixMember>
                <TablixMember>
                  <KeepWithGroup>Before</KeepWithGroup>
                  <RepeatOnNewPage>true</RepeatOnNewPage>
                  <KeepTogether>true</KeepTogether>
                </TablixMember>
              </TablixMembers>
            </TablixRowHierarchy>
            <Filters>
              <Filter>
                <FilterExpression>Fields!NameCaption.Value</FilterExpression>
                <Operator>GreaterThan</Operator>
                <FilterValues>
                  <FilterValue />
                </FilterValues>
              </Filter>
            </Filters>
            <Width>23.77459cm</Width>
            <Visibility>
              <Hidden>=IIF(Fields!NameCaption.Value = "",True,False)</Hidden>
            </Visibility>
            <DataElementOutput>NoOutput</DataElementOutput>
          </Tablix>
        </ReportItems>
        <Height>2.97137cm</Height>
      </Body>
      <Width>28.7cm</Width>
      <Page>
        <PageHeader>
          <Height>1.692cm</Height>
          <PrintOnFirstPage>true</PrintOnFirstPage>
          <PrintOnLastPage>true</PrintOnLastPage>
          <ReportItems>
            <Textbox Name="PageNumberTextBox">
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=ReportItems!PageCaption.Value &amp; Globals!PageNumber</Value>
                      <Style>
                        <FontSize>8pt</FontSize>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style>
                    <TextAlign>Left</TextAlign>
                  </Style>
                </Paragraph>
              </Paragraphs>
              <Top>0.9cm</Top>
              <Left>24cm</Left>
              <Height>0.3cm</Height>
              <Width>2.85cm</Width>
              <ZIndex>5</ZIndex>
              <Style>
                <VerticalAlign>Middle</VerticalAlign>
              </Style>
            </Textbox>
            <Textbox Name="ExecutionTimeTextBox">
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=Globals!ExecutionTime</Value>
                      <Style>
                        <FontSize>8pt</FontSize>
                        <Format>D</Format>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style>
                    <TextAlign>Left</TextAlign>
                  </Style>
                </Paragraph>
              </Paragraphs>
              <Left>24cm</Left>
              <Height>0.3cm</Height>
              <Width>1.29937in</Width>
              <ZIndex>4</ZIndex>
              <Style>
                <VerticalAlign>Middle</VerticalAlign>
              </Style>
            </Textbox>
            <Textbox Name="COMPANYNAME1">
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=ReportItems!COMPANYNAME.Value</Value>
                      <Style>
                        <FontSize>8pt</FontSize>
                      </Style>
                    </TextRun>
                  </TextRuns>
                </Paragraph>
              </Paragraphs>
              <Top>0.6cm</Top>
              <Left>0.075cm</Left>
              <Height>0.3cm</Height>
              <Width>10.65cm</Width>
              <ZIndex>3</ZIndex>
              <Style>
                <VerticalAlign>Middle</VerticalAlign>
              </Style>
            </Textbox>
            <Textbox Name="STRSUBSTNO_Text003_USERID_1">
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=ReportItems!STRSUBSTNO_Text003_USERID_.Value + User!UserID</Value>
                      <Style>
                        <FontSize>8pt</FontSize>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style>
                    <TextAlign>Left</TextAlign>
                  </Style>
                </Paragraph>
              </Paragraphs>
              <Top>0.3cm</Top>
              <Left>24cm</Left>
              <Height>0.31746cm</Height>
              <Width>4cm</Width>
              <ZIndex>2</ZIndex>
              <Style>
                <VerticalAlign>Middle</VerticalAlign>
              </Style>
            </Textbox>
            <Textbox Name="STRSUBSTNO_Text004_PreviousStartDate_1">
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=ReportItems!STRSUBSTNO_Text004_PreviousStartDate_.Value</Value>
                      <Style>
                        <FontSize>8pt</FontSize>
                      </Style>
                    </TextRun>
                  </TextRuns>
                </Paragraph>
              </Paragraphs>
              <Top>0.9cm</Top>
              <Left>0.075cm</Left>
              <Height>0.3cm</Height>
              <Width>10.65cm</Width>
              <ZIndex>1</ZIndex>
              <Style>
                <VerticalAlign>Middle</VerticalAlign>
              </Style>
            </Textbox>
            <Textbox Name="Customer_Trial_BalanceCaption1">
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=ReportItems!Customer_Trial_BalanceCaption.Value</Value>
                      <Style>
                        <FontSize>12pt</FontSize>
                        <FontWeight>Bold</FontWeight>
                      </Style>
                    </TextRun>
                  </TextRuns>
                </Paragraph>
              </Paragraphs>
              <Left>0.075cm</Left>
              <Height>0.6cm</Height>
              <Width>10.65cm</Width>
              <Style>
                <VerticalAlign>Middle</VerticalAlign>
              </Style>
            </Textbox>
          </ReportItems>
        </PageHeader>
        <PageHeight>21cm</PageHeight>
        <PageWidth>29.7cm</PageWidth>
        <InteractiveHeight>11in</InteractiveHeight>
        <InteractiveWidth>8.5in</InteractiveWidth>
        <LeftMargin>1cm</LeftMargin>
        <TopMargin>1cm</TopMargin>
        <BottomMargin>0.5cm</BottomMargin>
      </Page>
    </ReportSection>
  </ReportSections>
  <Code>Public Function BlankZero(ByVal Value As Decimal)
    if Value = 0 then
        Return ""
    end if
    Return Value
End Function

Public Function BlankPos(ByVal Value As Decimal)
    if Value &gt; 0 then
        Return ""
    end if
    Return Value
End Function

Public Function BlankZeroAndPos(ByVal Value As Decimal)
    if Value &gt;= 0 then
        Return ""
    end if
    Return Value
End Function

Public Function BlankNeg(ByVal Value As Decimal)
    if Value &lt; 0 then
        Return ""
    end if
    Return Value
End Function

Public Function BlankNegAndZero(ByVal Value As Decimal)
    if Value &lt;= 0 then
        Return ""
    end if
    Return Value
End Function
</Code>
  <Language>=User!Language</Language>
  <ConsumeContainerWhitespace>true</ConsumeContainerWhitespace>
  <rd:ReportUnitType>Invalid</rd:ReportUnitType>
  <rd:ReportID>15a02887-bdc5-4080-998a-8016369a1bce</rd:ReportID>
  <DataSets>
    <DataSet Name="DataSet_Result">
      <Fields>
        <Field Name="FORMAT_TODAY_0_4_">
          <DataField>FORMAT_TODAY_0_4_</DataField>
        </Field>
        <Field Name="COMPANYNAME">
          <DataField>COMPANYNAME</DataField>
        </Field>
        <Field Name="STRSUBSTNO_Text003_USERID_">
          <DataField>STRSUBSTNO_Text003_USERID_</DataField>
        </Field>
        <Field Name="STRSUBSTNO_Text004_PreviousStartDate_">
          <DataField>STRSUBSTNO_Text004_PreviousStartDate_</DataField>
        </Field>
        <Field Name="PageCaption">
          <DataField>PageCaption</DataField>
        </Field>
        <Field Name="UserCaption">
          <DataField>UserCaption</DataField>
        </Field>
        <Field Name="Customer_TABLECAPTION__________Filter">
          <DataField>Customer_TABLECAPTION__________Filter</DataField>
        </Field>
        <Field Name="Filter">
          <DataField>Filter</DataField>
        </Field>
        <Field Name="Customer__No__">
          <DataField>Customer__No__</DataField>
        </Field>
        <Field Name="Customer_Name">
          <DataField>Customer_Name</DataField>
        </Field>
        <Field Name="PreviousDebitAmountLCY_PreviousCreditAmountLCY">
          <DataField>PreviousDebitAmountLCY_PreviousCreditAmountLCY</DataField>
        </Field>
        <Field Name="PreviousDebitAmountLCY_PreviousCreditAmountLCYFormat">
          <DataField>PreviousDebitAmountLCY_PreviousCreditAmountLCYFormat</DataField>
        </Field>
        <Field Name="PreviousCreditAmountLCY_PreviousDebitAmountLCY">
          <DataField>PreviousCreditAmountLCY_PreviousDebitAmountLCY</DataField>
        </Field>
        <Field Name="PreviousCreditAmountLCY_PreviousDebitAmountLCYFormat">
          <DataField>PreviousCreditAmountLCY_PreviousDebitAmountLCYFormat</DataField>
        </Field>
        <Field Name="PeriodDebitAmountLCY">
          <DataField>PeriodDebitAmountLCY</DataField>
        </Field>
        <Field Name="PeriodDebitAmountLCYFormat">
          <DataField>PeriodDebitAmountLCYFormat</DataField>
        </Field>
        <Field Name="PeriodCreditAmountLCY">
          <DataField>PeriodCreditAmountLCY</DataField>
        </Field>
        <Field Name="PeriodCreditAmountLCYFormat">
          <DataField>PeriodCreditAmountLCYFormat</DataField>
        </Field>
        <Field Name="PreviousDebitAmountLCY_PeriodDebitAmountLCY___PreviousCreditAmountLCY_PeriodCreditAmountLCY_">
          <DataField>PreviousDebitAmountLCY_PeriodDebitAmountLCY___PreviousCreditAmountLCY_PeriodCreditAmountLCY_</DataField>
        </Field>
        <Field Name="PreviousDebitAmountLCY_PeriodDebitAmountLCY___PreviousCreditAmountLCY_PeriodCreditAmountLCY_Format">
          <DataField>PreviousDebitAmountLCY_PeriodDebitAmountLCY___PreviousCreditAmountLCY_PeriodCreditAmountLCY_Format</DataField>
        </Field>
        <Field Name="PreviousCreditAmountLCY_PeriodCreditAmountLCY___PreviousDebitAmountLCY_PeriodDebitAmountLCY_">
          <DataField>PreviousCreditAmountLCY_PeriodCreditAmountLCY___PreviousDebitAmountLCY_PeriodDebitAmountLCY_</DataField>
        </Field>
        <Field Name="PreviousCreditAmountLCY_PeriodCreditAmountLCY___PreviousDebitAmountLCY_PeriodDebitAmountLCY_Format">
          <DataField>PreviousCreditAmountLCY_PeriodCreditAmountLCY___PreviousDebitAmountLCY_PeriodDebitAmountLCY_Format</DataField>
        </Field>
        <Field Name="PreviousDebitAmountLCY_PreviousCreditAmountLCY_Control1120069">
          <DataField>PreviousDebitAmountLCY_PreviousCreditAmountLCY_Control1120069</DataField>
        </Field>
        <Field Name="PreviousDebitAmountLCY_PreviousCreditAmountLCY_Control1120069Format">
          <DataField>PreviousDebitAmountLCY_PreviousCreditAmountLCY_Control1120069Format</DataField>
        </Field>
        <Field Name="PreviousCreditAmountLCY_PreviousDebitAmountLCY_Control1120072">
          <DataField>PreviousCreditAmountLCY_PreviousDebitAmountLCY_Control1120072</DataField>
        </Field>
        <Field Name="PreviousCreditAmountLCY_PreviousDebitAmountLCY_Control1120072Format">
          <DataField>PreviousCreditAmountLCY_PreviousDebitAmountLCY_Control1120072Format</DataField>
        </Field>
        <Field Name="PeriodDebitAmountLCY_Control1120075">
          <DataField>PeriodDebitAmountLCY_Control1120075</DataField>
        </Field>
        <Field Name="PeriodDebitAmountLCY_Control1120075Format">
          <DataField>PeriodDebitAmountLCY_Control1120075Format</DataField>
        </Field>
        <Field Name="PeriodCreditAmountLCY_Control1120078">
          <DataField>PeriodCreditAmountLCY_Control1120078</DataField>
        </Field>
        <Field Name="PeriodCreditAmountLCY_Control1120078Format">
          <DataField>PeriodCreditAmountLCY_Control1120078Format</DataField>
        </Field>
        <Field Name="PreviousDebitAmountLCY_PeriodDebitAmountLCY___PreviousCreditAmountLCY_PeriodCreditAmountLCY__Control1120081">
          <DataField>PreviousDebitAmountLCY_PeriodDebitAmountLCY___PreviousCreditAmountLCY_PeriodCreditAmountLCY__Control1120081</DataField>
        </Field>
        <Field Name="PreviousDebitAmountLCY_PeriodDebitAmountLCY___PreviousCreditAmountLCY_PeriodCreditAmountLCY__Control1120081Format">
          <DataField>PreviousDebitAmountLCY_PeriodDebitAmountLCY___PreviousCreditAmountLCY_PeriodCreditAmountLCY__Control1120081Format</DataField>
        </Field>
        <Field Name="PreviousCreditAmountLCY_PeriodCreditAmountLCY___PreviousDebitAmountLCY_PeriodDebitAmountLCY__Control1120084">
          <DataField>PreviousCreditAmountLCY_PeriodCreditAmountLCY___PreviousDebitAmountLCY_PeriodDebitAmountLCY__Control1120084</DataField>
        </Field>
        <Field Name="PreviousCreditAmountLCY_PeriodCreditAmountLCY___PreviousDebitAmountLCY_PeriodDebitAmountLCY__Control1120084Format">
          <DataField>PreviousCreditAmountLCY_PeriodCreditAmountLCY___PreviousDebitAmountLCY_PeriodDebitAmountLCY__Control1120084Format</DataField>
        </Field>
        <Field Name="Customer_Trial_BalanceCaption">
          <DataField>Customer_Trial_BalanceCaption</DataField>
        </Field>
        <Field Name="No_Caption">
          <DataField>No_Caption</DataField>
        </Field>
        <Field Name="NameCaption">
          <DataField>NameCaption</DataField>
        </Field>
        <Field Name="Balance_at_Starting_DateCaption">
          <DataField>Balance_at_Starting_DateCaption</DataField>
        </Field>
        <Field Name="Balance_Date_RangeCaption">
          <DataField>Balance_Date_RangeCaption</DataField>
        </Field>
        <Field Name="Balance_at_Ending_dateCaption">
          <DataField>Balance_at_Ending_dateCaption</DataField>
        </Field>
        <Field Name="DebitCaption">
          <DataField>DebitCaption</DataField>
        </Field>
        <Field Name="CreditCaption">
          <DataField>CreditCaption</DataField>
        </Field>
        <Field Name="DebitCaption_Control1120030">
          <DataField>DebitCaption_Control1120030</DataField>
        </Field>
        <Field Name="CreditCaption_Control1120032">
          <DataField>CreditCaption_Control1120032</DataField>
        </Field>
        <Field Name="DebitCaption_Control1120034">
          <DataField>DebitCaption_Control1120034</DataField>
        </Field>
        <Field Name="CreditCaption_Control1120036">
          <DataField>CreditCaption_Control1120036</DataField>
        </Field>
        <Field Name="Grand_totalCaption">
          <DataField>Grand_totalCaption</DataField>
        </Field>
      </Fields>
      <Query>
        <DataSourceName>DataSource</DataSourceName>
        <CommandText />
      </Query>
    </DataSet>
  </DataSets>
</Report>