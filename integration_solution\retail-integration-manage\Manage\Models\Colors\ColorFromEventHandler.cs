﻿using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using WSA.Retail.Integration.Manage.Configuration;
using WSA.Retail.Integration.Manage.Core;
using WSA.Retail.Integration.Manage.EventProcessing;
using WSA.Retail.Integration.Models.Configuration;
using WSA.Retail.Integration.Models.Couplings;
using WSA.Retail.Integration.Models.Colors;

namespace WSA.Retail.Integration.Manage.Models.Colors;

public class ColorFromEventHandler(
    IOptions<AppSettings> appSettings,
    ILogger<ColorFromEventHandler> logger,
    IColorService domainModelService,
    IEntitySubscriberService entitySubscriberService,
    ICouplingService couplingService,
    IEventHubEntityAdapter<ColorEventHubEvent, Color> eventHubEntityAdapter)
    : GenericFromEventHandler<
        ColorEventHubEvent,
        IColorService,
        Color,
        IEntitySubscriberService>(
            appSettings,
            domainModelService,
            entitySubscriberService,
            couplingService,
            eventHubEntityAdapter,
            EntityType.Color)

{
    private readonly ILogger<ColorFromEventHandler> _logger = logger;

    protected override Guid GetEventEntityId(ColorEventHubEvent entity)
    {
        return entity.Id ?? Guid.Empty;
    }

    protected override string? GetEventEntityName(ColorEventHubEvent entity)
    {
        return entity.Name;
    }
}