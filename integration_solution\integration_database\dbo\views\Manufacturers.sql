﻿CREATE VIEW [dbo].[Manufacturers]
AS 

SELECT Source.Id,
       Source.Code,
       (SELECT TOP 1 ExternalRecordId
          FROM dbo.Coupling
         WHERE Coupling.RecordId = Source.Id AND Coupling.ExternalSystemId = ExternalSystem.Id) AS ExternalCode,
       Source.[Name],
       ExternalSystem.Id AS ExternalSystemId,
       ExternalSystem.Code AS ExternalSystemCode,
       Source.[ModifiedOn],
       t1.JSON

  FROM dbo.Manufacturer AS Source

 OUTER APPLY dbo.ExternalSystem

 OUTER APPLY (SELECT(
              SELECT Manufacturer.Id AS 'id',
                     ExternalSystem.Code AS 'externalSystemCode',
                     UPPER(Manufacturer.Code) AS 'code',
                     (SELECT TOP 1 ExternalRecordId 
                        FROM dbo.Coupling 
                       WHERE Coupling.RecordId = Manufacturer.Id AND Coupling.ExternalSystemId = ExternalSystem.Id) AS 'externalCode',
                     Manufacturer.[Name] AS 'name'

                FROM dbo.Manufacturer

                WHERE Manufacturer.Id = Source.Id

                  FOR JSON PATH, WITHOUT_ARRAY_WRAPPER
                      ) AS JSON
                      ) AS t1