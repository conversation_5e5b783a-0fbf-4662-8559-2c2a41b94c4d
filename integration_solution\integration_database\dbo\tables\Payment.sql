﻿CREATE TABLE [dbo].[Payment]
(
    [Id]                UNIQUEIDENTIFIER CONSTRAINT [DF_Payment_Id] DEFAULT NEWID() NOT NULL,
    [DocumentNumber]    NVARCHAR(20) NOT NULL,
    [DocumentDate]      DATE NULL,
    [PatientId]         UNIQUEIDENTIFIER NULL,
    [PayorId]           UNIQUEIDENTIFIER NULL,
    [ClinicId]          UNIQUEIDENTIFIER NULL,
    [SalesOrderId]      UNIQUEIDENTIFIER NULL,
    [SalesInvoiceId]    UNIQUEIDENTIFIER NULL,
    [SalesCreditId]     UNIQUEIDENTIFIER NULL,
    [AppliesTo]         NVARCHAR(20) NULL,
    [ReferenceNumber]   NVARCHAR(50) NULL,
    [AuthCode]          NVARCHAR(20) NULL,
    [PaymentMethodId]   UNIQUEIDENTIFIER NULL,
    [PaymentMethod]     NVARCHAR(20) NULL,
    [Amount]            MONEY NULL,
    [Description]       NVARCHAR(100) NULL,
    [CreatedOn]         DATETIME2 CONSTRAINT [DF_Payment_CreatedOn] DEFAULT (sysutcdatetime())  NOT NULL,
    [ModifiedOn]        DATETIME2 CONSTRAINT [DF_Payment_ModifiedOn] DEFAULT (sysutcdatetime())  NOT NULL,
    CONSTRAINT [PK_Payment_Id] PRIMARY KEY CLUSTERED ([Id] ASC),
    CONSTRAINT [FK_Payment_Patient] FOREIGN KEY (PatientId) REFERENCES [dbo].[Patient](Id),
    CONSTRAINT [FK_Payment_Payor] FOREIGN KEY (PayorId) REFERENCES [dbo].[Payor](Id),
    CONSTRAINT [FK_Payment_Clinic] FOREIGN KEY (ClinicId) REFERENCES [dbo].[Clinic](Id),
    CONSTRAINT [FK_Payment_SalesOrder] FOREIGN KEY (SalesOrderId) REFERENCES [dbo].[SalesOrder](Id),
    CONSTRAINT [FK_Payment_SalesInvoice] FOREIGN KEY (SalesInvoiceId) REFERENCES [dbo].[SalesInvoice](Id),
    CONSTRAINT [FK_Payment_SalesCredit] FOREIGN KEY (SalesCreditId) REFERENCES [dbo].[SalesCredit](Id),
    CONSTRAINT [FK_Payment_PaymentMethod] FOREIGN KEY (PaymentMethodId) REFERENCES [dbo].[PaymentMethod](Id))
GO

CREATE UNIQUE INDEX IX_Payment_DocumentNumber
ON [dbo].[Payment] ([DocumentNumber])
GO

CREATE INDEX IX_Payment_DocumentDate
ON [dbo].[Payment] ([DocumentDate], [DocumentNumber])
GO

CREATE INDEX IX_Payment_SalesOrder
ON [dbo].[Payment] ([SalesOrderId], [Id])
GO;

CREATE INDEX IX_Payment_SalesInvoice
ON [dbo].[Payment] ([SalesInvoiceId], [Id])
GO;

CREATE INDEX IX_Payment_SalesCredit   
ON [dbo].[Payment] ([SalesCreditId], [Id])
GO;