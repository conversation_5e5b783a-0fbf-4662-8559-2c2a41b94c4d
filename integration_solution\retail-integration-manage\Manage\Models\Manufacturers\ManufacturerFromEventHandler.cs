﻿using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using WSA.Retail.Integration.Logging;
using WSA.Retail.Integration.Manage.Configuration;
using WSA.Retail.Integration.Manage.Core;
using WSA.Retail.Integration.Manage.EventProcessing;
using WSA.Retail.Integration.Models.Configuration;
using WSA.Retail.Integration.Models.Couplings;
using WSA.Retail.Integration.Models.Manufacturers;

namespace WSA.Retail.Integration.Manage.Models.Manufacturers;

public class ManufacturerFromEventHandler(
    IOptions<AppSettings> appSettings,
    ILogger<ManufacturerFromEventHandler> logger,
    IManufacturerService domainModelService,
    IEntitySubscriberService entitySubscriberService,
    ICouplingService couplingService,
    IEventHubEntityAdapter<ManufacturerEventHubEvent, Manufacturer> eventHubEntityAdapter) 
    : GenericFromEventHandler<
        ManufacturerEventHubEvent, 
        IManufacturerService,
        Manufacturer,
        IEntitySubscriberService>(
            appSettings,
            domainModelService,
            entitySubscriberService,
            couplingService,
            eventHubEntityAdapter,
            EntityType.Manufacturer)

{
    private readonly ILogger<ManufacturerFromEventHandler> _logger = logger;

    protected override void LogMethodStart()
    {
        _logger.LogMethodStart();
    }
    protected override void LogCustomInformation(string message)
    {
        _logger.LogCustomInformation(message);
    }
    protected override void LogCustomWarning(string message)
    {
        _logger.LogCustomWarning(message);
    }
    protected override void LogCustomError(Exception ex, string message)
    {
        _logger.LogCustomError(ex, message);
    }
    protected override void LogCustomError(Exception ex)
    {
        _logger.LogCustomError(ex);
    }

    protected override Guid GetEventEntityId(ManufacturerEventHubEvent entity)
    {
        return entity.Id ?? Guid.Empty;
    }

    protected override string? GetEventEntityName(ManufacturerEventHubEvent entity)
    {
        return entity.Name;
    }

    protected override async Task AfterConvertToEntity(Manufacturer entity)
    {
        // Manufacturer doesn't have a code in in Manage.  Fax is used instead.  This is a workaround
        // to lookup the code by externalCode or Name, then fall back to using the name as a code.

        if (string.IsNullOrWhiteSpace(entity.Code))
        {
            if (!string.IsNullOrWhiteSpace(entity.ExternalCode))
            {
                var mfgByExternalCode = await _domainModelService.GetListAsync(
                    _appSettings.ExternalSystemCode,
                    externalCode: entity.ExternalCode);

                if (mfgByExternalCode.Count > 0)
                {
                    entity.Code = mfgByExternalCode[0].Code;
                    return;
                }
            }

            if (!string.IsNullOrWhiteSpace(entity.Name))
            {
                var mfgByName = await _domainModelService.GetListAsync(
                    _appSettings.ExternalSystemCode, 
                    name: entity.Name);

                if (mfgByName.Count > 0)
                {
                    entity.Code = mfgByName[0].Code;
                    return;
                }

                entity.Code = entity.Name.ToUpper();
                if (entity.Code.Length > 20)
                {
                    entity.Code = entity.Code[..20];
                    return;
                }
            }
        }
    }

}