﻿using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using WSA.Retail.Integration.Configuration;
using WSA.Retail.Integration.Logging;
using WSA.Retail.Integration.PIM.Data;
using WSA.Retail.Integration.PIM.GraphQL;
using WSA.Retail.Integration.Utilities;

namespace WSA.Retail.Integration.PIM.Models.Categories;

class PimCategoryRepository(
    IOptions<AppSettings> appSettings,
    ILogger<PimCategoryRepository> logger,
    IDbContextFactory<PimDbContext> dbContextFactory
    ) : IPimCategoryRepository
{
    private readonly AppSettings _appSettings = appSettings.Value;
    private readonly ILogger<PimCategoryRepository> _logger = logger;
    private readonly IDbContextFactory<PimDbContext> _dbContextFactory = dbContextFactory;


    public async Task<bool> UpsertAsync(GraphQLCategory category)
    {
        _logger.LogMethodStart();
        _logger.LogCustomInformation($"Upserting {nameof(PimCategoryEntity)}: {category.CategoryName}");
        var context = _dbContextFactory.CreateDbContext();

        try
        {
            var existingCategory = await context.PimCategories.FindAsync(category.CategoryId);
            if (existingCategory == null)
            {
                PimCategoryEntity newCategory = new()
                {
                    Id = category.CategoryId,
                    Name = category.CategoryName,
                    ParentCategoryId = category.ParentCategoryId
                };
                context.PimCategories.Add(newCategory);
                await context.SaveChangesAsync();
                _logger.LogCustomInformation($"Inserting {nameof(PimCategoryEntity)}: {category.CategoryName}");
                return true;
            }
            else
            {
                if (HasChanges(existingCategory, category)) 
                {
                    existingCategory.Name = category.CategoryName;
                    existingCategory.ParentCategoryId = category.ParentCategoryId;
                    existingCategory.SystemModifiedOn = DateTime.UtcNow;
                    await context.SaveChangesAsync();
                    _logger.LogCustomInformation($"Updating {nameof(PimCategoryEntity)}: {category.CategoryName}");
                    return true;
                }
                else
                {
                    _logger.LogCustomInformation($"{nameof(PimCategoryEntity)}: {category.CategoryName} has not changed.");
                    return true;
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogCustomError(ex);
            return false;
        }
    }

    private static bool HasChanges(PimCategoryEntity oldCategory, GraphQLCategory newCategory)
    {
        if (oldCategory == null) return true;
        if (!Common.AreEqual(oldCategory.Name, newCategory.CategoryName)) return true;
        if (!Common.AreEqual(oldCategory.ParentCategoryId, newCategory.ParentCategoryId)) return true;
        return false;
    }
}
