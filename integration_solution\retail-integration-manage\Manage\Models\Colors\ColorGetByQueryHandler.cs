﻿using Microsoft.Extensions.Options;
using WSA.Retail.Integration.Manage.Configuration;
using WSA.Retail.Integration.Manage.Core;
using WSA.Retail.Integration.Models.Colors;

namespace WSA.Retail.Integration.Manage.Models.Colors;

public class ColorGetByQueryHandler(
    IOptions<AppSettings> appSettings,
    IColorService entityService)
    : BaseApiGetByQuery<
        Color,
        IColorService>(appSettings, entityService)
{
}