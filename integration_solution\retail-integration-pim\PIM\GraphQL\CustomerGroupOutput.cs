﻿using System.Text.Json.Serialization;

namespace WSA.Retail.Integration.PIM.GraphQL;

public class CustomerGroupOutput
{
    [JsonPropertyName("code")] public string? Code { get; set; }

    [JsonPropertyName("currency")] public string? Currency { get; set; }

    [JsonPropertyName("description")] public string? Description { get; set; }

    [JsonPropertyName("state")] public StateOutputType? State { get; set; }
}