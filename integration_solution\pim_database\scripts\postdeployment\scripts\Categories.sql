﻿DECLARE @categoryCoupling TABLE (Code NVARCHAR(20), ExternalCode NVARCHAR(255))
INSERT INTO @categoryCoupling VALUES 
('1110', 'BTE'),
('1130', 'INS'),
('1140', 'ITE'),
('1160', 'RIC'),
('1210', 'EM'),
('1220', 'A03'),
('1290', 'NRE'),
('1310', 'A09 Receiver'),
('1390', 'A04'),
('1390', 'A05'),
('1410', 'A02'),
('1490', 'A07'),
('1490', 'A13'),
('1490', 'A14'),
('1490', 'A16'),
('1490', 'A17'),
('1490', 'A99'),
('1490', 'Accessory'),
('1410', 'Consumable'),
('1490', 'S01'),
('1490', 'S03'),
('1490', 'S88'),
('1490', 'Spare Part'),
('2200', 'NWY');

  WITH Source AS (
SELECT (SELECT TOP 1 Id FROM dbo.ExternalSystem WHERE Code = 'PIM') AS ExternalSystemId,
       (SELECT TOP 1 Id FROM dbo.Entity WHERE Code = 'CATEGORY') AS EntityId,
       ProductCategory.Id AS RecordId,
       c1.ExternalCode AS ExternalRecordId
  FROM dbo.ProductCategory
 INNER JOIN @categoryCoupling AS c1
    ON ProductCategory.Code = c1.Code
)

INSERT INTO dbo.Coupling (ExternalSystemId, EntityId, RecordId, ExternalRecordId)
SELECT ExternalSystemId, EntityId, RecordId, ExternalRecordId
  FROM Source

WHERE NOT EXISTS (SELECT * FROM dbo.Coupling 
                   WHERE ExternalSystemId = Source.ExternalSystemId
                     AND EntityId = Source.EntityId
                     AND RecordId = Source.RecordId
                     AND ExternalRecordId = Source.ExternalRecordId)