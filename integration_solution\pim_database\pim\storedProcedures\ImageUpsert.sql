﻿CREATE PROCEDURE [pim].[ImageUpsert]
(
    @source [pim].[ImageUpsertTableType] READONLY,

    -- Add output parameters for diagnostics
    @rowsReceived INT = NULL OUTPUT,
    @diagnosticMessage NVARCHAR(MAX) = NULL OUTPUT
)
AS
BEGIN
DECLARE @distinctImageCount INT;
DECLARE @currentImageCount INT;
DECLARE @firstMergeAffected INT;
DECLARE @finalImageCount INT;
DECLARE @diagnosticLog NVARCHAR(MAX) = '';

-- Populate variables
SELECT @rowsReceived = COUNT(*) FROM @source;
SELECT @currentImageCount = COUNT(*) FROM pim.Image;

-- Build diagnostic info
SET @diagnosticLog = 'Received ' + CAST(@rowsReceived AS VARCHAR(20)) + ' rows in @source parameter';
SET @diagnosticLog = @diagnosticLog + CHAR(13) + CHAR(10) + 'Current pim.Image count: ' + CAST(@currentImageCount AS VARCHAR(20))


 BEGIN TRY
   SET XACT_ABORT ON
 BEGIN TRANSACTION


 MERGE pim.[Image] AS Target
 USING (SELECT s1.ProductId,
               Brand.Id AS BrandId,
               s1.CDNUrl,
               S1.IsDefault
          FROM (SELECT DISTINCT * FROM @source) AS s1
          LEFT JOIN pim.Brand ON s1.BrandCode = Brand.Code
             ) AS Source
    ON Source.ProductId = Target.ProductId
   AND Source.BrandId = Target.BrandId
   AND Source.CDNUrl = Target.CDNUrl
   
  WHEN NOT MATCHED BY Target 
  THEN
INSERT (ProductId, BrandId, CDNUrl, IsDefault)
VALUES (Source.ProductId, Source.BrandId, Source.CDNUrl, Source.IsDefault)

  WHEN MATCHED AND Source.IsDefault <> Target.IsDefault 
  THEN
UPDATE
   SET IsDefault = Source.IsDefault,
       SystemModifiedOn = sysutcdatetime();

   SET @firstMergeAffected = @@rowCount;
   SET @diagnosticLog = @diagnosticLog + CHAR(13) + CHAR(10) + 'Rows affected by pim.Image MERGE: ' + CAST(@firstMergeAffected AS VARCHAR(20));


COMMIT TRANSACTION;

-- Final counts
SELECT @finalImageCount = COUNT(*) FROM pim.Image;
SET @diagnosticLog = @diagnosticLog + CHAR(13) + CHAR(10) + 'Final pim.Image count: ' + CAST(@finalImageCount AS VARCHAR(20))

  
   END TRY
 BEGIN CATCH
    IF @@TRANCOUNT > 0
       ROLLBACK TRANSACTION;
            
       SET @diagnosticLog = @diagnosticLog + CHAR(13) + CHAR(10) + 'Error occurred: ' + ERROR_MESSAGE();
       SET @diagnosticMessage = @diagnosticLog;
       THROW;
   END CATCH
    
   SET @diagnosticMessage = @diagnosticLog;
END