﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace WSA.Retail.Integration.Cosium
{
    [Table("TauxTVA", Schema = "cosium")]
    public class TauxTVA
    {
        [Key]
        [Column("id")]
        public string? Id { get; set; }
        [Column("taux")]
        public string? Taux { get; set; }
        [Column("datemodif")]
        public string? DateModif { get; set; }
    }
}
