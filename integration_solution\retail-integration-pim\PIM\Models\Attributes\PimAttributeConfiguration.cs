using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using WSA.Retail.Integration.PIM.Data;

namespace WSA.Retail.Integration.PIM.Models.Attributes
{
    public class PimAttributeConfiguration : IEntityTypeConfiguration<PimAttributeEntity>
    {
        public void Configure(EntityTypeBuilder<PimAttributeEntity> builder)
        {
            builder.ToTable("Attribute", "pim");

            builder.<PERSON><PERSON><PERSON>(x => x.Id);
            builder.HasAlternateKey(x => x.Code);

            builder.ConfigureIdentifiableFields();
            builder.ConfigureCodeIdentifiableFields();
            builder.ConfigureNamableFields();
            builder.ConfigureAuditInfoFields();

            builder.Property(x => x.DataType)
                .HasColumnName("DataType")
                .HasColumnType("nvarchar(100)");
        }
    }
}