﻿CREATE TABLE [cosium].[site] (
    [id]                        NVARCHAR (50)  NOT NULL,
    [nomsite]                   NVARCHAR (50)  NULL,
    [libellefordisplaylongsite] NVARCHAR (50)  NULL,
    [telsite]                   NVARCHAR (50)  NULL,
    [adressesite]               NVARCHAR (50)  NULL,
    [cpsite]                    NVARCHAR (50)  NULL,
    [villesite]                 NVARCHAR (50)  NULL,
    [pays]                      NVARCHAR (50)  NULL,
    [latitudesite]              NVARCHAR (50)  NULL,
    [longitudesite]             NVARCHAR (50)  NULL,
    [urloptic]                  NVARCHAR (100) NULL,
    [urlaudio]                  NVARCHAR (100) NULL,
    [refentite]                 NVARCHAR (50)  NULL,
    [datemodif]                 NVARCHAR (50)  NULL,
    [ref_client_passage]        NVARCHAR (50)  NULL,
    [societe]                   NVARCHAR (50)  NULL,
    [libellelongsite]           NVARCHAR (50)  NULL,
    [codesite]                  NVARCHAR (50)  NULL,
    [commentairesite]           NVARCHAR (MAX) NULL,
    [finess]                    NVARCHAR (50)  NULL,
    [siret]                     NVARCHAR (50)  NULL,
    [numagrementss]             NVARCHAR (50)  NULL,
    [faxsite]                   NVARCHAR (50)  NULL,
    [email]                     NVARCHAR (100) NULL,
    [cheminexportcomptable]     NVARCHAR (50)  NULL,
    [logincrm]                  NVARCHAR (50)  NULL,
    [nomsiteaffiche]            NVARCHAR (50)  NULL,
    [datedebutactivite]         NVARCHAR (50)  NULL,
    [datefinactivite]           NVARCHAR (50)  NULL,
    [CreatedOn]                 DATETIME2 CONSTRAINT [DF_site_CreatedOn] DEFAULT (sysutcdatetime()) NOT NULL,
    [ModifiedOn]                DATETIME2 CONSTRAINT [DF_site_ModifiedOn] DEFAULT (sysutcdatetime()) NOT NULL,
    [IntegrationRequired]   BIT DEFAULT 1 NOT NULL,
    [IntegrationDate]       DATETIME2(7) NULL
    CONSTRAINT [PK_site] PRIMARY KEY CLUSTERED ([id] ASC)
);
GO

CREATE INDEX IX_site_codesite
ON [cosium].[site] ([codesite])
GO

CREATE INDEX IX_site_ModifiedOn
ON [cosium].[site] ([ModifiedOn])
GO

CREATE INDEX IX_site_IntegrationRequired
ON [cosium].[site] ([IntegrationRequired], [id])
GO

CREATE TRIGGER [cosium].site_UpdateModified
ON [cosium].[site]
AFTER UPDATE 
AS
   UPDATE [cosium].[site]
      SET [ModifiedOn] = sysutcdatetime(),
          [IntegrationRequired] = IIF(ISNULL(d.[datemodif], '1900-01-01') < i.[datemodif], 1, [site].[IntegrationRequired])
     FROM Inserted AS i
          LEFT JOIN deleted AS d
            ON i.id = d.id
    WHERE [site].[id] = i.[id]
GO
