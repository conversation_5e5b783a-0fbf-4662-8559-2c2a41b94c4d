﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003" ToolsVersion="4.0">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <Name>pim_database</Name>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectVersion>4.1</ProjectVersion>
    <ProjectGuid>{e2204611-702e-4fdc-821a-717f4f255d14}</ProjectGuid>
    <DSP>Microsoft.Data.Tools.Schema.Sql.SqlAzureV12DatabaseSchemaProvider</DSP>
    <OutputType>Database</OutputType>
    <RootPath>
    </RootPath>
    <RootNamespace>pim_database</RootNamespace>
    <AssemblyName>pim_database</AssemblyName>
    <ModelCollation>1033, CI</ModelCollation>
    <DefaultFileStructure>BySchemaAndSchemaType</DefaultFileStructure>
    <DeployToDatabase>True</DeployToDatabase>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <TargetLanguage>CS</TargetLanguage>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <SqlServerVerification>False</SqlServerVerification>
    <IncludeCompositeObjects>True</IncludeCompositeObjects>
    <TargetDatabaseSet>True</TargetDatabaseSet>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <OutputPath>bin\Release\</OutputPath>
    <BuildScriptName>$(MSBuildProjectName).sql</BuildScriptName>
    <TreatWarningsAsErrors>False</TreatWarningsAsErrors>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <DefineDebug>false</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <OutputPath>bin\Debug\</OutputPath>
    <BuildScriptName>$(MSBuildProjectName).sql</BuildScriptName>
    <TreatWarningsAsErrors>false</TreatWarningsAsErrors>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <DefineDebug>true</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup>
    <VisualStudioVersion Condition="'$(VisualStudioVersion)' == ''">11.0</VisualStudioVersion>
    <!-- Default to the v11.0 targets path if the targets file for the current VS version is not found -->
    <SSDTExists Condition="Exists('$(MSBuildExtensionsPath)\Microsoft\VisualStudio\v$(VisualStudioVersion)\SSDT\Microsoft.Data.Tools.Schema.SqlTasks.targets')">True</SSDTExists>
    <VisualStudioVersion Condition="'$(SSDTExists)' == ''">11.0</VisualStudioVersion>
  </PropertyGroup>
  <Import Condition="'$(SQLDBExtensionsRefPath)' != ''" Project="$(SQLDBExtensionsRefPath)\Microsoft.Data.Tools.Schema.SqlTasks.targets" />
  <Import Condition="'$(SQLDBExtensionsRefPath)' == ''" Project="$(MSBuildExtensionsPath)\Microsoft\VisualStudio\v$(VisualStudioVersion)\SSDT\Microsoft.Data.Tools.Schema.SqlTasks.targets" />
  <ItemGroup>
    <Folder Include="Properties" />
    <Folder Include="pim" />
    <Folder Include="pim\schemas" />
    <Folder Include="pim\storedProcedures" />
    <Folder Include="pim\tables" />
    <Folder Include="pim\types" />
    <Folder Include="scripts" />
    <Folder Include="scripts\postdeployment" />
    <Folder Include="scripts\postdeployment\scripts" />
  </ItemGroup>
  <ItemGroup>
    <Build Include="pim\schemas\pim.sql" />
    <Build Include="pim\storedProcedures\AttributeUpsert.sql" />
    <Build Include="pim\storedProcedures\CategoryUpsert.sql" />
    <Build Include="pim\storedProcedures\ColorUpsert.sql" />
    <Build Include="pim\storedProcedures\ProductUpsert.sql" />
    <Build Include="pim\tables\Attribute.sql" />
    <Build Include="pim\tables\Brand.sql" />
    <Build Include="pim\tables\BundleSet.sql" />
    <Build Include="pim\tables\Category.sql" />
    <Build Include="pim\tables\Color.sql" />
    <Build Include="pim\tables\Image.sql" />
    <Build Include="pim\tables\Product.sql" />
    <Build Include="pim\tables\ProductAttribute.sql" />
    <Build Include="pim\tables\ProductBrand.sql" />
    <Build Include="pim\tables\ProductBundleSet.sql" />
    <Build Include="pim\tables\ProductCategory.sql" />
    <Build Include="pim\tables\ProductParent.sql" />
    <Build Include="pim\types\ProductUpsertTableType.sql" />
    <Build Include="pim\types\ColorUpsertTableType.sql" />
    <Build Include="pim\types\BrandUpsertTableType.sql" />
    <Build Include="pim\storedProcedures\BrandUpsert.sql" />
    <Build Include="pim\types\ImageUpsertTableType.sql" />
    <Build Include="pim\storedProcedures\ImageUpsert.sql" />
    <Build Include="pim\types\BundleSetUpsertTableType.sql" />
    <Build Include="pim\storedProcedures\BundleSetUpsert.sql" />
    <Build Include="pim\types\AttributeUpsertTableType.sql" />
    <Build Include="pim\types\ProductCategoryUpsertTableType.sql" />
    <Build Include="pim\types\ProductParentUpsertTableType.sql" />
    <Build Include="pim\storedProcedures\ProductParentUpsert.sql" />
    <Build Include="pim\tables\Country.sql" />
    <Build Include="pim\tables\CountryBrand.sql" />
    <None Include="scripts\postdeployment\scripts\ExternalSystems.sql" />
    <None Include="scripts\postdeployment\scripts\EntitySubscribers.sql" />
    <None Include="scripts\postdeployment\scripts\Batteries.sql" />
    <None Include="scripts\postdeployment\scripts\User.sql" />
  </ItemGroup>
  <ItemGroup>
    <None Include="pim_database.publish.xml" />
    <None Include="scripts\postdeployment\scripts\Categories.sql" />
    <None Include="scripts\postdeployment\scripts\Brands.sql" />
    <None Include="scripts\postdeployment\scripts\Manufacturers.sql" />
  </ItemGroup>
  <ItemGroup>
    <PostDeploy Include="scripts\postdeployment\PostDeployment.sql" />
  </ItemGroup>
</Project>