using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Builder;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using WSA.Retail.Integration.Core;
using WSA.Retail.Integration.Manage.Configuration;
using WSA.Retail.Integration.Manage.Core;


var builder = FunctionsApplication.CreateBuilder(args);

builder.ConfigureFunctionsWebApplication();
builder.Services
    .AddApplicationInsightsTelemetryWorkerService()
    .ConfigureFunctionsApplicationInsights();


// ==== SETTINGS ===================================================
var appSettings = Activator.CreateInstance<AppSettings>()!;
builder.Configuration.Bind(appSettings);

builder.Services.Configure<AppSettings>(builder.Configuration);
builder.Services.AddSingleton(appSettings);


// ==== CORE SERVICES ==============================================
builder.Services.AddCoreServices(builder.Configuration);
builder.Services.AddManageServices(appSettings);

var host = builder.Build();

host.Run();