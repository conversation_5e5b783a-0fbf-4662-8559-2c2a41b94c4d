﻿CREATE TABLE [bc].[SalesCredit]
(
	[Id] UNIQUEIDENTIFIER NOT NULL DEFAULT NEWID(),
	[CompanyId] UNIQUEIDENTIFIER,
	[ExternalCompanyId] UNIQUEIDENTIFIER NOT NULL,
	[ExternalId] UNIQUEIDENTIFIER NOT NULL,
	[DocumentNumber] NVARCHAR(20),
	[ExternalDocumentNumber] NVARCHAR(35),
	[DocumentDate] DATETIME2(7),
	[PostingDate] DATETIME2(7),
	[CustomerId] UNIQUEIDENTIFIER,
	[ExternalCustomerId] UNIQUEIDENTIFIER, 
	[SellToCustomerId] UNIQUEIDENTIFIER,
	[ExternalSellToCustomerId] UNIQUEIDENTIFIER,
	[ResponsibilityCenterId] UNIQUEIDENTIFIER,
	[ExternalResponsibilityCenterId] UNIQUEIDENTIFIER,
	[CurrencyId] UNIQUEIDENTIFIER,
	[ExternalCurrencyId] UNIQUEIDENTIFIER,
	[PaymentTermsId] UNIQUEIDENTIFIER,
	[ExternalPaymentTermsId] UNIQUEIDENTIFIER,
	[DimensionSetId] UNIQUEIDENTIFIER,
	[ExternalDimensionSetNumber] INT,
	[AmountExcludingTax] DECIMAL(18, 4),
	[AmountIncludingTax] DECIMAL(18, 4),
	[ExternalCreatedOn] DATETIME2(7),
	[ExternalModifiedOn] DATETIME2(7),
	[CreatedOn] DATETIME2(7) NOT NULL DEFAULT SYSUTCDATETIME(),
	[ModifiedOn] DATETIME2(7) NOT NULL DEFAULT SYSUTCDATETIME(),
	CONSTRAINT [PK_SalesCredit_Id] PRIMARY KEY CLUSTERED ([Id] ASC)
)
GO

CREATE UNIQUE INDEX [IX_SalesCredit_CompanyId_ExternalId]
ON [bc].[SalesCredit] ([CompanyId], [ExternalId])
GO

CREATE INDEX [IX_SalesCredit_PostingDate_DocumentNumber]
ON [bc].[SalesCredit] ([PostingDate], [DocumentNumber])
GO