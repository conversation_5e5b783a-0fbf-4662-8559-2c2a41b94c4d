﻿CREATE PROCEDURE [cosium].[UpsertStagedClient]
AS

        SET XACT_ABORT ON
        BEGIN TRANSACTION
     
       UPDATE cosium.client
          SET [denomination] = Source.[denomination], 
              [nom] = Source.[nom], 
              [prenom] = Source.[prenom], 
              [datedenaissance] = Source.[datedenaissance], 
              [secunumero] = Source.[secunumero], 
              [secudatedebutdroit] = Source.[secudatedebutdroit], 
              [secudatefindroit] = Source.[secudatefindroit], 
              [secutauxremboursement] = Source.[secutauxremboursement], 
              [adresse1] = Source.[adresse1], 
              [adresse1bis] = Source.[adresse1bis], 
              [adresse1codepostal] = Source.[adresse1codepostal], 
              [adresse1ligne3] = Source.[adresse1ligne3], 
              [adresse1ligne4] = Source.[adresse1ligne4], 
              [adresse1ville] = Source.[adresse1ville], 
              [adresse1pays] = Source.[adresse1pays], 
              [denomination2] = Source.[denomination2], 
              [nom2] = Source.[nom2], 
              [prenom2] = Source.[prenom2], 
              [adresse2] = Source.[adresse2], 
              [adresse2bis] = Source.[adresse2bis], 
              [adresse2ligne3] = Source.[adresse2ligne3], 
              [adresse2ligne4] = Source.[adresse2ligne4], 
              [adresse2codepostal] = Source.[adresse2codepostal], 
              [adresse2ville] = Source.[adresse2ville], 
              [adresse2pays] = Source.[adresse2pays], 
              [typeadr2] = Source.[typeadr2], 
              [medecin] = Source.[medecin], 
              [telportable] = Source.[telportable], 
              [telbureau] = Source.[telbureau], 
              [teldomicile] = Source.[teldomicile], 
              [commentteld] = Source.[commentteld], 
              [email] = Source.[email], 
              [datecreation] = Source.[datecreation],
              [centre] = Source.[centre], 
              [stereo] = Source.[stereo], 
              [sterealisable] = Source.[sterealisable], 
              [caissesecu] = Source.[caissesecu], 
              [nomassure] = Source.[nomassure], 
              [assurenom] = Source.[assurenom], 
              [assureprenom] = Source.[assureprenom], 
              [assuretitre] = Source.[assuretitre], 
              [allocationforfaitaire] = Source.[allocationforfaitaire], 
              [appareillable] = Source.[appareillable], 
              [cache] = Source.[cache], 
              [exclus] = Source.[exclus], 
              [reforigine] = Source.[reforigine], 
              [article115] = Source.[article115], 
              [generaliste] = Source.[generaliste], 
              [prospect] = Source.[prospect], 
              [audioid] = Source.[audioid], 
              [fax] = Source.[fax], 
              [numeroclient] = Source.[numeroclient], 
              [offrecommercialemail] = Source.[offrecommercialemail], 
              [offrecommercialesms] = Source.[offrecommercialesms], 
              [rang] = Source.[rang], 
              [datenaissassure] = Source.[datenaissassure], 
              [refvendeur] = Source.[refvendeur], 
              [prescription] = Source.[prescription], 
              [adresse1npai] = Source.[adresse1npai], 
              [adresse2npai] = Source.[adresse2npai], 
              [datelunaire] = Source.[datelunaire], 
              [pertesaudioog] = Source.[pertesaudioog], 
              [pertesaudiood] = Source.[pertesaudiood], 
              [pertesaudiomoyenne] = Source.[pertesaudiomoyenne], 
              [datemodif] = Source.[datemodif], 
              [creator_id] = Source.[creator_id]

         FROM cosium.client

              INNER JOIN cosium.staging_client AS Source
                      ON client.id = Source.id

        WHERE client.datemodif <> Source.datemodif;

        INSERT INTO cosium.client (
              [id], 
              [denomination], 
              [nom], 
              [prenom], 
              [datedenaissance], 
              [secunumero], 
              [secudatedebutdroit], 
              [secudatefindroit], 
              [secutauxremboursement], 
              [adresse1], 
              [adresse1bis], 
              [adresse1codepostal], 
              [adresse1ligne3], 
              [adresse1ligne4], 
              [adresse1ville], 
              [adresse1pays], 
              [denomination2], 
              [nom2], 
              [prenom2], 
              [adresse2], 
              [adresse2bis], 
              [adresse2ligne3], 
              [adresse2ligne4], 
              [adresse2codepostal], 
              [adresse2ville], 
              [adresse2pays], 
              [typeadr2], 
              [medecin], 
              [telportable], 
              [telbureau], 
              [teldomicile], 
              [commentteld], 
              [email], 
              [datecreation],
              [centre], 
              [stereo], 
              [sterealisable], 
              [caissesecu], 
              [nomassure], 
              [assurenom], 
              [assureprenom], 
              [assuretitre], 
              [allocationforfaitaire], 
              [appareillable], 
              [cache], 
              [exclus], 
              [reforigine], 
              [article115], 
              [generaliste], 
              [prospect], 
              [audioid], 
              [fax], 
              [numeroclient], 
              [offrecommercialemail], 
              [offrecommercialesms], 
              [rang], 
              [datenaissassure], 
              [refvendeur], 
              [prescription], 
              [adresse1npai], 
              [adresse2npai], 
              [datelunaire], 
              [pertesaudioog], 
              [pertesaudiood], 
              [pertesaudiomoyenne], 
              [datemodif], 
              [creator_id])

       SELECT Source.[id], 
              Source.[denomination], 
              Source.[nom], 
              Source.[prenom], 
              Source.[datedenaissance], 
              Source.[secunumero], 
              Source.[secudatedebutdroit], 
              Source.[secudatefindroit], 
              Source.[secutauxremboursement], 
              Source.[adresse1], 
              Source.[adresse1bis], 
              Source.[adresse1codepostal], 
              Source.[adresse1ligne3], 
              Source.[adresse1ligne4], 
              Source.[adresse1ville], 
              Source.[adresse1pays], 
              Source.[denomination2], 
              Source.[nom2], 
              Source.[prenom2], 
              Source.[adresse2], 
              Source.[adresse2bis], 
              Source.[adresse2ligne3], 
              Source.[adresse2ligne4], 
              Source.[adresse2codepostal], 
              Source.[adresse2ville], 
              Source.[adresse2pays], 
              Source.[typeadr2], 
              Source.[medecin], 
              Source.[telportable], 
              Source.[telbureau], 
              Source.[teldomicile], 
              Source.[commentteld], 
              Source.[email], 
              Source.[datecreation],
              Source.[centre], 
              Source.[stereo], 
              Source.[sterealisable], 
              Source.[caissesecu], 
              Source.[nomassure], 
              Source.[assurenom], 
              Source.[assureprenom], 
              Source.[assuretitre], 
              Source.[allocationforfaitaire], 
              Source.[appareillable], 
              Source.[cache], 
              Source.[exclus], 
              Source.[reforigine], 
              Source.[article115], 
              Source.[generaliste], 
              Source.[prospect], 
              Source.[audioid], 
              Source.[fax], 
              Source.[numeroclient], 
              Source.[offrecommercialemail], 
              Source.[offrecommercialesms], 
              Source.[rang], 
              Source.[datenaissassure], 
              Source.[refvendeur], 
              Source.[prescription], 
              Source.[adresse1npai], 
              Source.[adresse2npai], 
              Source.[datelunaire], 
              Source.[pertesaudioog], 
              Source.[pertesaudiood], 
              Source.[pertesaudiomoyenne], 
              Source.[datemodif], 
              Source.[creator_id]

         FROM cosium.staging_client AS Source

        WHERE NOT EXISTS (SELECT * FROM cosium.client WHERE id = Source.id)

     TRUNCATE TABLE cosium.staging_client
       
       COMMIT TRANSACTION;