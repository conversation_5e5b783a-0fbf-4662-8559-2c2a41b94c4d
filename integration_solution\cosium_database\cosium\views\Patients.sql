﻿CREATE VIEW [cosium].[Patients]
AS

SELECT Source.id,
       CONCAT('PAT-', FORMAT(CAST(Source.id AS INT), '**********')) AS Code,
       Source.id AS ExternalCode,
       TRIM(TRIM(ISNULL(Source.prenom, '')) + ' ' + TRIM(ISNULL(Source.nom, ''))) AS [Name],
       Source.IntegrationRequired,
       Source.IntegrationDate,
       Source.ModifiedOn,
       t1.JSON

  FROM cosium.client AS Source

 OUTER APPLY  (
       SELECT (
              SELECT CONCAT('PAT-', FORMAT(CAST(client.id AS INT), '**********')) AS 'code',
                     client.numeroclient AS 'alternateCode',
                     client.id AS 'externalCode',
                     TRIM(TRIM(ISNULL(client.prenom, '')) + ' ' + TRIM(ISNULL(client.nom, ''))) AS 'name', 
                     UPPER(LEFT(client.secunumero, 20)) AS 'identificationNumber'

                FROM cosium.client

               WHERE client.id = Source.id

                 FOR JSON PATH, WITHOUT_ARRAY_WRAPPER

                     ) AS JSON
              ) AS t1