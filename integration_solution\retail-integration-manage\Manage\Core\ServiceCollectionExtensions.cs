﻿using Azure.Storage.Blobs;
using Azure.Storage.Queues;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using WSA.Retail.Integration.Manage.API;
using WSA.Retail.Integration.Manage.Configuration;
using WSA.Retail.Integration.Manage.Data;
using WSA.Retail.Integration.Manage.Data.Repositories;
using WSA.Retail.Integration.Manage.Data.Repositories.Interfaces;
using WSA.Retail.Integration.Manage.EventProcessing;
using WSA.Retail.Integration.Manage.Models.Batteries;
using WSA.Retail.Integration.Manage.Models.CategoryMappings;
using WSA.Retail.Integration.Manage.Models.Clinics;
using WSA.Retail.Integration.Manage.Models.Colors;
using WSA.Retail.Integration.Manage.Models.Countries;
using WSA.Retail.Integration.Manage.Models.Manufacturers;
using WSA.Retail.Integration.Manage.Models.Patients;
using WSA.Retail.Integration.Manage.Models.Payors;
using WSA.Retail.Integration.Manage.Models.ProductModels;
using WSA.Retail.Integration.Manage.Models.Products;
using WSA.Retail.Integration.Manage.Models.Vendors;
using WSA.Retail.Integration.Models.Colors;
using WSA.Retail.Integration.Models.Clinics;
using WSA.Retail.Integration.Models.Manufacturers;
using WSA.Retail.Integration.Models.Patients;
using WSA.Retail.Integration.Models.Payors;
using WSA.Retail.Integration.Models.Products;
using WSA.Retail.Integration.Models.Vendors;

namespace WSA.Retail.Integration.Manage.Core;

public static class ServiceCollectionExtensions
{
    public static IServiceCollection AddManageServices(
        this IServiceCollection services,
        AppSettings appSettings)
    {
        // ==== EVENT PROCESSOR =============================================
        services.AddSingleton<EventProcessor>();

        // ==== EVENT HUB ===================================================
        ArgumentException.ThrowIfNullOrWhiteSpace(appSettings.EventHubConnectionString, "EventHubConnectionString");
        ArgumentException.ThrowIfNullOrWhiteSpace(appSettings.EventHubConsumerGroup, "EventHubConsumerGroup");
        ArgumentException.ThrowIfNullOrWhiteSpace(appSettings.EventHubName, "EventHubName");


        // ==== MANAGE API ==================================================
        ArgumentException.ThrowIfNullOrWhiteSpace(appSettings.ManageApiBaseUrl, "ManageApiBaseUrl");
        ArgumentException.ThrowIfNullOrWhiteSpace(appSettings.ManageApiKey, "ManageApiKey");
        services.AddHttpClient<ManageAPI>(client =>
        {
            client.BaseAddress = new Uri(appSettings.ManageApiBaseUrl);
            client.DefaultRequestHeaders.Add("ApiKey", appSettings.ManageApiKey);
        });

        services.AddScoped<ManageAPI>(sp =>
        {
            var httpClient = sp.GetRequiredService<HttpClient>();
            httpClient.DefaultRequestHeaders.Add("ApiKey", appSettings.ManageApiKey);
            return new ManageAPI(appSettings.ManageApiBaseUrl, httpClient);
        });


        // ==== STORAGE ACCOUNT =============================================
        ArgumentException.ThrowIfNullOrWhiteSpace(appSettings.FromStorageQueueName, "FromStorageQueueName");
        ArgumentException.ThrowIfNullOrWhiteSpace(appSettings.ToStorageQueueName, "ToStorageQueueName");
        services.AddSingleton(provider =>
        {
            return new Dictionary<string, QueueClient>
            {
                { "from-manage", new QueueClient(appSettings.StorageQueueConnectionString, appSettings.FromStorageQueueName) },
                { "to-manage", new QueueClient(appSettings.StorageQueueConnectionString, appSettings.ToStorageQueueName) }
            };
        });

        services.AddSingleton(provider =>
        {
            return new BlobServiceClient(appSettings.StorageQueueConnectionString);
        });


        // ==== DB CONTEXT =================================================
        bool enableDetailedDbLogging = Convert.ToBoolean(Environment.GetEnvironmentVariable("EnableDetailedDbLogging") ?? "false");

        ArgumentException.ThrowIfNullOrWhiteSpace(appSettings.SqlConnectionString, "SqlConnectionString");
        services.AddDbContextFactory<ManageDbContext>(options =>
        {
            var dbOptionsBuilder = options.UseSqlServer(appSettings.SqlConnectionString);
            if (enableDetailedDbLogging)
            {
                dbOptionsBuilder
                    .EnableDetailedErrors(true)
                    .EnableSensitiveDataLogging(true)
                    .LogTo(s => System.Diagnostics.Debug.WriteLine(s));
            }
        });

        // ==== INTERFACES =================================================
        services.AddScoped<IColorManageIntegrator, ColorManageIntegrator>();
        services.AddScoped<IProductManageIntegrator, ProductManageIntegrator>();
        services.AddScoped<IProductModelManageIntegrator, ProductModelManageIntegrator>();
        services.AddScoped<IVendorManageIntegrator, VendorManageIntegrator>();
        services.AddScoped<ICountryService, CountryService>();
        services.AddScoped<ICategoryMappingService, CategoryMappingService>();
        services.AddScoped<ICategoryMappingRepository, CategoryMappingRepository>();
        services.AddScoped<IInventoryCountryRepository, InventoryCountryRepository>();
        services.AddScoped<IInventoryCountryService, InventoryCountryService>();

        services.AddScoped<FromManageEventProcessor>();
        services.AddScoped<ToManageEventProcessor>();

        services.AddScoped<BatteryEventHandler>();
        //services.AddScoped<BatteryFromEventHandler>();  <-- No EventHub event.  Must be manually aligned.
        services.AddScoped<BatteryGetByQueryHandler>();

        services.AddScoped<ClinicEventHandler>();
        services.AddScoped<ClinicFromEventHandler>();
        // services.AddScoped<ClinicToEventHandler>();  <-- Update to Manage not in scope.
        services.AddScoped<ClinicGetByQueryHandler>();
        services.AddScoped<IEventHubEntityAdapter<ClinicEventHubEvent, Clinic>, Manage.Models.Clinics.ClinicAdapter>();

        services.AddScoped<ColorEventHandler>();
        services.AddScoped<ColorFromEventHandler>();
        services.AddScoped<ColorGetByQueryHandler>();
        services.AddScoped<IManageRequestAdapter<DictionaryRequest, Color>, Manage.Models.Colors.ColorAdapter>();
        services.AddScoped<IManageResponseAdapter<DictionaryResponse, Color>, Manage.Models.Colors.ColorAdapter>();

        services.AddScoped<ManufacturerEventHandler>();
        services.AddScoped<ManufacturerFromEventHandler>();
        // services.AddScoped<ManufacturerToEventHandler>();  <-- Update to Manage not in scope (Manage requires more detail than tracked in BC).
        services.AddScoped<ManufacturerGetByQueryHandler>();
        services.AddScoped<IEventHubEntityAdapter<ManufacturerEventHubEvent, Manufacturer>, Manage.Models.Manufacturers.ManufacturerAdapter>();

        services.AddScoped<PatientEventHandler>();
        services.AddScoped<PatientFromEventHandler>();
        // services.AddScoped<PatientToEventHandler>();  <-- Update to Manage not in scope.
        services.AddScoped<PatientGetByQueryHandler>();
        services.AddScoped<IEventHubEntityAdapter<PatientEventHubEvent, Patient>, Manage.Models.Patients.PatientAdapter>();

        services.AddScoped<PayorEventHandler>();
        services.AddScoped<PayorFromEventHandler>();
        // services.AddScoped<PayorToEventHandler>();  <-- Update to Manage not in scope.
        services.AddScoped<PayorGetByQueryHandler>();
        services.AddScoped<IEventHubEntityAdapter<PayorEventHubEvent, Payor>, Manage.Models.Payors.PayorAdapter>();

        services.AddScoped<ProductEventHandler>();
        services.AddScoped<ProductFromEventHandler>();
        services.AddScoped<ProductToEventHandler>();
        services.AddScoped<ProductGetByQueryHandler>();
        services.AddScoped<IProductManageIntegrator, ProductManageIntegrator>();
        services.AddScoped<ProductManageIntegrator>();
        services.AddScoped<IManageRequestAdapter<ProductRequest, Product>, Manage.Models.Products.ProductAdapter>();
        services.AddScoped<IManageResponseAdapter<ProductResponse, Product>, Manage.Models.Products.ProductAdapter>();
        services.AddScoped<IEventHubEntityAdapter<ProductEventHubEvent, Product>, Manage.Models.Products.ProductAdapter>();

        services.AddScoped<PurchaseOrderEventHandler>();
        services.AddScoped<PurchaseReceiptEventHandler>();

        services.AddScoped<VendorEventHandler>();
        services.AddScoped<VendorFromEventHandler>();
        services.AddScoped<VendorToEventHandler>();
        services.AddScoped<VendorGetByQueryHandler>();
        services.AddScoped<IEventHubEntityAdapter<VendorEventHubEvent, Vendor>, Manage.Models.Vendors.VendorAdapter>();
        services.AddScoped<IManageRequestAdapter<AuSupplierRequest, Vendor>, Manage.Models.Vendors.VendorAdapter>();
        services.AddScoped<IManageResponseAdapter<AuSupplierResponse, Vendor>, Manage.Models.Vendors.VendorAdapter>();

        return services;
    }
}

