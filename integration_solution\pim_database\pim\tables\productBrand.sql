﻿CREATE TABLE [pim].[ProductBrand]
(
    [Id]                    UNIQUEIDENTIFIER CONSTRAINT [DF_ProductBrand_id] DEFAULT NEWID() NOT NULL,
    [ProductId]             UNIQUEIDENTIFIER NOT NULL,
    [BrandId]               UNIQUEIDENTIFIER NOT NULL,
    [SystemCreatedOn]       DATETIME2 CONSTRAINT [DF_ProductBrand_SystemCreatedOn] DEFAULT (sysutcdatetime()) NOT NULL,
    [SystemModifiedOn]      DATETIME2 CONSTRAINT [DF_ProductBrand_SystemModifiedOn] DEFAULT (sysutcdatetime()) NOT NULL,
    CONSTRAINT              [PK_ProductBrand_Id] PRIMARY KEY CLUSTERED ([Id] ASC)
)
GO

CREATE UNIQUE INDEX IX_ProductBrand_ProductId
ON [pim].[ProductBrand] ([ProductId], [BrandId])
GO