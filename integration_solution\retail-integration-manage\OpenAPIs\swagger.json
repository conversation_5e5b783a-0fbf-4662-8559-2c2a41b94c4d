﻿{
  "openapi": "3.0.1",
  "info": {
    "title": "Auditdata Manage Public Api v2.0",
    "description": "Swagger documentation for Manage Public Api",
    "version": "2.0"
  },
  "paths": {
    "/api/v2/edi/gnresound/status": {
      "post": {
        "tags": [
          "Edi"
        ],
        "summary": "Receive GN Resound Edi Order status update.",
        "requestBody": {
          "description": "Request payload.",
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/ReceiveGnEdiStatus"
              }
            },
            "application/xml": {
              "schema": {
                "$ref": "#/components/schemas/ReceiveGnEdiStatus"
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/GnEdiResponse"
                }
              },
              "application/xml": {
                "schema": {
                  "$ref": "#/components/schemas/GnEdiResponse"
                }
              }
            }
          }
        },
        "security": [
          {
            "ApiKey": [],
            "EdiScheme": []
          }
        ]
      }
    },
    "/api/v2/edi/gnresound/shipment": {
      "post": {
        "tags": [
          "Edi"
        ],
        "summary": "Receive GN Resound Edi Order ASN.",
        "requestBody": {
          "description": "Request payload.",
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/ReceiveGnAsn"
              }
            },
            "application/xml": {
              "schema": {
                "$ref": "#/components/schemas/ReceiveGnAsn"
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/GnEdiResponse"
                }
              },
              "application/xml": {
                "schema": {
                  "$ref": "#/components/schemas/GnEdiResponse"
                }
              }
            }
          }
        },
        "security": [
          {
            "ApiKey": [],
            "EdiScheme": []
          }
        ]
      }
    },
    "/api/v2/inventory/attributes": {
      "get": {
        "tags": [
          "Inventory"
        ],
        "summary": "Get a list of all available inventory attributes.",
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/AttributeResponseIEnumerableListResponse"
                }
              }
            }
          }
        },
        "security": [
          {
            "ApiKey": [],
            "EdiScheme": []
          }
        ]
      },
      "post": {
        "tags": [
          "Inventory"
        ],
        "summary": "Creates new inventory attribute.",
        "requestBody": {
          "description": "Attribute payload.",
          "content": {
            "application/merge-patch+json": {
              "schema": {
                "$ref": "#/components/schemas/AttributeRequest"
              }
            },
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/AttributeRequest"
              }
            },
            "text/json": {
              "schema": {
                "$ref": "#/components/schemas/AttributeRequest"
              }
            },
            "application/*+json": {
              "schema": {
                "$ref": "#/components/schemas/AttributeRequest"
              }
            }
          }
        },
        "responses": {
          "201": {
            "description": "Created",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/CreatedResponse"
                }
              }
            }
          }
        },
        "security": [
          {
            "ApiKey": [],
            "EdiScheme": []
          }
        ]
      }
    },
    "/api/v2/inventory/attributes/{id}": {
      "get": {
        "tags": [
          "Inventory"
        ],
        "summary": "Gets an inventory attribute by id.",
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "description": "Unique identifier of an attribute.",
            "required": true,
            "schema": {
              "type": "string",
              "format": "uuid"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/AttributeResponse"
                }
              }
            }
          },
          "404": {
            "description": "Not Found",
            "content": {
              "application/json": {
                "schema": {
                  "oneOf": [
                    {
                      "$ref": "#/components/schemas/ProblemDetails"
                    },
                    {
                      "$ref": "#/components/schemas/HttpValidationProblemDetails"
                    }
                  ]
                }
              }
            }
          }
        },
        "security": [
          {
            "ApiKey": [],
            "EdiScheme": []
          }
        ]
      },
      "put": {
        "tags": [
          "Inventory"
        ],
        "summary": "Updates an existing inventory attribute by Id.",
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "description": "Unique identifier of an attribute.",
            "required": true,
            "schema": {
              "type": "string",
              "format": "uuid"
            }
          }
        ],
        "requestBody": {
          "description": "Attribute payload.",
          "content": {
            "application/merge-patch+json": {
              "schema": {
                "$ref": "#/components/schemas/AttributeRequest"
              }
            },
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/AttributeRequest"
              }
            },
            "text/json": {
              "schema": {
                "$ref": "#/components/schemas/AttributeRequest"
              }
            },
            "application/*+json": {
              "schema": {
                "$ref": "#/components/schemas/AttributeRequest"
              }
            }
          }
        },
        "responses": {
          "201": {
            "description": "Created",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/CreatedResponse"
                }
              }
            }
          }
        },
        "security": [
          {
            "ApiKey": [],
            "EdiScheme": []
          }
        ]
      }
    },
    "/api/v2/inventory/colors": {
      "get": {
        "tags": [
          "Inventory"
        ],
        "summary": "Get a list of all available inventory colors.",
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/DictionaryResponseIEnumerableListResponse"
                }
              }
            }
          }
        },
        "security": [
          {
            "ApiKey": [],
            "EdiScheme": []
          }
        ]
      },
      "post": {
        "tags": [
          "Inventory"
        ],
        "summary": "Creates a new inventory color.",
        "requestBody": {
          "description": "New color payload.",
          "content": {
            "application/merge-patch+json": {
              "schema": {
                "$ref": "#/components/schemas/DictionaryRequest"
              }
            },
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/DictionaryRequest"
              }
            },
            "text/json": {
              "schema": {
                "$ref": "#/components/schemas/DictionaryRequest"
              }
            },
            "application/*+json": {
              "schema": {
                "$ref": "#/components/schemas/DictionaryRequest"
              }
            },
            "application/xml": {
              "schema": {
                "$ref": "#/components/schemas/DictionaryRequest"
              }
            },
            "text/xml": {
              "schema": {
                "$ref": "#/components/schemas/DictionaryRequest"
              }
            },
            "application/*+xml": {
              "schema": {
                "$ref": "#/components/schemas/DictionaryRequest"
              }
            }
          }
        },
        "responses": {
          "201": {
            "description": "Created",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/CreatedResponse"
                }
              }
            }
          },
          "400": {
            "description": "Bad Request",
            "content": {
              "application/json": {
                "schema": {
                  "oneOf": [
                    {
                      "$ref": "#/components/schemas/ProblemDetails"
                    },
                    {
                      "$ref": "#/components/schemas/HttpValidationProblemDetails"
                    }
                  ]
                }
              }
            }
          }
        },
        "security": [
          {
            "ApiKey": [],
            "EdiScheme": []
          }
        ]
      }
    },
    "/api/v2/inventory/colors/{id}": {
      "get": {
        "tags": [
          "Inventory"
        ],
        "summary": "Gets an inventory color by id.",
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "description": "Unique identifier of a color.",
            "required": true,
            "schema": {
              "type": "string",
              "format": "uuid"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "application/json": {
                "schema": {
                  "oneOf": [
                    {
                      "$ref": "#/components/schemas/DictionaryResponse"
                    },
                    {
                      "$ref": "#/components/schemas/CountryResponse"
                    }
                  ]
                }
              }
            }
          },
          "404": {
            "description": "Not Found",
            "content": {
              "application/json": {
                "schema": {
                  "oneOf": [
                    {
                      "$ref": "#/components/schemas/ProblemDetails"
                    },
                    {
                      "$ref": "#/components/schemas/HttpValidationProblemDetails"
                    }
                  ]
                }
              }
            }
          }
        },
        "security": [
          {
            "ApiKey": [],
            "EdiScheme": []
          }
        ]
      },
      "put": {
        "tags": [
          "Inventory"
        ],
        "summary": "Updates an existing inventory color by id.",
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "description": "Unique identifier of a color.",
            "required": true,
            "schema": {
              "type": "string",
              "format": "uuid"
            }
          }
        ],
        "requestBody": {
          "description": "Color payload.",
          "content": {
            "application/merge-patch+json": {
              "schema": {
                "$ref": "#/components/schemas/DictionaryRequest"
              }
            },
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/DictionaryRequest"
              }
            },
            "text/json": {
              "schema": {
                "$ref": "#/components/schemas/DictionaryRequest"
              }
            },
            "application/*+json": {
              "schema": {
                "$ref": "#/components/schemas/DictionaryRequest"
              }
            },
            "application/xml": {
              "schema": {
                "$ref": "#/components/schemas/DictionaryRequest"
              }
            },
            "text/xml": {
              "schema": {
                "$ref": "#/components/schemas/DictionaryRequest"
              }
            },
            "application/*+xml": {
              "schema": {
                "$ref": "#/components/schemas/DictionaryRequest"
              }
            }
          }
        },
        "responses": {
          "204": {
            "description": "No Content"
          },
          "400": {
            "description": "Bad Request",
            "content": {
              "application/json": {
                "schema": {
                  "oneOf": [
                    {
                      "$ref": "#/components/schemas/ProblemDetails"
                    },
                    {
                      "$ref": "#/components/schemas/HttpValidationProblemDetails"
                    }
                  ]
                }
              }
            }
          },
          "404": {
            "description": "Not Found",
            "content": {
              "application/json": {
                "schema": {
                  "oneOf": [
                    {
                      "$ref": "#/components/schemas/ProblemDetails"
                    },
                    {
                      "$ref": "#/components/schemas/HttpValidationProblemDetails"
                    }
                  ]
                }
              }
            }
          }
        },
        "security": [
          {
            "ApiKey": [],
            "EdiScheme": []
          }
        ]
      }
    },
    "/api/v2/inventory/battery-types": {
      "get": {
        "tags": [
          "Inventory"
        ],
        "summary": "Get a list of all available inventory battery types.",
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "$ref": "#/components/schemas/DictionaryResponseIEnumerableListResponse"
                }
              },
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/DictionaryResponseIEnumerableListResponse"
                }
              },
              "text/json": {
                "schema": {
                  "$ref": "#/components/schemas/DictionaryResponseIEnumerableListResponse"
                }
              }
            }
          }
        },
        "security": [
          {
            "ApiKey": [],
            "EdiScheme": []
          }
        ]
      }
    },
    "/api/v2/inventory/hearing-aid-types": {
      "get": {
        "tags": [
          "Inventory"
        ],
        "summary": "Get a list of all available inventory hearing aid types.",
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "$ref": "#/components/schemas/DictionaryResponseIEnumerableListResponse"
                }
              },
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/DictionaryResponseIEnumerableListResponse"
                }
              },
              "text/json": {
                "schema": {
                  "$ref": "#/components/schemas/DictionaryResponseIEnumerableListResponse"
                }
              }
            }
          }
        },
        "security": [
          {
            "ApiKey": [],
            "EdiScheme": []
          }
        ]
      }
    },
    "/api/v2/inventory/product-categories": {
      "get": {
        "tags": [
          "Inventory"
        ],
        "summary": "Get a list of all available inventory product categories.",
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "$ref": "#/components/schemas/ProductCategoryResponseIEnumerableListResponse"
                }
              },
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/ProductCategoryResponseIEnumerableListResponse"
                }
              },
              "text/json": {
                "schema": {
                  "$ref": "#/components/schemas/ProductCategoryResponseIEnumerableListResponse"
                }
              }
            }
          }
        },
        "security": [
          {
            "ApiKey": [],
            "EdiScheme": []
          }
        ]
      }
    },
    "/api/v2/inventory/countries": {
      "get": {
        "tags": [
          "Inventory"
        ],
        "summary": "Get a list of all available inventory countries. Used in Suppliers and Manufacturers.",
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "$ref": "#/components/schemas/CountryResponseIEnumerableListResponse"
                }
              },
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/CountryResponseIEnumerableListResponse"
                }
              },
              "text/json": {
                "schema": {
                  "$ref": "#/components/schemas/CountryResponseIEnumerableListResponse"
                }
              }
            }
          }
        },
        "security": [
          {
            "ApiKey": [],
            "EdiScheme": []
          }
        ]
      }
    },
    "/api/v2/inventory/manufacturers": {
      "get": {
        "tags": [
          "Inventory"
        ],
        "summary": "Get a paged list of all available inventory manufacturers.",
        "parameters": [
          {
            "name": "Page",
            "in": "query",
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          },
          {
            "name": "PerPage",
            "in": "query",
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/ManufacturerResponsePagingResponse"
                }
              }
            }
          }
        },
        "security": [
          {
            "ApiKey": [],
            "EdiScheme": []
          }
        ]
      },
      "post": {
        "tags": [
          "Inventory"
        ],
        "summary": "Creates a new inventory Manufacturer.",
        "requestBody": {
          "description": "Manufacturer payload.",
          "content": {
            "application/merge-patch+json": {
              "schema": {
                "oneOf": [
                  {
                    "$ref": "#/components/schemas/AuManufacturerRequest"
                  },
                  {
                    "$ref": "#/components/schemas/UsManufacturerRequest"
                  },
                  {
                    "$ref": "#/components/schemas/UkManufacturerRequest"
                  },
                  {
                    "$ref": "#/components/schemas/NzManufacturerRequest"
                  },
                  {
                    "$ref": "#/components/schemas/RoiManufacturerRequest"
                  }
                ]
              }
            },
            "application/json": {
              "schema": {
                "oneOf": [
                  {
                    "$ref": "#/components/schemas/AuManufacturerRequest"
                  },
                  {
                    "$ref": "#/components/schemas/UsManufacturerRequest"
                  },
                  {
                    "$ref": "#/components/schemas/UkManufacturerRequest"
                  },
                  {
                    "$ref": "#/components/schemas/NzManufacturerRequest"
                  },
                  {
                    "$ref": "#/components/schemas/RoiManufacturerRequest"
                  }
                ]
              }
            },
            "text/json": {
              "schema": {
                "oneOf": [
                  {
                    "$ref": "#/components/schemas/AuManufacturerRequest"
                  },
                  {
                    "$ref": "#/components/schemas/UsManufacturerRequest"
                  },
                  {
                    "$ref": "#/components/schemas/UkManufacturerRequest"
                  },
                  {
                    "$ref": "#/components/schemas/NzManufacturerRequest"
                  },
                  {
                    "$ref": "#/components/schemas/RoiManufacturerRequest"
                  }
                ]
              }
            },
            "application/*+json": {
              "schema": {
                "oneOf": [
                  {
                    "$ref": "#/components/schemas/AuManufacturerRequest"
                  },
                  {
                    "$ref": "#/components/schemas/UsManufacturerRequest"
                  },
                  {
                    "$ref": "#/components/schemas/UkManufacturerRequest"
                  },
                  {
                    "$ref": "#/components/schemas/NzManufacturerRequest"
                  },
                  {
                    "$ref": "#/components/schemas/RoiManufacturerRequest"
                  }
                ]
              }
            },
            "application/xml": {
              "schema": {
                "oneOf": [
                  {
                    "$ref": "#/components/schemas/AuManufacturerRequest"
                  },
                  {
                    "$ref": "#/components/schemas/UsManufacturerRequest"
                  },
                  {
                    "$ref": "#/components/schemas/UkManufacturerRequest"
                  },
                  {
                    "$ref": "#/components/schemas/NzManufacturerRequest"
                  },
                  {
                    "$ref": "#/components/schemas/RoiManufacturerRequest"
                  }
                ]
              }
            },
            "text/xml": {
              "schema": {
                "oneOf": [
                  {
                    "$ref": "#/components/schemas/AuManufacturerRequest"
                  },
                  {
                    "$ref": "#/components/schemas/UsManufacturerRequest"
                  },
                  {
                    "$ref": "#/components/schemas/UkManufacturerRequest"
                  },
                  {
                    "$ref": "#/components/schemas/NzManufacturerRequest"
                  },
                  {
                    "$ref": "#/components/schemas/RoiManufacturerRequest"
                  }
                ]
              }
            },
            "application/*+xml": {
              "schema": {
                "oneOf": [
                  {
                    "$ref": "#/components/schemas/AuManufacturerRequest"
                  },
                  {
                    "$ref": "#/components/schemas/UsManufacturerRequest"
                  },
                  {
                    "$ref": "#/components/schemas/UkManufacturerRequest"
                  },
                  {
                    "$ref": "#/components/schemas/NzManufacturerRequest"
                  },
                  {
                    "$ref": "#/components/schemas/RoiManufacturerRequest"
                  }
                ]
              }
            }
          }
        },
        "responses": {
          "201": {
            "description": "Created",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/CreatedResponse"
                }
              }
            }
          },
          "400": {
            "description": "Bad Request",
            "content": {
              "application/json": {
                "schema": {
                  "oneOf": [
                    {
                      "$ref": "#/components/schemas/ProblemDetails"
                    },
                    {
                      "$ref": "#/components/schemas/HttpValidationProblemDetails"
                    }
                  ]
                }
              }
            }
          }
        },
        "security": [
          {
            "ApiKey": [],
            "EdiScheme": []
          }
        ]
      }
    },
    "/api/v2/inventory/manufacturers/{id}": {
      "get": {
        "tags": [
          "Inventory"
        ],
        "summary": "Get an inventory manufacturer by id.",
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "description": "Unique identifier of Manufacturer.",
            "required": true,
            "schema": {
              "type": "string",
              "format": "uuid"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "application/json": {
                "schema": {
                  "oneOf": [
                    {
                      "$ref": "#/components/schemas/AuManufacturerResponse"
                    },
                    {
                      "$ref": "#/components/schemas/UsManufacturerResponse"
                    },
                    {
                      "$ref": "#/components/schemas/UkManufacturerResponse"
                    },
                    {
                      "$ref": "#/components/schemas/NzManufacturerResponse"
                    },
                    {
                      "$ref": "#/components/schemas/RoiManufacturerResponse"
                    }
                  ]
                }
              }
            }
          },
          "404": {
            "description": "Not Found",
            "content": {
              "application/json": {
                "schema": {
                  "oneOf": [
                    {
                      "$ref": "#/components/schemas/ProblemDetails"
                    },
                    {
                      "$ref": "#/components/schemas/HttpValidationProblemDetails"
                    }
                  ]
                }
              }
            }
          }
        },
        "security": [
          {
            "ApiKey": [],
            "EdiScheme": []
          }
        ]
      },
      "put": {
        "tags": [
          "Inventory"
        ],
        "summary": "Updates an existing inventory Manufacturer by id.",
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "description": "Unique identifier of a manufacturer.",
            "required": true,
            "schema": {
              "type": "string",
              "format": "uuid"
            }
          }
        ],
        "requestBody": {
          "description": "Manufacturer payload.",
          "content": {
            "application/merge-patch+json": {
              "schema": {
                "oneOf": [
                  {
                    "$ref": "#/components/schemas/AuManufacturerRequest"
                  },
                  {
                    "$ref": "#/components/schemas/UsManufacturerRequest"
                  },
                  {
                    "$ref": "#/components/schemas/UkManufacturerRequest"
                  },
                  {
                    "$ref": "#/components/schemas/NzManufacturerRequest"
                  },
                  {
                    "$ref": "#/components/schemas/RoiManufacturerRequest"
                  }
                ]
              }
            },
            "application/json": {
              "schema": {
                "oneOf": [
                  {
                    "$ref": "#/components/schemas/AuManufacturerRequest"
                  },
                  {
                    "$ref": "#/components/schemas/UsManufacturerRequest"
                  },
                  {
                    "$ref": "#/components/schemas/UkManufacturerRequest"
                  },
                  {
                    "$ref": "#/components/schemas/NzManufacturerRequest"
                  },
                  {
                    "$ref": "#/components/schemas/RoiManufacturerRequest"
                  }
                ]
              }
            },
            "text/json": {
              "schema": {
                "oneOf": [
                  {
                    "$ref": "#/components/schemas/AuManufacturerRequest"
                  },
                  {
                    "$ref": "#/components/schemas/UsManufacturerRequest"
                  },
                  {
                    "$ref": "#/components/schemas/UkManufacturerRequest"
                  },
                  {
                    "$ref": "#/components/schemas/NzManufacturerRequest"
                  },
                  {
                    "$ref": "#/components/schemas/RoiManufacturerRequest"
                  }
                ]
              }
            },
            "application/*+json": {
              "schema": {
                "oneOf": [
                  {
                    "$ref": "#/components/schemas/AuManufacturerRequest"
                  },
                  {
                    "$ref": "#/components/schemas/UsManufacturerRequest"
                  },
                  {
                    "$ref": "#/components/schemas/UkManufacturerRequest"
                  },
                  {
                    "$ref": "#/components/schemas/NzManufacturerRequest"
                  },
                  {
                    "$ref": "#/components/schemas/RoiManufacturerRequest"
                  }
                ]
              }
            },
            "application/xml": {
              "schema": {
                "oneOf": [
                  {
                    "$ref": "#/components/schemas/AuManufacturerRequest"
                  },
                  {
                    "$ref": "#/components/schemas/UsManufacturerRequest"
                  },
                  {
                    "$ref": "#/components/schemas/UkManufacturerRequest"
                  },
                  {
                    "$ref": "#/components/schemas/NzManufacturerRequest"
                  },
                  {
                    "$ref": "#/components/schemas/RoiManufacturerRequest"
                  }
                ]
              }
            },
            "text/xml": {
              "schema": {
                "oneOf": [
                  {
                    "$ref": "#/components/schemas/AuManufacturerRequest"
                  },
                  {
                    "$ref": "#/components/schemas/UsManufacturerRequest"
                  },
                  {
                    "$ref": "#/components/schemas/UkManufacturerRequest"
                  },
                  {
                    "$ref": "#/components/schemas/NzManufacturerRequest"
                  },
                  {
                    "$ref": "#/components/schemas/RoiManufacturerRequest"
                  }
                ]
              }
            },
            "application/*+xml": {
              "schema": {
                "oneOf": [
                  {
                    "$ref": "#/components/schemas/AuManufacturerRequest"
                  },
                  {
                    "$ref": "#/components/schemas/UsManufacturerRequest"
                  },
                  {
                    "$ref": "#/components/schemas/UkManufacturerRequest"
                  },
                  {
                    "$ref": "#/components/schemas/NzManufacturerRequest"
                  },
                  {
                    "$ref": "#/components/schemas/RoiManufacturerRequest"
                  }
                ]
              }
            }
          }
        },
        "responses": {
          "204": {
            "description": "No Content"
          },
          "400": {
            "description": "Bad Request",
            "content": {
              "application/json": {
                "schema": {
                  "oneOf": [
                    {
                      "$ref": "#/components/schemas/ProblemDetails"
                    },
                    {
                      "$ref": "#/components/schemas/HttpValidationProblemDetails"
                    }
                  ]
                }
              }
            }
          },
          "404": {
            "description": "Not Found",
            "content": {
              "application/json": {
                "schema": {
                  "oneOf": [
                    {
                      "$ref": "#/components/schemas/ProblemDetails"
                    },
                    {
                      "$ref": "#/components/schemas/HttpValidationProblemDetails"
                    }
                  ]
                }
              }
            }
          }
        },
        "security": [
          {
            "ApiKey": [],
            "EdiScheme": []
          }
        ]
      }
    },
    "/api/v2/inventory/products": {
      "get": {
        "tags": [
          "Inventory"
        ],
        "summary": "Returns a paged list of inventory products.",
        "parameters": [
          {
            "name": "Page",
            "in": "query",
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          },
          {
            "name": "PerPage",
            "in": "query",
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/ProductListResponsePagingResponse"
                }
              }
            }
          }
        },
        "security": [
          {
            "ApiKey": [],
            "EdiScheme": []
          }
        ]
      },
      "post": {
        "tags": [
          "Inventory"
        ],
        "summary": "Creates a new product.",
        "requestBody": {
          "description": "New product payload.",
          "content": {
            "application/merge-patch+json": {
              "schema": {
                "oneOf": [
                  {
                    "$ref": "#/components/schemas/ProductRequest"
                  },
                  {
                    "$ref": "#/components/schemas/UKProductRequest"
                  },
                  {
                    "$ref": "#/components/schemas/ROIProductRequest"
                  },
                  {
                    "$ref": "#/components/schemas/AUProductRequest"
                  },
                  {
                    "$ref": "#/components/schemas/NZProductRequest"
                  },
                  {
                    "$ref": "#/components/schemas/USProductRequest"
                  }
                ]
              }
            },
            "application/json": {
              "schema": {
                "oneOf": [
                  {
                    "$ref": "#/components/schemas/ProductRequest"
                  },
                  {
                    "$ref": "#/components/schemas/UKProductRequest"
                  },
                  {
                    "$ref": "#/components/schemas/ROIProductRequest"
                  },
                  {
                    "$ref": "#/components/schemas/AUProductRequest"
                  },
                  {
                    "$ref": "#/components/schemas/NZProductRequest"
                  },
                  {
                    "$ref": "#/components/schemas/USProductRequest"
                  }
                ]
              }
            },
            "text/json": {
              "schema": {
                "oneOf": [
                  {
                    "$ref": "#/components/schemas/ProductRequest"
                  },
                  {
                    "$ref": "#/components/schemas/UKProductRequest"
                  },
                  {
                    "$ref": "#/components/schemas/ROIProductRequest"
                  },
                  {
                    "$ref": "#/components/schemas/AUProductRequest"
                  },
                  {
                    "$ref": "#/components/schemas/NZProductRequest"
                  },
                  {
                    "$ref": "#/components/schemas/USProductRequest"
                  }
                ]
              }
            },
            "application/*+json": {
              "schema": {
                "oneOf": [
                  {
                    "$ref": "#/components/schemas/ProductRequest"
                  },
                  {
                    "$ref": "#/components/schemas/UKProductRequest"
                  },
                  {
                    "$ref": "#/components/schemas/ROIProductRequest"
                  },
                  {
                    "$ref": "#/components/schemas/AUProductRequest"
                  },
                  {
                    "$ref": "#/components/schemas/NZProductRequest"
                  },
                  {
                    "$ref": "#/components/schemas/USProductRequest"
                  }
                ]
              }
            }
          }
        },
        "responses": {
          "201": {
            "description": "Created",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/CreatedResponse"
                }
              }
            }
          }
        },
        "security": [
          {
            "ApiKey": [],
            "EdiScheme": []
          }
        ]
      }
    },
    "/api/v2/inventory/products/{id}": {
      "get": {
        "tags": [
          "Inventory"
        ],
        "summary": "Returns single product information by id.",
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "description": "Unique identifier of a product.",
            "required": true,
            "schema": {
              "type": "string",
              "format": "uuid"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/ProductResponse"
                }
              }
            }
          },
          "404": {
            "description": "Not Found",
            "content": {
              "application/json": {
                "schema": {
                  "oneOf": [
                    {
                      "$ref": "#/components/schemas/ProblemDetails"
                    },
                    {
                      "$ref": "#/components/schemas/HttpValidationProblemDetails"
                    }
                  ]
                }
              }
            }
          }
        },
        "security": [
          {
            "ApiKey": [],
            "EdiScheme": []
          }
        ]
      },
      "put": {
        "tags": [
          "Inventory"
        ],
        "summary": "Updates an existing product by id.",
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "description": "Product unique identifier.",
            "required": true,
            "schema": {
              "type": "string",
              "format": "uuid"
            }
          }
        ],
        "requestBody": {
          "description": "Product payload.",
          "content": {
            "application/merge-patch+json": {
              "schema": {
                "oneOf": [
                  {
                    "$ref": "#/components/schemas/ProductRequest"
                  },
                  {
                    "$ref": "#/components/schemas/UKProductRequest"
                  },
                  {
                    "$ref": "#/components/schemas/ROIProductRequest"
                  },
                  {
                    "$ref": "#/components/schemas/AUProductRequest"
                  },
                  {
                    "$ref": "#/components/schemas/NZProductRequest"
                  },
                  {
                    "$ref": "#/components/schemas/USProductRequest"
                  }
                ]
              }
            },
            "application/json": {
              "schema": {
                "oneOf": [
                  {
                    "$ref": "#/components/schemas/ProductRequest"
                  },
                  {
                    "$ref": "#/components/schemas/UKProductRequest"
                  },
                  {
                    "$ref": "#/components/schemas/ROIProductRequest"
                  },
                  {
                    "$ref": "#/components/schemas/AUProductRequest"
                  },
                  {
                    "$ref": "#/components/schemas/NZProductRequest"
                  },
                  {
                    "$ref": "#/components/schemas/USProductRequest"
                  }
                ]
              }
            },
            "text/json": {
              "schema": {
                "oneOf": [
                  {
                    "$ref": "#/components/schemas/ProductRequest"
                  },
                  {
                    "$ref": "#/components/schemas/UKProductRequest"
                  },
                  {
                    "$ref": "#/components/schemas/ROIProductRequest"
                  },
                  {
                    "$ref": "#/components/schemas/AUProductRequest"
                  },
                  {
                    "$ref": "#/components/schemas/NZProductRequest"
                  },
                  {
                    "$ref": "#/components/schemas/USProductRequest"
                  }
                ]
              }
            },
            "application/*+json": {
              "schema": {
                "oneOf": [
                  {
                    "$ref": "#/components/schemas/ProductRequest"
                  },
                  {
                    "$ref": "#/components/schemas/UKProductRequest"
                  },
                  {
                    "$ref": "#/components/schemas/ROIProductRequest"
                  },
                  {
                    "$ref": "#/components/schemas/AUProductRequest"
                  },
                  {
                    "$ref": "#/components/schemas/NZProductRequest"
                  },
                  {
                    "$ref": "#/components/schemas/USProductRequest"
                  }
                ]
              }
            }
          }
        },
        "responses": {
          "204": {
            "description": "No Content"
          },
          "404": {
            "description": "Not Found",
            "content": {
              "application/json": {
                "schema": {
                  "oneOf": [
                    {
                      "$ref": "#/components/schemas/ProblemDetails"
                    },
                    {
                      "$ref": "#/components/schemas/HttpValidationProblemDetails"
                    }
                  ]
                }
              }
            }
          }
        },
        "security": [
          {
            "ApiKey": [],
            "EdiScheme": []
          }
        ]
      },
      "patch": {
        "tags": [
          "Inventory"
        ],
        "summary": "Partially updates inventory Product.",
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "description": "Product unique identifier.",
            "required": true,
            "schema": {
              "type": "string",
              "format": "uuid"
            }
          }
        ],
        "requestBody": {
          "description": "Patch request payload.",
          "content": {
            "application/merge-patch+json": {
              "schema": {
                "$ref": "#/components/schemas/PatchProductRequest"
              }
            }
          }
        },
        "responses": {
          "204": {
            "description": "No Content"
          },
          "404": {
            "description": "Not Found",
            "content": {
              "application/json": {
                "schema": {
                  "oneOf": [
                    {
                      "$ref": "#/components/schemas/ProblemDetails"
                    },
                    {
                      "$ref": "#/components/schemas/HttpValidationProblemDetails"
                    }
                  ]
                }
              }
            }
          }
        },
        "security": [
          {
            "ApiKey": [],
            "EdiScheme": []
          }
        ]
      }
    },
    "/api/v2/inventory/products/{id}/search-suggested": {
      "get": {
        "tags": [
          "Inventory"
        ],
        "summary": "Returns a paged list of suggested products.",
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "description": "Product unique identifier.",
            "required": true,
            "schema": {
              "type": "string",
              "format": "uuid"
            }
          },
          {
            "name": "SearchText",
            "in": "query",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "CategoryId",
            "in": "query",
            "schema": {
              "type": "string",
              "format": "uuid"
            }
          },
          {
            "name": "CategoryCode",
            "in": "query",
            "schema": {
              "$ref": "#/components/schemas/ProductCategoryCodeRequest"
            }
          },
          {
            "name": "ManufacturerId",
            "in": "query",
            "schema": {
              "type": "string",
              "format": "uuid"
            }
          },
          {
            "name": "IsActive",
            "in": "query",
            "schema": {
              "type": "boolean"
            }
          },
          {
            "name": "IsSerialized",
            "in": "query",
            "schema": {
              "type": "boolean"
            }
          },
          {
            "name": "IsNhs",
            "in": "query",
            "schema": {
              "type": "boolean"
            }
          },
          {
            "name": "Page",
            "in": "query",
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          },
          {
            "name": "PerPage",
            "in": "query",
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/ProductListResponsePagingResponse"
                }
              }
            }
          }
        },
        "security": [
          {
            "ApiKey": [],
            "EdiScheme": []
          }
        ]
      }
    },
    "/api/v2/inventory/products/search": {
      "get": {
        "tags": [
          "Inventory"
        ],
        "summary": "Returns a paged list of products.",
        "parameters": [
          {
            "name": "SearchText",
            "in": "query",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "CategoryId",
            "in": "query",
            "schema": {
              "type": "string",
              "format": "uuid"
            }
          },
          {
            "name": "CategoryCode",
            "in": "query",
            "schema": {
              "$ref": "#/components/schemas/ProductCategoryCodeRequest"
            }
          },
          {
            "name": "ManufacturerId",
            "in": "query",
            "schema": {
              "type": "string",
              "format": "uuid"
            }
          },
          {
            "name": "IsActive",
            "in": "query",
            "schema": {
              "type": "boolean"
            }
          },
          {
            "name": "IsSerialized",
            "in": "query",
            "schema": {
              "type": "boolean"
            }
          },
          {
            "name": "IsNhs",
            "in": "query",
            "schema": {
              "type": "boolean"
            }
          },
          {
            "name": "Page",
            "in": "query",
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          },
          {
            "name": "PerPage",
            "in": "query",
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/ProductListResponsePagingResponse"
                }
              }
            }
          }
        },
        "security": [
          {
            "ApiKey": [],
            "EdiScheme": []
          }
        ]
      }
    },
    "/api/v2/inventory/products/{productId}/skus": {
      "put": {
        "tags": [
          "Inventory"
        ],
        "summary": "Assign SKUs to the product.",
        "parameters": [
          {
            "name": "productId",
            "in": "path",
            "description": "Product ID.",
            "required": true,
            "schema": {
              "type": "string",
              "format": "uuid"
            }
          }
        ],
        "requestBody": {
          "description": "SKUs payload.",
          "content": {
            "application/merge-patch+json": {
              "schema": {
                "type": "array",
                "items": {
                  "$ref": "#/components/schemas/AssignSkuRequest"
                }
              }
            },
            "application/json": {
              "schema": {
                "type": "array",
                "items": {
                  "$ref": "#/components/schemas/AssignSkuRequest"
                }
              }
            },
            "text/json": {
              "schema": {
                "type": "array",
                "items": {
                  "$ref": "#/components/schemas/AssignSkuRequest"
                }
              }
            },
            "application/*+json": {
              "schema": {
                "type": "array",
                "items": {
                  "$ref": "#/components/schemas/AssignSkuRequest"
                }
              }
            },
            "application/xml": {
              "schema": {
                "type": "array",
                "items": {
                  "$ref": "#/components/schemas/AssignSkuRequest"
                }
              }
            },
            "text/xml": {
              "schema": {
                "type": "array",
                "items": {
                  "$ref": "#/components/schemas/AssignSkuRequest"
                }
              }
            },
            "application/*+xml": {
              "schema": {
                "type": "array",
                "items": {
                  "$ref": "#/components/schemas/AssignSkuRequest"
                }
              }
            }
          }
        },
        "responses": {
          "204": {
            "description": "No Content",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/CreatedResponse"
                }
              }
            }
          },
          "400": {
            "description": "Bad Request",
            "content": {
              "application/json": {
                "schema": {
                  "oneOf": [
                    {
                      "$ref": "#/components/schemas/ProblemDetails"
                    },
                    {
                      "$ref": "#/components/schemas/HttpValidationProblemDetails"
                    }
                  ]
                }
              }
            }
          },
          "404": {
            "description": "Not Found",
            "content": {
              "application/json": {
                "schema": {
                  "oneOf": [
                    {
                      "$ref": "#/components/schemas/ProblemDetails"
                    },
                    {
                      "$ref": "#/components/schemas/HttpValidationProblemDetails"
                    }
                  ]
                }
              }
            }
          }
        },
        "security": [
          {
            "ApiKey": [],
            "EdiScheme": []
          }
        ]
      },
      "get": {
        "tags": [
          "Inventory"
        ],
        "summary": "Get a paged list of all available inventory SKUs for a product.",
        "parameters": [
          {
            "name": "productId",
            "in": "path",
            "description": "Product ID.",
            "required": true,
            "schema": {
              "type": "string",
              "format": "uuid"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "application/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/GetSkuResponse"
                  }
                }
              }
            }
          }
        },
        "security": [
          {
            "ApiKey": [],
            "EdiScheme": []
          }
        ]
      }
    },
    "/api/v2/inventory/products/{productId}/skus/configuration": {
      "post": {
        "tags": [
          "Inventory"
        ],
        "summary": "Set which of the product attributes to use for SKU definition.",
        "parameters": [
          {
            "name": "productId",
            "in": "path",
            "description": "Product ID.",
            "required": true,
            "schema": {
              "type": "string",
              "format": "uuid"
            }
          }
        ],
        "requestBody": {
          "description": "Setup SKU configs payload.",
          "content": {
            "application/merge-patch+json": {
              "schema": {
                "$ref": "#/components/schemas/AssignSkuConfigRequest"
              }
            },
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/AssignSkuConfigRequest"
              }
            },
            "text/json": {
              "schema": {
                "$ref": "#/components/schemas/AssignSkuConfigRequest"
              }
            },
            "application/*+json": {
              "schema": {
                "$ref": "#/components/schemas/AssignSkuConfigRequest"
              }
            },
            "application/xml": {
              "schema": {
                "$ref": "#/components/schemas/AssignSkuConfigRequest"
              }
            },
            "text/xml": {
              "schema": {
                "$ref": "#/components/schemas/AssignSkuConfigRequest"
              }
            },
            "application/*+xml": {
              "schema": {
                "$ref": "#/components/schemas/AssignSkuConfigRequest"
              }
            }
          }
        },
        "responses": {
          "204": {
            "description": "No Content"
          },
          "400": {
            "description": "Bad Request",
            "content": {
              "application/json": {
                "schema": {
                  "oneOf": [
                    {
                      "$ref": "#/components/schemas/ProblemDetails"
                    },
                    {
                      "$ref": "#/components/schemas/HttpValidationProblemDetails"
                    }
                  ]
                }
              }
            }
          },
          "404": {
            "description": "Not Found",
            "content": {
              "application/json": {
                "schema": {
                  "oneOf": [
                    {
                      "$ref": "#/components/schemas/ProblemDetails"
                    },
                    {
                      "$ref": "#/components/schemas/HttpValidationProblemDetails"
                    }
                  ]
                }
              }
            }
          }
        },
        "security": [
          {
            "ApiKey": [],
            "EdiScheme": []
          }
        ]
      },
      "delete": {
        "tags": [
          "Inventory"
        ],
        "summary": "Delete all SKUs configured for the product and product SKU configuration.",
        "parameters": [
          {
            "name": "productId",
            "in": "path",
            "description": "Product ID.",
            "required": true,
            "schema": {
              "type": "string",
              "format": "uuid"
            }
          }
        ],
        "responses": {
          "204": {
            "description": "No Content"
          }
        },
        "security": [
          {
            "ApiKey": [],
            "EdiScheme": []
          }
        ]
      },
      "get": {
        "tags": [
          "Inventory"
        ],
        "summary": "Get SKU configuration for a product.",
        "parameters": [
          {
            "name": "productId",
            "in": "path",
            "description": "Product ID.",
            "required": true,
            "schema": {
              "type": "string",
              "format": "uuid"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/GetSkuConfigResponse"
                }
              }
            }
          },
          "404": {
            "description": "Not Found",
            "content": {
              "application/json": {
                "schema": {
                  "oneOf": [
                    {
                      "$ref": "#/components/schemas/ProblemDetails"
                    },
                    {
                      "$ref": "#/components/schemas/HttpValidationProblemDetails"
                    }
                  ]
                }
              }
            }
          }
        },
        "security": [
          {
            "ApiKey": [],
            "EdiScheme": []
          }
        ]
      }
    },
    "/api/v2/inventory/stock/locations/{locationId}/products/{productId}/not-serialized/adjust": {
      "post": {
        "tags": [
          "Inventory"
        ],
        "summary": "Adjusts stock of not serialized product with given quantity.",
        "parameters": [
          {
            "name": "productId",
            "in": "path",
            "description": "Product unique identifier.",
            "required": true,
            "schema": {
              "type": "string",
              "format": "uuid"
            }
          },
          {
            "name": "locationId",
            "in": "path",
            "description": "Location unique identifier.",
            "required": true,
            "schema": {
              "type": "string",
              "format": "uuid"
            }
          }
        ],
        "requestBody": {
          "description": "Adjustment request payload.",
          "content": {
            "application/merge-patch+json": {
              "schema": {
                "$ref": "#/components/schemas/AdjustNotSerializedProductRequest"
              }
            },
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/AdjustNotSerializedProductRequest"
              }
            },
            "text/json": {
              "schema": {
                "$ref": "#/components/schemas/AdjustNotSerializedProductRequest"
              }
            },
            "application/*+json": {
              "schema": {
                "$ref": "#/components/schemas/AdjustNotSerializedProductRequest"
              }
            },
            "application/xml": {
              "schema": {
                "$ref": "#/components/schemas/AdjustNotSerializedProductRequest"
              }
            },
            "text/xml": {
              "schema": {
                "$ref": "#/components/schemas/AdjustNotSerializedProductRequest"
              }
            },
            "application/*+xml": {
              "schema": {
                "$ref": "#/components/schemas/AdjustNotSerializedProductRequest"
              }
            }
          }
        },
        "responses": {
          "204": {
            "description": "No Content"
          }
        },
        "security": [
          {
            "ApiKey": [],
            "EdiScheme": []
          }
        ]
      }
    },
    "/api/v2/inventory/stock/locations/{locationId}/products/{productId}/serialized/adjust": {
      "post": {
        "tags": [
          "Inventory"
        ],
        "summary": "Adjusts stock of serialized product with given quantity.",
        "parameters": [
          {
            "name": "productId",
            "in": "path",
            "description": "Product unique identifier.",
            "required": true,
            "schema": {
              "type": "string",
              "format": "uuid"
            }
          },
          {
            "name": "locationId",
            "in": "path",
            "description": "Location unique identifier.",
            "required": true,
            "schema": {
              "type": "string",
              "format": "uuid"
            }
          }
        ],
        "requestBody": {
          "description": "Adjustment request payload.",
          "content": {
            "application/merge-patch+json": {
              "schema": {
                "$ref": "#/components/schemas/AdjustSerializedProductRequest"
              }
            },
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/AdjustSerializedProductRequest"
              }
            },
            "text/json": {
              "schema": {
                "$ref": "#/components/schemas/AdjustSerializedProductRequest"
              }
            },
            "application/*+json": {
              "schema": {
                "$ref": "#/components/schemas/AdjustSerializedProductRequest"
              }
            }
          }
        },
        "responses": {
          "204": {
            "description": "No Content"
          }
        },
        "security": [
          {
            "ApiKey": [],
            "EdiScheme": []
          }
        ]
      }
    },
    "/api/v2/inventory/suppliers": {
      "get": {
        "tags": [
          "Inventory"
        ],
        "summary": "Get a paged list of all available inventory suppliers.",
        "parameters": [
          {
            "name": "Page",
            "in": "query",
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          },
          {
            "name": "PerPage",
            "in": "query",
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/SupplierResponsePagingResponse"
                }
              }
            }
          }
        },
        "security": [
          {
            "ApiKey": [],
            "EdiScheme": []
          }
        ]
      },
      "post": {
        "tags": [
          "Inventory"
        ],
        "summary": "Creates a new inventory supplier.",
        "requestBody": {
          "description": "Supplier payload.",
          "content": {
            "application/merge-patch+json": {
              "schema": {
                "oneOf": [
                  {
                    "$ref": "#/components/schemas/AuSupplierRequest"
                  },
                  {
                    "$ref": "#/components/schemas/UsSupplierRequest"
                  },
                  {
                    "$ref": "#/components/schemas/UkSupplierRequest"
                  },
                  {
                    "$ref": "#/components/schemas/NzSupplierRequest"
                  },
                  {
                    "$ref": "#/components/schemas/RoiSupplierRequest"
                  }
                ]
              }
            },
            "application/json": {
              "schema": {
                "oneOf": [
                  {
                    "$ref": "#/components/schemas/AuSupplierRequest"
                  },
                  {
                    "$ref": "#/components/schemas/UsSupplierRequest"
                  },
                  {
                    "$ref": "#/components/schemas/UkSupplierRequest"
                  },
                  {
                    "$ref": "#/components/schemas/NzSupplierRequest"
                  },
                  {
                    "$ref": "#/components/schemas/RoiSupplierRequest"
                  }
                ]
              }
            },
            "text/json": {
              "schema": {
                "oneOf": [
                  {
                    "$ref": "#/components/schemas/AuSupplierRequest"
                  },
                  {
                    "$ref": "#/components/schemas/UsSupplierRequest"
                  },
                  {
                    "$ref": "#/components/schemas/UkSupplierRequest"
                  },
                  {
                    "$ref": "#/components/schemas/NzSupplierRequest"
                  },
                  {
                    "$ref": "#/components/schemas/RoiSupplierRequest"
                  }
                ]
              }
            },
            "application/*+json": {
              "schema": {
                "oneOf": [
                  {
                    "$ref": "#/components/schemas/AuSupplierRequest"
                  },
                  {
                    "$ref": "#/components/schemas/UsSupplierRequest"
                  },
                  {
                    "$ref": "#/components/schemas/UkSupplierRequest"
                  },
                  {
                    "$ref": "#/components/schemas/NzSupplierRequest"
                  },
                  {
                    "$ref": "#/components/schemas/RoiSupplierRequest"
                  }
                ]
              }
            },
            "application/xml": {
              "schema": {
                "oneOf": [
                  {
                    "$ref": "#/components/schemas/AuSupplierRequest"
                  },
                  {
                    "$ref": "#/components/schemas/UsSupplierRequest"
                  },
                  {
                    "$ref": "#/components/schemas/UkSupplierRequest"
                  },
                  {
                    "$ref": "#/components/schemas/NzSupplierRequest"
                  },
                  {
                    "$ref": "#/components/schemas/RoiSupplierRequest"
                  }
                ]
              }
            },
            "text/xml": {
              "schema": {
                "oneOf": [
                  {
                    "$ref": "#/components/schemas/AuSupplierRequest"
                  },
                  {
                    "$ref": "#/components/schemas/UsSupplierRequest"
                  },
                  {
                    "$ref": "#/components/schemas/UkSupplierRequest"
                  },
                  {
                    "$ref": "#/components/schemas/NzSupplierRequest"
                  },
                  {
                    "$ref": "#/components/schemas/RoiSupplierRequest"
                  }
                ]
              }
            },
            "application/*+xml": {
              "schema": {
                "oneOf": [
                  {
                    "$ref": "#/components/schemas/AuSupplierRequest"
                  },
                  {
                    "$ref": "#/components/schemas/UsSupplierRequest"
                  },
                  {
                    "$ref": "#/components/schemas/UkSupplierRequest"
                  },
                  {
                    "$ref": "#/components/schemas/NzSupplierRequest"
                  },
                  {
                    "$ref": "#/components/schemas/RoiSupplierRequest"
                  }
                ]
              }
            }
          }
        },
        "responses": {
          "201": {
            "description": "Created",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/CreatedResponse"
                }
              }
            }
          },
          "400": {
            "description": "Bad Request",
            "content": {
              "application/json": {
                "schema": {
                  "oneOf": [
                    {
                      "$ref": "#/components/schemas/ProblemDetails"
                    },
                    {
                      "$ref": "#/components/schemas/HttpValidationProblemDetails"
                    }
                  ]
                }
              }
            }
          }
        },
        "security": [
          {
            "ApiKey": [],
            "EdiScheme": []
          }
        ]
      }
    },
    "/api/v2/inventory/suppliers/{id}": {
      "get": {
        "tags": [
          "Inventory"
        ],
        "summary": "Get an inventory supplier by id.",
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "description": "Unique identifier of supplier.",
            "required": true,
            "schema": {
              "type": "string",
              "format": "uuid"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "application/json": {
                "schema": {
                  "oneOf": [
                    {
                      "$ref": "#/components/schemas/AuSupplierResponse"
                    },
                    {
                      "$ref": "#/components/schemas/UsSupplierResponse"
                    },
                    {
                      "$ref": "#/components/schemas/UkSupplierResponse"
                    },
                    {
                      "$ref": "#/components/schemas/NzSupplierResponse"
                    },
                    {
                      "$ref": "#/components/schemas/RoiSupplierResponse"
                    }
                  ]
                }
              }
            }
          },
          "404": {
            "description": "Not Found",
            "content": {
              "application/json": {
                "schema": {
                  "oneOf": [
                    {
                      "$ref": "#/components/schemas/ProblemDetails"
                    },
                    {
                      "$ref": "#/components/schemas/HttpValidationProblemDetails"
                    }
                  ]
                }
              }
            }
          }
        },
        "security": [
          {
            "ApiKey": [],
            "EdiScheme": []
          }
        ]
      },
      "put": {
        "tags": [
          "Inventory"
        ],
        "summary": "Updates an existing inventory supplier by id.",
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "description": "Unique identifier of a supplier.",
            "required": true,
            "schema": {
              "type": "string",
              "format": "uuid"
            }
          }
        ],
        "requestBody": {
          "description": "Supplier payload.",
          "content": {
            "application/merge-patch+json": {
              "schema": {
                "oneOf": [
                  {
                    "$ref": "#/components/schemas/AuSupplierRequest"
                  },
                  {
                    "$ref": "#/components/schemas/UsSupplierRequest"
                  },
                  {
                    "$ref": "#/components/schemas/UkSupplierRequest"
                  },
                  {
                    "$ref": "#/components/schemas/NzSupplierRequest"
                  },
                  {
                    "$ref": "#/components/schemas/RoiSupplierRequest"
                  }
                ]
              }
            },
            "application/json": {
              "schema": {
                "oneOf": [
                  {
                    "$ref": "#/components/schemas/AuSupplierRequest"
                  },
                  {
                    "$ref": "#/components/schemas/UsSupplierRequest"
                  },
                  {
                    "$ref": "#/components/schemas/UkSupplierRequest"
                  },
                  {
                    "$ref": "#/components/schemas/NzSupplierRequest"
                  },
                  {
                    "$ref": "#/components/schemas/RoiSupplierRequest"
                  }
                ]
              }
            },
            "text/json": {
              "schema": {
                "oneOf": [
                  {
                    "$ref": "#/components/schemas/AuSupplierRequest"
                  },
                  {
                    "$ref": "#/components/schemas/UsSupplierRequest"
                  },
                  {
                    "$ref": "#/components/schemas/UkSupplierRequest"
                  },
                  {
                    "$ref": "#/components/schemas/NzSupplierRequest"
                  },
                  {
                    "$ref": "#/components/schemas/RoiSupplierRequest"
                  }
                ]
              }
            },
            "application/*+json": {
              "schema": {
                "oneOf": [
                  {
                    "$ref": "#/components/schemas/AuSupplierRequest"
                  },
                  {
                    "$ref": "#/components/schemas/UsSupplierRequest"
                  },
                  {
                    "$ref": "#/components/schemas/UkSupplierRequest"
                  },
                  {
                    "$ref": "#/components/schemas/NzSupplierRequest"
                  },
                  {
                    "$ref": "#/components/schemas/RoiSupplierRequest"
                  }
                ]
              }
            },
            "application/xml": {
              "schema": {
                "oneOf": [
                  {
                    "$ref": "#/components/schemas/AuSupplierRequest"
                  },
                  {
                    "$ref": "#/components/schemas/UsSupplierRequest"
                  },
                  {
                    "$ref": "#/components/schemas/UkSupplierRequest"
                  },
                  {
                    "$ref": "#/components/schemas/NzSupplierRequest"
                  },
                  {
                    "$ref": "#/components/schemas/RoiSupplierRequest"
                  }
                ]
              }
            },
            "text/xml": {
              "schema": {
                "oneOf": [
                  {
                    "$ref": "#/components/schemas/AuSupplierRequest"
                  },
                  {
                    "$ref": "#/components/schemas/UsSupplierRequest"
                  },
                  {
                    "$ref": "#/components/schemas/UkSupplierRequest"
                  },
                  {
                    "$ref": "#/components/schemas/NzSupplierRequest"
                  },
                  {
                    "$ref": "#/components/schemas/RoiSupplierRequest"
                  }
                ]
              }
            },
            "application/*+xml": {
              "schema": {
                "oneOf": [
                  {
                    "$ref": "#/components/schemas/AuSupplierRequest"
                  },
                  {
                    "$ref": "#/components/schemas/UsSupplierRequest"
                  },
                  {
                    "$ref": "#/components/schemas/UkSupplierRequest"
                  },
                  {
                    "$ref": "#/components/schemas/NzSupplierRequest"
                  },
                  {
                    "$ref": "#/components/schemas/RoiSupplierRequest"
                  }
                ]
              }
            }
          }
        },
        "responses": {
          "204": {
            "description": "No Content"
          },
          "400": {
            "description": "Bad Request",
            "content": {
              "application/json": {
                "schema": {
                  "oneOf": [
                    {
                      "$ref": "#/components/schemas/ProblemDetails"
                    },
                    {
                      "$ref": "#/components/schemas/HttpValidationProblemDetails"
                    }
                  ]
                }
              }
            }
          },
          "404": {
            "description": "Not Found",
            "content": {
              "application/json": {
                "schema": {
                  "oneOf": [
                    {
                      "$ref": "#/components/schemas/ProblemDetails"
                    },
                    {
                      "$ref": "#/components/schemas/HttpValidationProblemDetails"
                    }
                  ]
                }
              }
            }
          }
        },
        "security": [
          {
            "ApiKey": [],
            "EdiScheme": []
          }
        ]
      }
    },
    "/api/v2/invocing/funders": {
      "get": {
        "tags": [
          "Invoicing"
        ],
        "summary": "Get a paged list of all available invoicing funders.",
        "parameters": [
          {
            "name": "Page",
            "in": "query",
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          },
          {
            "name": "PerPage",
            "in": "query",
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/FunderResponsePagingResponse"
                }
              }
            }
          }
        },
        "security": [
          {
            "ApiKey": [],
            "EdiScheme": []
          }
        ]
      },
      "post": {
        "tags": [
          "Invoicing"
        ],
        "summary": "Creates a new Funder.",
        "requestBody": {
          "description": "Funder payload.",
          "content": {
            "application/merge-patch+json": {
              "schema": {
                "$ref": "#/components/schemas/FunderRequest"
              }
            },
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/FunderRequest"
              }
            },
            "text/json": {
              "schema": {
                "$ref": "#/components/schemas/FunderRequest"
              }
            },
            "application/*+json": {
              "schema": {
                "$ref": "#/components/schemas/FunderRequest"
              }
            },
            "application/xml": {
              "schema": {
                "$ref": "#/components/schemas/FunderRequest"
              }
            },
            "text/xml": {
              "schema": {
                "$ref": "#/components/schemas/FunderRequest"
              }
            },
            "application/*+xml": {
              "schema": {
                "$ref": "#/components/schemas/FunderRequest"
              }
            }
          }
        },
        "responses": {
          "201": {
            "description": "Created",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/CreatedResponse"
                }
              }
            }
          }
        },
        "security": [
          {
            "ApiKey": [],
            "EdiScheme": []
          }
        ]
      }
    },
    "/api/v2/invocing/funders/{id}": {
      "get": {
        "tags": [
          "Invoicing"
        ],
        "summary": "Gets a Funder by id.",
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "description": "Unique identifier of a Funder.",
            "required": true,
            "schema": {
              "type": "string",
              "format": "uuid"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/FunderResponse"
                }
              }
            }
          },
          "404": {
            "description": "Not Found",
            "content": {
              "application/json": {
                "schema": {
                  "oneOf": [
                    {
                      "$ref": "#/components/schemas/ProblemDetails"
                    },
                    {
                      "$ref": "#/components/schemas/HttpValidationProblemDetails"
                    }
                  ]
                }
              }
            }
          }
        },
        "security": [
          {
            "ApiKey": [],
            "EdiScheme": []
          }
        ]
      },
      "put": {
        "tags": [
          "Invoicing"
        ],
        "summary": "Updates Funder by id with a given payload.",
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "description": "Unique identifier of a Funder.",
            "required": true,
            "schema": {
              "type": "string",
              "format": "uuid"
            }
          }
        ],
        "requestBody": {
          "description": "Funder payload.",
          "content": {
            "application/merge-patch+json": {
              "schema": {
                "$ref": "#/components/schemas/FunderRequest"
              }
            },
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/FunderRequest"
              }
            },
            "text/json": {
              "schema": {
                "$ref": "#/components/schemas/FunderRequest"
              }
            },
            "application/*+json": {
              "schema": {
                "$ref": "#/components/schemas/FunderRequest"
              }
            },
            "application/xml": {
              "schema": {
                "$ref": "#/components/schemas/FunderRequest"
              }
            },
            "text/xml": {
              "schema": {
                "$ref": "#/components/schemas/FunderRequest"
              }
            },
            "application/*+xml": {
              "schema": {
                "$ref": "#/components/schemas/FunderRequest"
              }
            }
          }
        },
        "responses": {
          "204": {
            "description": "No Content"
          },
          "400": {
            "description": "Bad Request",
            "content": {
              "application/json": {
                "schema": {
                  "oneOf": [
                    {
                      "$ref": "#/components/schemas/ProblemDetails"
                    },
                    {
                      "$ref": "#/components/schemas/HttpValidationProblemDetails"
                    }
                  ]
                }
              }
            }
          },
          "404": {
            "description": "Not Found",
            "content": {
              "application/json": {
                "schema": {
                  "oneOf": [
                    {
                      "$ref": "#/components/schemas/ProblemDetails"
                    },
                    {
                      "$ref": "#/components/schemas/HttpValidationProblemDetails"
                    }
                  ]
                }
              }
            }
          }
        },
        "security": [
          {
            "ApiKey": [],
            "EdiScheme": []
          }
        ]
      }
    },
    "/api/v2/invocing/sales/{saleId}": {
      "get": {
        "tags": [
          "Invoicing"
        ],
        "summary": "Get specific sale by ID.",
        "parameters": [
          {
            "name": "saleId",
            "in": "path",
            "description": "Sale ID.",
            "required": true,
            "schema": {
              "type": "string",
              "format": "uuid"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "application/json": {
                "schema": {
                  "oneOf": [
                    {
                      "$ref": "#/components/schemas/AuSaleResponse"
                    },
                    {
                      "$ref": "#/components/schemas/NzSaleResponse"
                    },
                    {
                      "$ref": "#/components/schemas/UkNhsSaleResponse"
                    },
                    {
                      "$ref": "#/components/schemas/UkSaleResponse"
                    },
                    {
                      "$ref": "#/components/schemas/UsSaleResponse"
                    }
                  ]
                }
              }
            }
          }
        },
        "security": [
          {
            "ApiKey": [],
            "EdiScheme": []
          }
        ]
      }
    },
    "/api/v2/invocing/sales/{saleId}/products": {
      "get": {
        "tags": [
          "Invoicing"
        ],
        "summary": "Get a list of sale products for a given sale.",
        "parameters": [
          {
            "name": "saleId",
            "in": "path",
            "description": "Sale ID.",
            "required": true,
            "schema": {
              "type": "string",
              "format": "uuid"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "application/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "oneOf": [
                      {
                        "$ref": "#/components/schemas/AuSaleProductResponse"
                      },
                      {
                        "$ref": "#/components/schemas/NzSaleProductResponse"
                      },
                      {
                        "$ref": "#/components/schemas/UkSaleProductResponse"
                      },
                      {
                        "$ref": "#/components/schemas/UsSaleProductResponse"
                      }
                    ]
                  }
                }
              }
            }
          }
        },
        "security": [
          {
            "ApiKey": [],
            "EdiScheme": []
          }
        ]
      }
    },
    "/api/v2/invocing/sales/products/{saleProductId}": {
      "get": {
        "tags": [
          "Invoicing"
        ],
        "summary": "Get details of a specific sale product.",
        "parameters": [
          {
            "name": "saleProductId",
            "in": "path",
            "description": "Sale product ID.",
            "required": true,
            "schema": {
              "type": "string",
              "format": "uuid"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "application/json": {
                "schema": {
                  "oneOf": [
                    {
                      "$ref": "#/components/schemas/AuSaleProductDetailsResponse"
                    },
                    {
                      "$ref": "#/components/schemas/NzSaleProductDetailsResponse"
                    },
                    {
                      "$ref": "#/components/schemas/UkSaleProductDetailsResponse"
                    },
                    {
                      "$ref": "#/components/schemas/UsSaleProductDetailsResponse"
                    }
                  ]
                }
              }
            }
          }
        },
        "security": [
          {
            "ApiKey": [],
            "EdiScheme": []
          }
        ]
      }
    },
    "/api/v2/invocing/sales/{saleId}/invoices": {
      "get": {
        "tags": [
          "Invoicing"
        ],
        "summary": "Get a list of sale invoices for a given sale.",
        "parameters": [
          {
            "name": "saleId",
            "in": "path",
            "description": "Sale ID.",
            "required": true,
            "schema": {
              "type": "string",
              "format": "uuid"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "application/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "oneOf": [
                      {
                        "$ref": "#/components/schemas/AuSaleInvoiceResponse"
                      }
                    ]
                  }
                }
              }
            }
          }
        },
        "security": [
          {
            "ApiKey": [],
            "EdiScheme": []
          }
        ]
      }
    },
    "/api/v2/invocing/sales/{saleId}/credit-notes": {
      "get": {
        "tags": [
          "Invoicing"
        ],
        "summary": "Get a list of credit notes for a given sale.",
        "parameters": [
          {
            "name": "saleId",
            "in": "path",
            "description": "Sale ID.",
            "required": true,
            "schema": {
              "type": "string",
              "format": "uuid"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "application/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "oneOf": [
                      {
                        "$ref": "#/components/schemas/AuCreditNoteResponse"
                      },
                      {
                        "$ref": "#/components/schemas/NzCreditNoteResponse"
                      },
                      {
                        "$ref": "#/components/schemas/UkCreditNoteResponse"
                      },
                      {
                        "$ref": "#/components/schemas/UsCreditNoteResponse"
                      }
                    ]
                  }
                }
              }
            }
          }
        },
        "security": [
          {
            "ApiKey": [],
            "EdiScheme": []
          }
        ]
      }
    },
    "/api/v2/invocing/sales/{saleId}/delivery-notes": {
      "get": {
        "tags": [
          "Invoicing"
        ],
        "summary": "Get a list of delivery notes for a given sale.",
        "parameters": [
          {
            "name": "saleId",
            "in": "path",
            "description": "Sale ID.",
            "required": true,
            "schema": {
              "type": "string",
              "format": "uuid"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "application/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "oneOf": [
                      {
                        "$ref": "#/components/schemas/AuDeliveryNoteResponse"
                      },
                      {
                        "$ref": "#/components/schemas/NzDeliveryNoteResponse"
                      },
                      {
                        "$ref": "#/components/schemas/UkDeliveryNoteResponse"
                      },
                      {
                        "$ref": "#/components/schemas/UsDeliveryNoteResponse"
                      }
                    ]
                  }
                }
              }
            }
          }
        },
        "security": [
          {
            "ApiKey": [],
            "EdiScheme": []
          }
        ]
      }
    },
    "/api/v2/invocing/sales/{saleId}/trials": {
      "get": {
        "tags": [
          "Invoicing"
        ],
        "summary": "Get a list of trials for a given sale.",
        "parameters": [
          {
            "name": "saleId",
            "in": "path",
            "description": "Sale ID.",
            "required": true,
            "schema": {
              "type": "string",
              "format": "uuid"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "application/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "oneOf": [
                      {
                        "$ref": "#/components/schemas/AuTrialResponse"
                      },
                      {
                        "$ref": "#/components/schemas/TrialResponse"
                      }
                    ]
                  }
                }
              }
            }
          }
        },
        "security": [
          {
            "ApiKey": [],
            "EdiScheme": []
          }
        ]
      }
    },
    "/api/v2/locations/regions": {
      "get": {
        "tags": [
          "Locations"
        ],
        "summary": "Returns a list of all regions. Supports paging.",
        "parameters": [
          {
            "name": "Page",
            "in": "query",
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          },
          {
            "name": "PerPage",
            "in": "query",
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/ActiveDictionaryResponsePagingResponse"
                }
              }
            }
          }
        },
        "security": [
          {
            "ApiKey": [],
            "EdiScheme": []
          }
        ]
      }
    },
    "/api/v2/marketing/campaigns": {
      "get": {
        "tags": [
          "Marketing"
        ],
        "summary": "Get a list of all campaigns. Supports paging.",
        "parameters": [
          {
            "name": "Page",
            "in": "query",
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          },
          {
            "name": "PerPage",
            "in": "query",
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/CampaignResponsePagingResponse"
                }
              }
            }
          }
        },
        "security": [
          {
            "ApiKey": [],
            "EdiScheme": []
          }
        ]
      },
      "post": {
        "tags": [
          "Marketing"
        ],
        "summary": "Creates a new campaign.",
        "requestBody": {
          "description": "Campaign payload.",
          "content": {
            "application/merge-patch+json": {
              "schema": {
                "$ref": "#/components/schemas/CampaignRequest"
              }
            },
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/CampaignRequest"
              }
            },
            "text/json": {
              "schema": {
                "$ref": "#/components/schemas/CampaignRequest"
              }
            },
            "application/*+json": {
              "schema": {
                "$ref": "#/components/schemas/CampaignRequest"
              }
            },
            "application/xml": {
              "schema": {
                "$ref": "#/components/schemas/CampaignRequest"
              }
            },
            "text/xml": {
              "schema": {
                "$ref": "#/components/schemas/CampaignRequest"
              }
            },
            "application/*+xml": {
              "schema": {
                "$ref": "#/components/schemas/CampaignRequest"
              }
            }
          }
        },
        "responses": {
          "201": {
            "description": "Created",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/CreatedResponse"
                }
              }
            }
          },
          "400": {
            "description": "Bad Request",
            "content": {
              "application/json": {
                "schema": {
                  "oneOf": [
                    {
                      "$ref": "#/components/schemas/ProblemDetails"
                    },
                    {
                      "$ref": "#/components/schemas/HttpValidationProblemDetails"
                    }
                  ]
                }
              }
            }
          }
        },
        "security": [
          {
            "ApiKey": [],
            "EdiScheme": []
          }
        ]
      }
    },
    "/api/v2/marketing/campaigns/{id}": {
      "get": {
        "tags": [
          "Marketing"
        ],
        "summary": "Gets a campaign by id.",
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "description": "Unique identifier of a campaign.",
            "required": true,
            "schema": {
              "type": "string",
              "format": "uuid"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/CampaignResponse"
                }
              }
            }
          },
          "404": {
            "description": "Not Found",
            "content": {
              "application/json": {
                "schema": {
                  "oneOf": [
                    {
                      "$ref": "#/components/schemas/ProblemDetails"
                    },
                    {
                      "$ref": "#/components/schemas/HttpValidationProblemDetails"
                    }
                  ]
                }
              }
            }
          }
        },
        "security": [
          {
            "ApiKey": [],
            "EdiScheme": []
          }
        ]
      },
      "put": {
        "tags": [
          "Marketing"
        ],
        "summary": "Updates an existing campaign by id.",
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "description": "Unique identifier of a campaign.",
            "required": true,
            "schema": {
              "type": "string",
              "format": "uuid"
            }
          }
        ],
        "requestBody": {
          "description": "Campaign payload.",
          "content": {
            "application/merge-patch+json": {
              "schema": {
                "$ref": "#/components/schemas/CampaignRequest"
              }
            },
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/CampaignRequest"
              }
            },
            "text/json": {
              "schema": {
                "$ref": "#/components/schemas/CampaignRequest"
              }
            },
            "application/*+json": {
              "schema": {
                "$ref": "#/components/schemas/CampaignRequest"
              }
            },
            "application/xml": {
              "schema": {
                "$ref": "#/components/schemas/CampaignRequest"
              }
            },
            "text/xml": {
              "schema": {
                "$ref": "#/components/schemas/CampaignRequest"
              }
            },
            "application/*+xml": {
              "schema": {
                "$ref": "#/components/schemas/CampaignRequest"
              }
            }
          }
        },
        "responses": {
          "204": {
            "description": "No Content"
          },
          "400": {
            "description": "Bad Request",
            "content": {
              "application/json": {
                "schema": {
                  "oneOf": [
                    {
                      "$ref": "#/components/schemas/ProblemDetails"
                    },
                    {
                      "$ref": "#/components/schemas/HttpValidationProblemDetails"
                    }
                  ]
                }
              }
            }
          },
          "404": {
            "description": "Not Found",
            "content": {
              "application/json": {
                "schema": {
                  "oneOf": [
                    {
                      "$ref": "#/components/schemas/ProblemDetails"
                    },
                    {
                      "$ref": "#/components/schemas/HttpValidationProblemDetails"
                    }
                  ]
                }
              }
            }
          }
        },
        "security": [
          {
            "ApiKey": [],
            "EdiScheme": []
          }
        ]
      }
    },
    "/api/v2/online-booking/epos/{eposNumber}/appointments": {
      "post": {
        "tags": [
          "OnlineBookings"
        ],
        "parameters": [
          {
            "name": "eposNumber",
            "in": "path",
            "required": true,
            "schema": {
              "type": "string"
            }
          }
        ],
        "requestBody": {
          "content": {
            "application/merge-patch+json": {
              "schema": {
                "$ref": "#/components/schemas/BookAppointmentRequest"
              }
            },
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/BookAppointmentRequest"
              }
            },
            "text/json": {
              "schema": {
                "$ref": "#/components/schemas/BookAppointmentRequest"
              }
            },
            "application/*+json": {
              "schema": {
                "$ref": "#/components/schemas/BookAppointmentRequest"
              }
            },
            "application/xml": {
              "schema": {
                "$ref": "#/components/schemas/BookAppointmentRequest"
              }
            },
            "text/xml": {
              "schema": {
                "$ref": "#/components/schemas/BookAppointmentRequest"
              }
            },
            "application/*+xml": {
              "schema": {
                "$ref": "#/components/schemas/BookAppointmentRequest"
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "$ref": "#/components/schemas/BookAppointmentResponse"
                }
              },
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BookAppointmentResponse"
                }
              },
              "text/json": {
                "schema": {
                  "$ref": "#/components/schemas/BookAppointmentResponse"
                }
              },
              "application/xml": {
                "schema": {
                  "$ref": "#/components/schemas/BookAppointmentResponse"
                }
              },
              "text/xml": {
                "schema": {
                  "$ref": "#/components/schemas/BookAppointmentResponse"
                }
              }
            }
          }
        },
        "security": [
          {
            "ApiKey": [],
            "EdiScheme": []
          }
        ]
      }
    },
    "/api/v2/online-booking/locations/{locationId}/appointments": {
      "post": {
        "tags": [
          "OnlineBookings"
        ],
        "parameters": [
          {
            "name": "locationId",
            "in": "path",
            "required": true,
            "schema": {
              "type": "string",
              "format": "uuid"
            }
          }
        ],
        "requestBody": {
          "content": {
            "application/merge-patch+json": {
              "schema": {
                "$ref": "#/components/schemas/BookAppointmentByLocationRequest"
              }
            },
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/BookAppointmentByLocationRequest"
              }
            },
            "text/json": {
              "schema": {
                "$ref": "#/components/schemas/BookAppointmentByLocationRequest"
              }
            },
            "application/*+json": {
              "schema": {
                "$ref": "#/components/schemas/BookAppointmentByLocationRequest"
              }
            },
            "application/xml": {
              "schema": {
                "$ref": "#/components/schemas/BookAppointmentByLocationRequest"
              }
            },
            "text/xml": {
              "schema": {
                "$ref": "#/components/schemas/BookAppointmentByLocationRequest"
              }
            },
            "application/*+xml": {
              "schema": {
                "$ref": "#/components/schemas/BookAppointmentByLocationRequest"
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "$ref": "#/components/schemas/BookAppointmentByLocationResponse"
                }
              },
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BookAppointmentByLocationResponse"
                }
              },
              "text/json": {
                "schema": {
                  "$ref": "#/components/schemas/BookAppointmentByLocationResponse"
                }
              },
              "application/xml": {
                "schema": {
                  "$ref": "#/components/schemas/BookAppointmentByLocationResponse"
                }
              },
              "text/xml": {
                "schema": {
                  "$ref": "#/components/schemas/BookAppointmentByLocationResponse"
                }
              }
            }
          }
        },
        "security": [
          {
            "ApiKey": [],
            "EdiScheme": []
          }
        ]
      }
    },
    "/api/v2/orders/{orderId}/line-items": {
      "post": {
        "tags": [
          "Orders"
        ],
        "summary": "Adds new line item to an existing Order.",
        "parameters": [
          {
            "name": "orderId",
            "in": "path",
            "description": "Order unique identifier.",
            "required": true,
            "schema": {
              "type": "string",
              "format": "uuid"
            }
          }
        ],
        "requestBody": {
          "description": "Line item to add.",
          "content": {
            "application/merge-patch+json": {
              "schema": {
                "$ref": "#/components/schemas/OrderLineItemRequest"
              }
            },
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/OrderLineItemRequest"
              }
            },
            "text/json": {
              "schema": {
                "$ref": "#/components/schemas/OrderLineItemRequest"
              }
            },
            "application/*+json": {
              "schema": {
                "$ref": "#/components/schemas/OrderLineItemRequest"
              }
            }
          }
        },
        "responses": {
          "204": {
            "description": "No Content"
          },
          "409": {
            "description": "Conflict",
            "content": {
              "application/json": {
                "schema": {
                  "oneOf": [
                    {
                      "$ref": "#/components/schemas/ProblemDetails"
                    },
                    {
                      "$ref": "#/components/schemas/HttpValidationProblemDetails"
                    }
                  ]
                }
              }
            }
          },
          "404": {
            "description": "Not Found",
            "content": {
              "application/json": {
                "schema": {
                  "oneOf": [
                    {
                      "$ref": "#/components/schemas/ProblemDetails"
                    },
                    {
                      "$ref": "#/components/schemas/HttpValidationProblemDetails"
                    }
                  ]
                }
              }
            }
          }
        },
        "security": [
          {
            "ApiKey": [],
            "EdiScheme": []
          }
        ]
      }
    },
    "/api/v2/orders/{orderId}/line-items/{id}": {
      "put": {
        "tags": [
          "Orders"
        ],
        "summary": "Updates an existing Order line item.",
        "parameters": [
          {
            "name": "orderId",
            "in": "path",
            "description": "Order unique identifier.",
            "required": true,
            "schema": {
              "type": "string",
              "format": "uuid"
            }
          },
          {
            "name": "id",
            "in": "path",
            "description": "Line item unique identifier.",
            "required": true,
            "schema": {
              "type": "string",
              "format": "uuid"
            }
          }
        ],
        "requestBody": {
          "description": "Line item to update.",
          "content": {
            "application/merge-patch+json": {
              "schema": {
                "$ref": "#/components/schemas/OrderLineItemRequest"
              }
            },
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/OrderLineItemRequest"
              }
            },
            "text/json": {
              "schema": {
                "$ref": "#/components/schemas/OrderLineItemRequest"
              }
            },
            "application/*+json": {
              "schema": {
                "$ref": "#/components/schemas/OrderLineItemRequest"
              }
            }
          }
        },
        "responses": {
          "404": {
            "description": "Not Found",
            "content": {
              "application/json": {
                "schema": {
                  "oneOf": [
                    {
                      "$ref": "#/components/schemas/ProblemDetails"
                    },
                    {
                      "$ref": "#/components/schemas/HttpValidationProblemDetails"
                    }
                  ]
                }
              }
            }
          },
          "204": {
            "description": "No Content"
          }
        },
        "security": [
          {
            "ApiKey": [],
            "EdiScheme": []
          }
        ]
      }
    },
    "/api/v2/orders/{orderId}/line-items/{id}/accept": {
      "post": {
        "tags": [
          "Orders"
        ],
        "summary": "Accepts single line item in an Order.",
        "parameters": [
          {
            "name": "orderId",
            "in": "path",
            "description": "Order unique identifier.",
            "required": true,
            "schema": {
              "type": "string",
              "format": "uuid"
            }
          },
          {
            "name": "id",
            "in": "path",
            "description": "Line item unique identifier.",
            "required": true,
            "schema": {
              "type": "string",
              "format": "uuid"
            }
          }
        ],
        "requestBody": {
          "description": "Accept payload.",
          "content": {
            "application/merge-patch+json": {
              "schema": {
                "$ref": "#/components/schemas/AcceptLineItemRequest"
              }
            },
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/AcceptLineItemRequest"
              }
            },
            "text/json": {
              "schema": {
                "$ref": "#/components/schemas/AcceptLineItemRequest"
              }
            },
            "application/*+json": {
              "schema": {
                "$ref": "#/components/schemas/AcceptLineItemRequest"
              }
            }
          }
        },
        "responses": {
          "404": {
            "description": "Not Found",
            "content": {
              "application/json": {
                "schema": {
                  "oneOf": [
                    {
                      "$ref": "#/components/schemas/ProblemDetails"
                    },
                    {
                      "$ref": "#/components/schemas/HttpValidationProblemDetails"
                    }
                  ]
                }
              }
            }
          },
          "204": {
            "description": "No Content"
          }
        },
        "security": [
          {
            "ApiKey": [],
            "EdiScheme": []
          }
        ]
      }
    },
    "/api/v2/orders/{orderId}/line-items/{id}/ship": {
      "post": {
        "tags": [
          "Orders"
        ],
        "summary": "Ships single line item in an Order.",
        "parameters": [
          {
            "name": "orderId",
            "in": "path",
            "description": "Order unique identifier.",
            "required": true,
            "schema": {
              "type": "string",
              "format": "uuid"
            }
          },
          {
            "name": "id",
            "in": "path",
            "description": "Line item unique identifier.",
            "required": true,
            "schema": {
              "type": "string",
              "format": "uuid"
            }
          }
        ],
        "requestBody": {
          "description": "Ship payload.",
          "content": {
            "application/merge-patch+json": {
              "schema": {
                "$ref": "#/components/schemas/ShipLineItemRequest"
              }
            },
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/ShipLineItemRequest"
              }
            },
            "text/json": {
              "schema": {
                "$ref": "#/components/schemas/ShipLineItemRequest"
              }
            },
            "application/*+json": {
              "schema": {
                "$ref": "#/components/schemas/ShipLineItemRequest"
              }
            },
            "application/xml": {
              "schema": {
                "$ref": "#/components/schemas/ShipLineItemRequest"
              }
            },
            "text/xml": {
              "schema": {
                "$ref": "#/components/schemas/ShipLineItemRequest"
              }
            },
            "application/*+xml": {
              "schema": {
                "$ref": "#/components/schemas/ShipLineItemRequest"
              }
            }
          }
        },
        "responses": {
          "404": {
            "description": "Not Found",
            "content": {
              "application/json": {
                "schema": {
                  "oneOf": [
                    {
                      "$ref": "#/components/schemas/ProblemDetails"
                    },
                    {
                      "$ref": "#/components/schemas/HttpValidationProblemDetails"
                    }
                  ]
                }
              }
            }
          },
          "204": {
            "description": "No Content"
          }
        },
        "security": [
          {
            "ApiKey": [],
            "EdiScheme": []
          }
        ]
      }
    },
    "/api/v2/orders/{orderId}/line-items/{id}/reject": {
      "post": {
        "tags": [
          "Orders"
        ],
        "summary": "Rejects single line item in an Order.",
        "parameters": [
          {
            "name": "orderId",
            "in": "path",
            "description": "Order unique identifier.",
            "required": true,
            "schema": {
              "type": "string",
              "format": "uuid"
            }
          },
          {
            "name": "id",
            "in": "path",
            "description": "Line item unique identifier.",
            "required": true,
            "schema": {
              "type": "string",
              "format": "uuid"
            }
          }
        ],
        "requestBody": {
          "description": "Reject payload.",
          "content": {
            "application/merge-patch+json": {
              "schema": {
                "$ref": "#/components/schemas/RejectLineItemRequest"
              }
            },
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/RejectLineItemRequest"
              }
            },
            "text/json": {
              "schema": {
                "$ref": "#/components/schemas/RejectLineItemRequest"
              }
            },
            "application/*+json": {
              "schema": {
                "$ref": "#/components/schemas/RejectLineItemRequest"
              }
            },
            "application/xml": {
              "schema": {
                "$ref": "#/components/schemas/RejectLineItemRequest"
              }
            },
            "text/xml": {
              "schema": {
                "$ref": "#/components/schemas/RejectLineItemRequest"
              }
            },
            "application/*+xml": {
              "schema": {
                "$ref": "#/components/schemas/RejectLineItemRequest"
              }
            }
          }
        },
        "responses": {
          "404": {
            "description": "Not Found",
            "content": {
              "application/json": {
                "schema": {
                  "oneOf": [
                    {
                      "$ref": "#/components/schemas/ProblemDetails"
                    },
                    {
                      "$ref": "#/components/schemas/HttpValidationProblemDetails"
                    }
                  ]
                }
              }
            }
          },
          "204": {
            "description": "No Content"
          }
        },
        "security": [
          {
            "ApiKey": [],
            "EdiScheme": []
          }
        ]
      }
    },
    "/api/v2/orders/{orderId}/line-items/{id}/deliver": {
      "post": {
        "tags": [
          "Orders"
        ],
        "summary": "Delivers single line item in an Order.",
        "parameters": [
          {
            "name": "orderId",
            "in": "path",
            "description": "Order unique identifier.",
            "required": true,
            "schema": {
              "type": "string",
              "format": "uuid"
            }
          },
          {
            "name": "id",
            "in": "path",
            "description": "Line item unique identifier.",
            "required": true,
            "schema": {
              "type": "string",
              "format": "uuid"
            }
          }
        ],
        "requestBody": {
          "description": "Deliver payload.",
          "content": {
            "application/merge-patch+json": {
              "schema": {
                "$ref": "#/components/schemas/DeliverLineItemRequest"
              }
            },
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/DeliverLineItemRequest"
              }
            },
            "text/json": {
              "schema": {
                "$ref": "#/components/schemas/DeliverLineItemRequest"
              }
            },
            "application/*+json": {
              "schema": {
                "$ref": "#/components/schemas/DeliverLineItemRequest"
              }
            },
            "application/xml": {
              "schema": {
                "$ref": "#/components/schemas/DeliverLineItemRequest"
              }
            },
            "text/xml": {
              "schema": {
                "$ref": "#/components/schemas/DeliverLineItemRequest"
              }
            },
            "application/*+xml": {
              "schema": {
                "$ref": "#/components/schemas/DeliverLineItemRequest"
              }
            }
          }
        },
        "responses": {
          "404": {
            "description": "Not Found",
            "content": {
              "application/json": {
                "schema": {
                  "oneOf": [
                    {
                      "$ref": "#/components/schemas/ProblemDetails"
                    },
                    {
                      "$ref": "#/components/schemas/HttpValidationProblemDetails"
                    }
                  ]
                }
              }
            }
          },
          "204": {
            "description": "No Content"
          }
        },
        "security": [
          {
            "ApiKey": [],
            "EdiScheme": []
          }
        ]
      }
    },
    "/api/v2/orders/{orderId}/line-items/{id}/cancel": {
      "post": {
        "tags": [
          "Orders"
        ],
        "summary": "Cancels single line item in an Order.",
        "parameters": [
          {
            "name": "orderId",
            "in": "path",
            "description": "Order unique identifier.",
            "required": true,
            "schema": {
              "type": "string",
              "format": "uuid"
            }
          },
          {
            "name": "id",
            "in": "path",
            "description": "Line item unique identifier.",
            "required": true,
            "schema": {
              "type": "string",
              "format": "uuid"
            }
          }
        ],
        "responses": {
          "404": {
            "description": "Not Found",
            "content": {
              "application/json": {
                "schema": {
                  "oneOf": [
                    {
                      "$ref": "#/components/schemas/ProblemDetails"
                    },
                    {
                      "$ref": "#/components/schemas/HttpValidationProblemDetails"
                    }
                  ]
                }
              }
            }
          },
          "204": {
            "description": "No Content"
          }
        },
        "security": [
          {
            "ApiKey": [],
            "EdiScheme": []
          }
        ]
      }
    },
    "/api/v2/orders/{orderId}/line-items/{id}/partial-delivery": {
      "post": {
        "tags": [
          "Orders"
        ],
        "summary": "Partial delivery for Order line item.",
        "parameters": [
          {
            "name": "orderId",
            "in": "path",
            "description": "Order unique identifier.",
            "required": true,
            "schema": {
              "type": "string",
              "format": "uuid"
            }
          },
          {
            "name": "id",
            "in": "path",
            "description": "Line item unique identifier.",
            "required": true,
            "schema": {
              "type": "string",
              "format": "uuid"
            }
          }
        ],
        "requestBody": {
          "description": "New quantity to split partial delivery.",
          "content": {
            "application/merge-patch+json": {
              "schema": {
                "$ref": "#/components/schemas/PartialDeliverLineItemRequest"
              }
            },
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/PartialDeliverLineItemRequest"
              }
            },
            "text/json": {
              "schema": {
                "$ref": "#/components/schemas/PartialDeliverLineItemRequest"
              }
            },
            "application/*+json": {
              "schema": {
                "$ref": "#/components/schemas/PartialDeliverLineItemRequest"
              }
            },
            "application/xml": {
              "schema": {
                "$ref": "#/components/schemas/PartialDeliverLineItemRequest"
              }
            },
            "text/xml": {
              "schema": {
                "$ref": "#/components/schemas/PartialDeliverLineItemRequest"
              }
            },
            "application/*+xml": {
              "schema": {
                "$ref": "#/components/schemas/PartialDeliverLineItemRequest"
              }
            }
          }
        },
        "responses": {
          "204": {
            "description": "No Content"
          }
        },
        "security": [
          {
            "ApiKey": [],
            "EdiScheme": []
          }
        ]
      }
    },
    "/api/v2/orders": {
      "get": {
        "tags": [
          "Orders"
        ],
        "summary": "Searches Orders, based on search criteria.",
        "parameters": [
          {
            "name": "LocationId",
            "in": "query",
            "schema": {
              "type": "string",
              "format": "uuid"
            }
          },
          {
            "name": "SupplierId",
            "in": "query",
            "schema": {
              "type": "string",
              "format": "uuid"
            }
          },
          {
            "name": "PatientId",
            "in": "query",
            "schema": {
              "type": "string",
              "format": "uuid"
            }
          },
          {
            "name": "Status",
            "in": "query",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "FromDate",
            "in": "query",
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          },
          {
            "name": "ToDate",
            "in": "query",
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          },
          {
            "name": "Page",
            "in": "query",
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          },
          {
            "name": "PerPage",
            "in": "query",
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/SearchOrderResponseTablePageResult"
                }
              }
            }
          }
        },
        "security": [
          {
            "ApiKey": [],
            "EdiScheme": []
          }
        ]
      },
      "post": {
        "tags": [
          "Orders"
        ],
        "summary": "Creates a new Order.",
        "requestBody": {
          "description": "Order payload.",
          "content": {
            "application/merge-patch+json": {
              "schema": {
                "$ref": "#/components/schemas/OrderRequest"
              }
            },
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/OrderRequest"
              }
            },
            "text/json": {
              "schema": {
                "$ref": "#/components/schemas/OrderRequest"
              }
            },
            "application/*+json": {
              "schema": {
                "$ref": "#/components/schemas/OrderRequest"
              }
            }
          }
        },
        "responses": {
          "201": {
            "description": "Created",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/CreatedResponse"
                }
              }
            }
          }
        },
        "security": [
          {
            "ApiKey": [],
            "EdiScheme": []
          }
        ]
      }
    },
    "/api/v2/orders/{id}": {
      "get": {
        "tags": [
          "Orders"
        ],
        "summary": "Gets an Order by unique identifier.",
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "description": "Unique identifier of an Order.",
            "required": true,
            "schema": {
              "type": "string",
              "format": "uuid"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/OrderResponse"
                }
              }
            }
          },
          "404": {
            "description": "Not Found",
            "content": {
              "application/json": {
                "schema": {
                  "oneOf": [
                    {
                      "$ref": "#/components/schemas/ProblemDetails"
                    },
                    {
                      "$ref": "#/components/schemas/HttpValidationProblemDetails"
                    }
                  ]
                }
              }
            }
          }
        },
        "security": [
          {
            "ApiKey": [],
            "EdiScheme": []
          }
        ]
      }
    },
    "/api/v2/orders/{id}/submit": {
      "post": {
        "tags": [
          "Orders"
        ],
        "summary": "Submits an Order by given id.",
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "description": "Unique identifier of an Order.",
            "required": true,
            "schema": {
              "type": "string",
              "format": "uuid"
            }
          }
        ],
        "responses": {
          "204": {
            "description": "No Content"
          }
        },
        "security": [
          {
            "ApiKey": [],
            "EdiScheme": []
          }
        ]
      }
    },
    "/api/v2/orders/{id}/cancel": {
      "post": {
        "tags": [
          "Orders"
        ],
        "summary": "Cancels an Order by given id.",
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "description": "Unique identifier of an Order.",
            "required": true,
            "schema": {
              "type": "string",
              "format": "uuid"
            }
          }
        ],
        "responses": {
          "204": {
            "description": "No Content"
          }
        },
        "security": [
          {
            "ApiKey": [],
            "EdiScheme": []
          }
        ]
      }
    },
    "/api/v2/orders/{id}/reject": {
      "post": {
        "tags": [
          "Orders"
        ],
        "summary": "Rejects an Order by a given id.",
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "description": "Unique identifier of an Order.",
            "required": true,
            "schema": {
              "type": "string",
              "format": "uuid"
            }
          }
        ],
        "requestBody": {
          "description": "Line items to reject.",
          "content": {
            "application/merge-patch+json": {
              "schema": {
                "$ref": "#/components/schemas/RejectOrderRequest"
              }
            },
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/RejectOrderRequest"
              }
            },
            "text/json": {
              "schema": {
                "$ref": "#/components/schemas/RejectOrderRequest"
              }
            },
            "application/*+json": {
              "schema": {
                "$ref": "#/components/schemas/RejectOrderRequest"
              }
            }
          }
        },
        "responses": {
          "204": {
            "description": "No Content"
          }
        },
        "security": [
          {
            "ApiKey": [],
            "EdiScheme": []
          }
        ]
      }
    },
    "/api/v2/orders/{id}/ship": {
      "post": {
        "tags": [
          "Orders"
        ],
        "summary": "Ships an Order by a given id.",
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "description": "Unique identifier of an Order.",
            "required": true,
            "schema": {
              "type": "string",
              "format": "uuid"
            }
          }
        ],
        "requestBody": {
          "description": "Line items to ship.",
          "content": {
            "application/merge-patch+json": {
              "schema": {
                "$ref": "#/components/schemas/ShipOrderRequest"
              }
            },
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/ShipOrderRequest"
              }
            },
            "text/json": {
              "schema": {
                "$ref": "#/components/schemas/ShipOrderRequest"
              }
            },
            "application/*+json": {
              "schema": {
                "$ref": "#/components/schemas/ShipOrderRequest"
              }
            }
          }
        },
        "responses": {
          "204": {
            "description": "No Content"
          }
        },
        "security": [
          {
            "ApiKey": [],
            "EdiScheme": []
          }
        ]
      }
    },
    "/api/v2/orders/{id}/deliver": {
      "post": {
        "tags": [
          "Orders"
        ],
        "summary": "Delivers an Order by a given id.",
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "description": "Unique identifier of an Order.",
            "required": true,
            "schema": {
              "type": "string",
              "format": "uuid"
            }
          }
        ],
        "requestBody": {
          "description": "Line items to deliver.",
          "content": {
            "application/merge-patch+json": {
              "schema": {
                "$ref": "#/components/schemas/DeliverOrderRequest"
              }
            },
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/DeliverOrderRequest"
              }
            },
            "text/json": {
              "schema": {
                "$ref": "#/components/schemas/DeliverOrderRequest"
              }
            },
            "application/*+json": {
              "schema": {
                "$ref": "#/components/schemas/DeliverOrderRequest"
              }
            }
          }
        },
        "responses": {
          "204": {
            "description": "No Content"
          }
        },
        "security": [
          {
            "ApiKey": [],
            "EdiScheme": []
          }
        ]
      }
    },
    "/api/v2/orders/{id}/accept": {
      "post": {
        "tags": [
          "Orders"
        ],
        "summary": "Accepts an Order by a given id.",
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "description": "Unique identifier of an Order.",
            "required": true,
            "schema": {
              "type": "string",
              "format": "uuid"
            }
          }
        ],
        "requestBody": {
          "description": "Line items to accept.",
          "content": {
            "application/merge-patch+json": {
              "schema": {
                "$ref": "#/components/schemas/AcceptOrderRequest"
              }
            },
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/AcceptOrderRequest"
              }
            },
            "text/json": {
              "schema": {
                "$ref": "#/components/schemas/AcceptOrderRequest"
              }
            },
            "application/*+json": {
              "schema": {
                "$ref": "#/components/schemas/AcceptOrderRequest"
              }
            }
          }
        },
        "responses": {
          "204": {
            "description": "No Content"
          }
        },
        "security": [
          {
            "ApiKey": [],
            "EdiScheme": []
          }
        ]
      }
    },
    "/api/v2/patient-additional-attributes/metadata": {
      "get": {
        "tags": [
          "PatientAdditionalAttributes"
        ],
        "summary": "Get user defined fields metadata.",
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/UserDefinedFieldMetadataResponseIEnumerableListResponse"
                }
              }
            }
          }
        },
        "security": [
          {
            "ApiKey": [],
            "EdiScheme": []
          }
        ]
      }
    },
    "/api/v2/patient-additional-attributes/{patientId}": {
      "get": {
        "tags": [
          "PatientAdditionalAttributes"
        ],
        "summary": "Get UserDefinedFields by patient id.",
        "parameters": [
          {
            "name": "patientId",
            "in": "path",
            "description": "Unique identifier of a patient.",
            "required": true,
            "schema": {
              "type": "string",
              "format": "uuid"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/StringObjectIDictionaryListResponse"
                }
              }
            }
          }
        },
        "security": [
          {
            "ApiKey": [],
            "EdiScheme": []
          }
        ]
      },
      "patch": {
        "tags": [
          "PatientAdditionalAttributes"
        ],
        "summary": "Update user defined fields by patient id.",
        "parameters": [
          {
            "name": "patientId",
            "in": "path",
            "description": "Unique identifier of a patient.",
            "required": true,
            "schema": {
              "type": "string",
              "format": "uuid"
            }
          }
        ],
        "requestBody": {
          "content": {
            "application/merge-patch+json": {
              "schema": {}
            },
            "application/json": {
              "schema": {}
            },
            "text/json": {
              "schema": {}
            },
            "application/*+json": {
              "schema": {}
            },
            "application/xml": {
              "schema": {}
            },
            "text/xml": {
              "schema": {}
            },
            "application/*+xml": {
              "schema": {}
            }
          }
        },
        "responses": {
          "204": {
            "description": "No Content"
          }
        },
        "security": [
          {
            "ApiKey": [],
            "EdiScheme": []
          }
        ]
      }
    },
    "/api/v2/patients/{patientId}/consents": {
      "get": {
        "tags": [
          "Patients"
        ],
        "summary": "Gets patient consents.",
        "parameters": [
          {
            "name": "patientId",
            "in": "path",
            "description": "Unique identifier of a patient.",
            "required": true,
            "schema": {
              "type": "string",
              "format": "uuid"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/PatientConsentResponseListResponse"
                }
              }
            }
          }
        },
        "security": [
          {
            "ApiKey": [],
            "EdiScheme": []
          }
        ]
      },
      "put": {
        "tags": [
          "Patients"
        ],
        "summary": "Updates patient consent by patient id.",
        "parameters": [
          {
            "name": "patientId",
            "in": "path",
            "description": "Unique identifier of a patient.",
            "required": true,
            "schema": {
              "type": "string",
              "format": "uuid"
            }
          }
        ],
        "requestBody": {
          "description": "Consent payload.",
          "content": {
            "application/merge-patch+json": {
              "schema": {
                "$ref": "#/components/schemas/PatientConsentRequest"
              }
            },
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/PatientConsentRequest"
              }
            },
            "text/json": {
              "schema": {
                "$ref": "#/components/schemas/PatientConsentRequest"
              }
            },
            "application/*+json": {
              "schema": {
                "$ref": "#/components/schemas/PatientConsentRequest"
              }
            },
            "application/xml": {
              "schema": {
                "$ref": "#/components/schemas/PatientConsentRequest"
              }
            },
            "text/xml": {
              "schema": {
                "$ref": "#/components/schemas/PatientConsentRequest"
              }
            },
            "application/*+xml": {
              "schema": {
                "$ref": "#/components/schemas/PatientConsentRequest"
              }
            }
          }
        },
        "responses": {
          "204": {
            "description": "No Content"
          },
          "400": {
            "description": "Bad Request",
            "content": {
              "application/json": {
                "schema": {
                  "oneOf": [
                    {
                      "$ref": "#/components/schemas/ProblemDetails"
                    },
                    {
                      "$ref": "#/components/schemas/HttpValidationProblemDetails"
                    }
                  ]
                }
              }
            }
          },
          "404": {
            "description": "Not Found",
            "content": {
              "application/json": {
                "schema": {
                  "oneOf": [
                    {
                      "$ref": "#/components/schemas/ProblemDetails"
                    },
                    {
                      "$ref": "#/components/schemas/HttpValidationProblemDetails"
                    }
                  ]
                }
              }
            }
          }
        },
        "security": [
          {
            "ApiKey": [],
            "EdiScheme": []
          }
        ]
      }
    },
    "/api/v2/patients/{patientId}/consents/batch": {
      "put": {
        "tags": [
          "Patients"
        ],
        "summary": "Updates patient consents by patient id.",
        "parameters": [
          {
            "name": "patientId",
            "in": "path",
            "description": "Unique identifier of a patient.",
            "required": true,
            "schema": {
              "type": "string",
              "format": "uuid"
            }
          }
        ],
        "requestBody": {
          "description": "Array of Consent payload.",
          "content": {
            "application/merge-patch+json": {
              "schema": {
                "type": "array",
                "items": {
                  "$ref": "#/components/schemas/PatientConsentRequest"
                }
              }
            },
            "application/json": {
              "schema": {
                "type": "array",
                "items": {
                  "$ref": "#/components/schemas/PatientConsentRequest"
                }
              }
            },
            "text/json": {
              "schema": {
                "type": "array",
                "items": {
                  "$ref": "#/components/schemas/PatientConsentRequest"
                }
              }
            },
            "application/*+json": {
              "schema": {
                "type": "array",
                "items": {
                  "$ref": "#/components/schemas/PatientConsentRequest"
                }
              }
            }
          }
        },
        "responses": {
          "204": {
            "description": "No Content"
          },
          "400": {
            "description": "Bad Request",
            "content": {
              "application/json": {
                "schema": {
                  "oneOf": [
                    {
                      "$ref": "#/components/schemas/ProblemDetails"
                    },
                    {
                      "$ref": "#/components/schemas/HttpValidationProblemDetails"
                    }
                  ]
                }
              }
            }
          },
          "404": {
            "description": "Not Found",
            "content": {
              "application/json": {
                "schema": {
                  "oneOf": [
                    {
                      "$ref": "#/components/schemas/ProblemDetails"
                    },
                    {
                      "$ref": "#/components/schemas/HttpValidationProblemDetails"
                    }
                  ]
                }
              }
            }
          }
        },
        "security": [
          {
            "ApiKey": [],
            "EdiScheme": []
          }
        ]
      }
    },
    "/api/v2/patients/genders": {
      "get": {
        "tags": [
          "Patients"
        ],
        "summary": "Get a list of all available patient genders.",
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/ActiveDictionaryResponseIEnumerableListResponse"
                }
              }
            }
          }
        },
        "security": [
          {
            "ApiKey": [],
            "EdiScheme": []
          }
        ]
      }
    },
    "/api/v2/patients/titles": {
      "get": {
        "tags": [
          "Patients"
        ],
        "summary": "Get a list of all available patient titles.",
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/ActiveDictionaryResponseIEnumerableListResponse"
                }
              }
            }
          }
        },
        "security": [
          {
            "ApiKey": [],
            "EdiScheme": []
          }
        ]
      }
    },
    "/api/v2/patients/countries": {
      "get": {
        "tags": [
          "Patients"
        ],
        "summary": "Get a list of all available patient countries.",
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/CountryResponseIEnumerableListResponse"
                }
              }
            }
          }
        },
        "security": [
          {
            "ApiKey": [],
            "EdiScheme": []
          }
        ]
      }
    },
    "/api/v2/patients/employment-statuses": {
      "get": {
        "tags": [
          "Patients"
        ],
        "summary": "Get a list of all available patient employment statuses.",
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/ActiveDictionaryResponseIEnumerableListResponse"
                }
              }
            }
          }
        },
        "security": [
          {
            "ApiKey": [],
            "EdiScheme": []
          }
        ]
      }
    },
    "/api/v2/patients/languages": {
      "get": {
        "tags": [
          "Patients"
        ],
        "summary": "Get a list of all available patient languages.",
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/ActiveDictionaryResponseIEnumerableListResponse"
                }
              }
            }
          }
        },
        "security": [
          {
            "ApiKey": [],
            "EdiScheme": []
          }
        ]
      }
    },
    "/api/v2/patients/patient-statuses": {
      "get": {
        "tags": [
          "Patients"
        ],
        "summary": "Get a list of all available patient statuses.",
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/DictionaryResponseIEnumerableListResponse"
                }
              }
            }
          }
        },
        "security": [
          {
            "ApiKey": [],
            "EdiScheme": []
          }
        ]
      }
    },
    "/api/v2/patients/suffixes": {
      "get": {
        "tags": [
          "Patients"
        ],
        "summary": "Get a list of all available patient suffixes.",
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/ActiveDictionaryResponseIEnumerableListResponse"
                }
              }
            }
          }
        },
        "security": [
          {
            "ApiKey": [],
            "EdiScheme": []
          }
        ]
      }
    },
    "/api/v2/patients/states": {
      "get": {
        "tags": [
          "Patients"
        ],
        "summary": "Get a list of all available states.",
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/ActiveDictionaryResponseIEnumerableListResponse"
                }
              }
            }
          }
        },
        "security": [
          {
            "ApiKey": [],
            "EdiScheme": []
          }
        ]
      }
    },
    "/api/v2/patients": {
      "post": {
        "tags": [
          "Patients"
        ],
        "summary": "Creates new patient.",
        "requestBody": {
          "description": "Patient request body.",
          "content": {
            "application/merge-patch+json": {
              "schema": {
                "oneOf": [
                  {
                    "$ref": "#/components/schemas/CreatePatientRequest"
                  },
                  {
                    "$ref": "#/components/schemas/CreateAUPatientRequest"
                  },
                  {
                    "$ref": "#/components/schemas/CreateNZPatientRequest"
                  },
                  {
                    "$ref": "#/components/schemas/CreateROIPatientRequest"
                  },
                  {
                    "$ref": "#/components/schemas/CreateUKPatientRequest"
                  },
                  {
                    "$ref": "#/components/schemas/CreateUSPatientRequest"
                  }
                ]
              }
            },
            "application/json": {
              "schema": {
                "oneOf": [
                  {
                    "$ref": "#/components/schemas/CreatePatientRequest"
                  },
                  {
                    "$ref": "#/components/schemas/CreateAUPatientRequest"
                  },
                  {
                    "$ref": "#/components/schemas/CreateNZPatientRequest"
                  },
                  {
                    "$ref": "#/components/schemas/CreateROIPatientRequest"
                  },
                  {
                    "$ref": "#/components/schemas/CreateUKPatientRequest"
                  },
                  {
                    "$ref": "#/components/schemas/CreateUSPatientRequest"
                  }
                ]
              }
            },
            "text/json": {
              "schema": {
                "oneOf": [
                  {
                    "$ref": "#/components/schemas/CreatePatientRequest"
                  },
                  {
                    "$ref": "#/components/schemas/CreateAUPatientRequest"
                  },
                  {
                    "$ref": "#/components/schemas/CreateNZPatientRequest"
                  },
                  {
                    "$ref": "#/components/schemas/CreateROIPatientRequest"
                  },
                  {
                    "$ref": "#/components/schemas/CreateUKPatientRequest"
                  },
                  {
                    "$ref": "#/components/schemas/CreateUSPatientRequest"
                  }
                ]
              }
            },
            "application/*+json": {
              "schema": {
                "oneOf": [
                  {
                    "$ref": "#/components/schemas/CreatePatientRequest"
                  },
                  {
                    "$ref": "#/components/schemas/CreateAUPatientRequest"
                  },
                  {
                    "$ref": "#/components/schemas/CreateNZPatientRequest"
                  },
                  {
                    "$ref": "#/components/schemas/CreateROIPatientRequest"
                  },
                  {
                    "$ref": "#/components/schemas/CreateUKPatientRequest"
                  },
                  {
                    "$ref": "#/components/schemas/CreateUSPatientRequest"
                  }
                ]
              }
            },
            "application/xml": {
              "schema": {
                "oneOf": [
                  {
                    "$ref": "#/components/schemas/CreatePatientRequest"
                  },
                  {
                    "$ref": "#/components/schemas/CreateAUPatientRequest"
                  },
                  {
                    "$ref": "#/components/schemas/CreateNZPatientRequest"
                  },
                  {
                    "$ref": "#/components/schemas/CreateROIPatientRequest"
                  },
                  {
                    "$ref": "#/components/schemas/CreateUKPatientRequest"
                  },
                  {
                    "$ref": "#/components/schemas/CreateUSPatientRequest"
                  }
                ]
              }
            },
            "text/xml": {
              "schema": {
                "oneOf": [
                  {
                    "$ref": "#/components/schemas/CreatePatientRequest"
                  },
                  {
                    "$ref": "#/components/schemas/CreateAUPatientRequest"
                  },
                  {
                    "$ref": "#/components/schemas/CreateNZPatientRequest"
                  },
                  {
                    "$ref": "#/components/schemas/CreateROIPatientRequest"
                  },
                  {
                    "$ref": "#/components/schemas/CreateUKPatientRequest"
                  },
                  {
                    "$ref": "#/components/schemas/CreateUSPatientRequest"
                  }
                ]
              }
            },
            "application/*+xml": {
              "schema": {
                "oneOf": [
                  {
                    "$ref": "#/components/schemas/CreatePatientRequest"
                  },
                  {
                    "$ref": "#/components/schemas/CreateAUPatientRequest"
                  },
                  {
                    "$ref": "#/components/schemas/CreateNZPatientRequest"
                  },
                  {
                    "$ref": "#/components/schemas/CreateROIPatientRequest"
                  },
                  {
                    "$ref": "#/components/schemas/CreateUKPatientRequest"
                  },
                  {
                    "$ref": "#/components/schemas/CreateUSPatientRequest"
                  }
                ]
              }
            }
          }
        },
        "responses": {
          "201": {
            "description": "Created",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/CreatedResponse"
                }
              }
            }
          }
        },
        "security": [
          {
            "ApiKey": [],
            "EdiScheme": []
          }
        ]
      }
    },
    "/api/v2/patients/{id}": {
      "put": {
        "tags": [
          "Patients"
        ],
        "summary": "Updates patient information by id.",
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "description": "Patient id.",
            "required": true,
            "schema": {
              "type": "string",
              "format": "uuid"
            }
          }
        ],
        "requestBody": {
          "description": "Patient update payload.",
          "content": {
            "application/merge-patch+json": {
              "schema": {
                "oneOf": [
                  {
                    "$ref": "#/components/schemas/UpdatePatientRequest"
                  },
                  {
                    "$ref": "#/components/schemas/UpdateAUPatientRequest"
                  },
                  {
                    "$ref": "#/components/schemas/UpdateNZPatientRequest"
                  },
                  {
                    "$ref": "#/components/schemas/UpdateROIPatientRequest"
                  },
                  {
                    "$ref": "#/components/schemas/UpdateUKPatientRequest"
                  },
                  {
                    "$ref": "#/components/schemas/UpdateUSPatientRequest"
                  }
                ]
              }
            },
            "application/json": {
              "schema": {
                "oneOf": [
                  {
                    "$ref": "#/components/schemas/UpdatePatientRequest"
                  },
                  {
                    "$ref": "#/components/schemas/UpdateAUPatientRequest"
                  },
                  {
                    "$ref": "#/components/schemas/UpdateNZPatientRequest"
                  },
                  {
                    "$ref": "#/components/schemas/UpdateROIPatientRequest"
                  },
                  {
                    "$ref": "#/components/schemas/UpdateUKPatientRequest"
                  },
                  {
                    "$ref": "#/components/schemas/UpdateUSPatientRequest"
                  }
                ]
              }
            },
            "text/json": {
              "schema": {
                "oneOf": [
                  {
                    "$ref": "#/components/schemas/UpdatePatientRequest"
                  },
                  {
                    "$ref": "#/components/schemas/UpdateAUPatientRequest"
                  },
                  {
                    "$ref": "#/components/schemas/UpdateNZPatientRequest"
                  },
                  {
                    "$ref": "#/components/schemas/UpdateROIPatientRequest"
                  },
                  {
                    "$ref": "#/components/schemas/UpdateUKPatientRequest"
                  },
                  {
                    "$ref": "#/components/schemas/UpdateUSPatientRequest"
                  }
                ]
              }
            },
            "application/*+json": {
              "schema": {
                "oneOf": [
                  {
                    "$ref": "#/components/schemas/UpdatePatientRequest"
                  },
                  {
                    "$ref": "#/components/schemas/UpdateAUPatientRequest"
                  },
                  {
                    "$ref": "#/components/schemas/UpdateNZPatientRequest"
                  },
                  {
                    "$ref": "#/components/schemas/UpdateROIPatientRequest"
                  },
                  {
                    "$ref": "#/components/schemas/UpdateUKPatientRequest"
                  },
                  {
                    "$ref": "#/components/schemas/UpdateUSPatientRequest"
                  }
                ]
              }
            },
            "application/xml": {
              "schema": {
                "oneOf": [
                  {
                    "$ref": "#/components/schemas/UpdatePatientRequest"
                  },
                  {
                    "$ref": "#/components/schemas/UpdateAUPatientRequest"
                  },
                  {
                    "$ref": "#/components/schemas/UpdateNZPatientRequest"
                  },
                  {
                    "$ref": "#/components/schemas/UpdateROIPatientRequest"
                  },
                  {
                    "$ref": "#/components/schemas/UpdateUKPatientRequest"
                  },
                  {
                    "$ref": "#/components/schemas/UpdateUSPatientRequest"
                  }
                ]
              }
            },
            "text/xml": {
              "schema": {
                "oneOf": [
                  {
                    "$ref": "#/components/schemas/UpdatePatientRequest"
                  },
                  {
                    "$ref": "#/components/schemas/UpdateAUPatientRequest"
                  },
                  {
                    "$ref": "#/components/schemas/UpdateNZPatientRequest"
                  },
                  {
                    "$ref": "#/components/schemas/UpdateROIPatientRequest"
                  },
                  {
                    "$ref": "#/components/schemas/UpdateUKPatientRequest"
                  },
                  {
                    "$ref": "#/components/schemas/UpdateUSPatientRequest"
                  }
                ]
              }
            },
            "application/*+xml": {
              "schema": {
                "oneOf": [
                  {
                    "$ref": "#/components/schemas/UpdatePatientRequest"
                  },
                  {
                    "$ref": "#/components/schemas/UpdateAUPatientRequest"
                  },
                  {
                    "$ref": "#/components/schemas/UpdateNZPatientRequest"
                  },
                  {
                    "$ref": "#/components/schemas/UpdateROIPatientRequest"
                  },
                  {
                    "$ref": "#/components/schemas/UpdateUKPatientRequest"
                  },
                  {
                    "$ref": "#/components/schemas/UpdateUSPatientRequest"
                  }
                ]
              }
            }
          }
        },
        "responses": {
          "204": {
            "description": "No Content"
          }
        },
        "security": [
          {
            "ApiKey": [],
            "EdiScheme": []
          }
        ]
      },
      "patch": {
        "tags": [
          "Patients"
        ],
        "summary": "Partially updates Patient. If property not passed, it is skipped for update. Only passed properties are updated.",
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "description": "Patient unique identifier.",
            "required": true,
            "schema": {
              "type": "string",
              "format": "uuid"
            }
          }
        ],
        "requestBody": {
          "description": "PATCH Patient request.",
          "content": {
            "application/merge-patch+json": {
              "schema": {
                "oneOf": [
                  {
                    "$ref": "#/components/schemas/UpdatePatientRequest"
                  },
                  {
                    "$ref": "#/components/schemas/UpdateAUPatientRequest"
                  },
                  {
                    "$ref": "#/components/schemas/UpdateNZPatientRequest"
                  },
                  {
                    "$ref": "#/components/schemas/UpdateROIPatientRequest"
                  },
                  {
                    "$ref": "#/components/schemas/UpdateUKPatientRequest"
                  },
                  {
                    "$ref": "#/components/schemas/UpdateUSPatientRequest"
                  }
                ]
              }
            }
          }
        },
        "responses": {
          "204": {
            "description": "No Content"
          }
        },
        "security": [
          {
            "ApiKey": [],
            "EdiScheme": []
          }
        ]
      }
    },
    "/api/v2/payments/methods": {
      "get": {
        "tags": [
          "Payments"
        ],
        "summary": "Get a list of all available payment methods.",
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/DictionaryResponseIEnumerableListResponse"
                }
              }
            }
          }
        },
        "security": [
          {
            "ApiKey": [],
            "EdiScheme": []
          }
        ]
      }
    },
    "/api/v2/payments/methods/{id}": {
      "get": {
        "tags": [
          "Payments"
        ],
        "summary": "Get a Payment Method by Id.",
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "description": "Unique identifier of Payment method.",
            "required": true,
            "schema": {
              "type": "string",
              "format": "uuid"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "application/json": {
                "schema": {
                  "oneOf": [
                    {
                      "$ref": "#/components/schemas/DictionaryResponse"
                    },
                    {
                      "$ref": "#/components/schemas/CountryResponse"
                    }
                  ]
                }
              }
            }
          },
          "404": {
            "description": "Not Found",
            "content": {
              "application/json": {
                "schema": {
                  "oneOf": [
                    {
                      "$ref": "#/components/schemas/ProblemDetails"
                    },
                    {
                      "$ref": "#/components/schemas/HttpValidationProblemDetails"
                    }
                  ]
                }
              }
            }
          }
        },
        "security": [
          {
            "ApiKey": [],
            "EdiScheme": []
          }
        ]
      }
    },
    "/api/v{version}/payjunction/webhooks/terminal-request": {
      "post": {
        "tags": [
          "PaymentWebhook"
        ],
        "parameters": [
          {
            "name": "version",
            "in": "path",
            "required": true,
            "schema": {
              "type": "string"
            }
          }
        ],
        "requestBody": {
          "content": {
            "application/merge-patch+json": {
              "schema": {
                "$ref": "#/components/schemas/ProcessPaymentRequest"
              }
            },
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/ProcessPaymentRequest"
              }
            },
            "text/json": {
              "schema": {
                "$ref": "#/components/schemas/ProcessPaymentRequest"
              }
            },
            "application/*+json": {
              "schema": {
                "$ref": "#/components/schemas/ProcessPaymentRequest"
              }
            },
            "application/xml": {
              "schema": {
                "$ref": "#/components/schemas/ProcessPaymentRequest"
              }
            },
            "text/xml": {
              "schema": {
                "$ref": "#/components/schemas/ProcessPaymentRequest"
              }
            },
            "application/*+xml": {
              "schema": {
                "$ref": "#/components/schemas/ProcessPaymentRequest"
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "OK"
          }
        },
        "security": [
          {
            "ApiKey": [],
            "EdiScheme": []
          }
        ]
      }
    },
    "/api/v2/pricing/taxes": {
      "get": {
        "tags": [
          "Pricing"
        ],
        "summary": "Get a list of all taxes.",
        "parameters": [
          {
            "name": "Page",
            "in": "query",
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          },
          {
            "name": "PerPage",
            "in": "query",
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/TaxResponsePagedResponse"
                }
              }
            }
          }
        },
        "security": [
          {
            "ApiKey": [],
            "EdiScheme": []
          }
        ]
      },
      "post": {
        "tags": [
          "Pricing"
        ],
        "summary": "Creates a new Tax.",
        "requestBody": {
          "description": "Tax payload.",
          "content": {
            "application/merge-patch+json": {
              "schema": {
                "$ref": "#/components/schemas/TaxRequest"
              }
            },
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/TaxRequest"
              }
            },
            "text/json": {
              "schema": {
                "$ref": "#/components/schemas/TaxRequest"
              }
            },
            "application/*+json": {
              "schema": {
                "$ref": "#/components/schemas/TaxRequest"
              }
            },
            "application/xml": {
              "schema": {
                "$ref": "#/components/schemas/TaxRequest"
              }
            },
            "text/xml": {
              "schema": {
                "$ref": "#/components/schemas/TaxRequest"
              }
            },
            "application/*+xml": {
              "schema": {
                "$ref": "#/components/schemas/TaxRequest"
              }
            }
          }
        },
        "responses": {
          "201": {
            "description": "Created",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/CreatedResponse"
                }
              }
            }
          },
          "400": {
            "description": "Bad Request",
            "content": {
              "application/json": {
                "schema": {
                  "oneOf": [
                    {
                      "$ref": "#/components/schemas/ProblemDetails"
                    },
                    {
                      "$ref": "#/components/schemas/HttpValidationProblemDetails"
                    }
                  ]
                }
              }
            }
          }
        },
        "security": [
          {
            "ApiKey": [],
            "EdiScheme": []
          }
        ]
      }
    },
    "/api/v2/pricing/taxes/{id}": {
      "get": {
        "tags": [
          "Pricing"
        ],
        "summary": "Gets a tax by id.",
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "description": "Unique identifier of a tax.",
            "required": true,
            "schema": {
              "type": "string",
              "format": "uuid"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/TaxResponse"
                }
              }
            }
          },
          "404": {
            "description": "Not Found",
            "content": {
              "application/json": {
                "schema": {
                  "oneOf": [
                    {
                      "$ref": "#/components/schemas/ProblemDetails"
                    },
                    {
                      "$ref": "#/components/schemas/HttpValidationProblemDetails"
                    }
                  ]
                }
              }
            }
          }
        },
        "security": [
          {
            "ApiKey": [],
            "EdiScheme": []
          }
        ]
      },
      "put": {
        "tags": [
          "Pricing"
        ],
        "summary": "Updates an existing Tax by id.",
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "description": "Unique identifier of a tax.",
            "required": true,
            "schema": {
              "type": "string",
              "format": "uuid"
            }
          }
        ],
        "requestBody": {
          "description": "Tax payload.",
          "content": {
            "application/merge-patch+json": {
              "schema": {
                "$ref": "#/components/schemas/TaxRequest"
              }
            },
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/TaxRequest"
              }
            },
            "text/json": {
              "schema": {
                "$ref": "#/components/schemas/TaxRequest"
              }
            },
            "application/*+json": {
              "schema": {
                "$ref": "#/components/schemas/TaxRequest"
              }
            },
            "application/xml": {
              "schema": {
                "$ref": "#/components/schemas/TaxRequest"
              }
            },
            "text/xml": {
              "schema": {
                "$ref": "#/components/schemas/TaxRequest"
              }
            },
            "application/*+xml": {
              "schema": {
                "$ref": "#/components/schemas/TaxRequest"
              }
            }
          }
        },
        "responses": {
          "204": {
            "description": "No Content"
          },
          "400": {
            "description": "Bad Request",
            "content": {
              "application/json": {
                "schema": {
                  "oneOf": [
                    {
                      "$ref": "#/components/schemas/ProblemDetails"
                    },
                    {
                      "$ref": "#/components/schemas/HttpValidationProblemDetails"
                    }
                  ]
                }
              }
            }
          },
          "404": {
            "description": "Not Found",
            "content": {
              "application/json": {
                "schema": {
                  "oneOf": [
                    {
                      "$ref": "#/components/schemas/ProblemDetails"
                    },
                    {
                      "$ref": "#/components/schemas/HttpValidationProblemDetails"
                    }
                  ]
                }
              }
            }
          }
        },
        "security": [
          {
            "ApiKey": [],
            "EdiScheme": []
          }
        ]
      }
    },
    "/api/v2/referrals/general-practitioners": {
      "get": {
        "tags": [
          "Referrals"
        ],
        "summary": "Gets a paged list of General Practitioners.",
        "parameters": [
          {
            "name": "Page",
            "in": "query",
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          },
          {
            "name": "PerPage",
            "in": "query",
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/GeneralPractitionerResponsePagingResponse"
                }
              }
            }
          }
        },
        "security": [
          {
            "ApiKey": [],
            "EdiScheme": []
          }
        ]
      }
    },
    "/api/v2/referrals/practices": {
      "get": {
        "tags": [
          "Referrals"
        ],
        "summary": "Gets a paged list of Practices.",
        "parameters": [
          {
            "name": "Page",
            "in": "query",
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          },
          {
            "name": "PerPage",
            "in": "query",
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/PracticeResponsePagingResponse"
                }
              }
            }
          }
        },
        "security": [
          {
            "ApiKey": [],
            "EdiScheme": []
          }
        ]
      }
    },
    "/api/v2/repairs/lnd-settings": {
      "get": {
        "tags": [
          "Repairs"
        ],
        "summary": "Get LnD settings.",
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "$ref": "#/components/schemas/LnDSettingsResponse"
                }
              },
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/LnDSettingsResponse"
                }
              },
              "text/json": {
                "schema": {
                  "$ref": "#/components/schemas/LnDSettingsResponse"
                }
              }
            }
          }
        },
        "security": [
          {
            "ApiKey": [],
            "EdiScheme": []
          }
        ]
      }
    },
    "/api/v2/repairs/lnd-reasons": {
      "get": {
        "tags": [
          "Repairs"
        ],
        "summary": "Get a list of LnD reasons.",
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "$ref": "#/components/schemas/LnDReasonResponseIEnumerableListResponse"
                }
              },
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/LnDReasonResponseIEnumerableListResponse"
                }
              },
              "text/json": {
                "schema": {
                  "$ref": "#/components/schemas/LnDReasonResponseIEnumerableListResponse"
                }
              }
            }
          }
        },
        "security": [
          {
            "ApiKey": [],
            "EdiScheme": []
          }
        ]
      }
    },
    "/api/v2/repairs/lnd-statuses": {
      "get": {
        "tags": [
          "Repairs"
        ],
        "summary": "Get a list of LnD statuses.",
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "$ref": "#/components/schemas/LnDStatusResponseIEnumerableListResponse"
                }
              },
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/LnDStatusResponseIEnumerableListResponse"
                }
              },
              "text/json": {
                "schema": {
                  "$ref": "#/components/schemas/LnDStatusResponseIEnumerableListResponse"
                }
              }
            }
          }
        },
        "security": [
          {
            "ApiKey": [],
            "EdiScheme": []
          }
        ]
      }
    },
    "/api/v2/repairs/repair-order-statuses": {
      "get": {
        "tags": [
          "Repairs"
        ],
        "summary": "Get a list of repair order statuses.",
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "$ref": "#/components/schemas/RepairOrderStatusResponseIEnumerableListResponse"
                }
              },
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/RepairOrderStatusResponseIEnumerableListResponse"
                }
              },
              "text/json": {
                "schema": {
                  "$ref": "#/components/schemas/RepairOrderStatusResponseIEnumerableListResponse"
                }
              }
            }
          }
        },
        "security": [
          {
            "ApiKey": [],
            "EdiScheme": []
          }
        ]
      }
    },
    "/api/v2/repairs/repair-reasons": {
      "get": {
        "tags": [
          "Repairs"
        ],
        "summary": "Get a list of repair reasons.",
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "$ref": "#/components/schemas/RepairReasonResponseIEnumerableListResponse"
                }
              },
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/RepairReasonResponseIEnumerableListResponse"
                }
              },
              "text/json": {
                "schema": {
                  "$ref": "#/components/schemas/RepairReasonResponseIEnumerableListResponse"
                }
              }
            }
          }
        },
        "security": [
          {
            "ApiKey": [],
            "EdiScheme": []
          }
        ]
      }
    },
    "/api/v2/scheduler/appointments/{id}": {
      "get": {
        "tags": [
          "Scheduler"
        ],
        "summary": "Get an appointment by id.",
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "description": "Id of the appointment.",
            "required": true,
            "schema": {
              "type": "string",
              "format": "uuid"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/GetAppointmentResponse"
                }
              }
            }
          }
        },
        "security": [
          {
            "ApiKey": [],
            "EdiScheme": []
          }
        ]
      },
      "put": {
        "tags": [
          "Scheduler"
        ],
        "summary": "Updates an existing Appointment with a given body.",
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "description": "Id of the appointment.",
            "required": true,
            "schema": {
              "type": "string",
              "format": "uuid"
            }
          }
        ],
        "requestBody": {
          "description": "Appointment payload.",
          "content": {
            "application/merge-patch+json": {
              "schema": {
                "$ref": "#/components/schemas/AppointmentRequest"
              }
            },
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/AppointmentRequest"
              }
            },
            "text/json": {
              "schema": {
                "$ref": "#/components/schemas/AppointmentRequest"
              }
            },
            "application/*+json": {
              "schema": {
                "$ref": "#/components/schemas/AppointmentRequest"
              }
            }
          }
        },
        "responses": {
          "204": {
            "description": "No Content"
          },
          "404": {
            "description": "Not Found",
            "content": {
              "application/json": {
                "schema": {
                  "oneOf": [
                    {
                      "$ref": "#/components/schemas/ProblemDetails"
                    },
                    {
                      "$ref": "#/components/schemas/HttpValidationProblemDetails"
                    }
                  ]
                }
              }
            }
          }
        },
        "security": [
          {
            "ApiKey": [],
            "EdiScheme": []
          }
        ]
      },
      "patch": {
        "tags": [
          "Scheduler"
        ],
        "summary": "Partially updates an Appointment.",
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "description": "Unique identifier of Appointment.",
            "required": true,
            "schema": {
              "type": "string",
              "format": "uuid"
            }
          }
        ],
        "requestBody": {
          "description": "Patch payload.",
          "content": {
            "application/merge-patch+json": {
              "schema": {
                "$ref": "#/components/schemas/PatchAppointmentRequest"
              }
            }
          }
        },
        "responses": {
          "204": {
            "description": "No Content"
          },
          "404": {
            "description": "Not Found",
            "content": {
              "application/json": {
                "schema": {
                  "oneOf": [
                    {
                      "$ref": "#/components/schemas/ProblemDetails"
                    },
                    {
                      "$ref": "#/components/schemas/HttpValidationProblemDetails"
                    }
                  ]
                }
              }
            }
          }
        },
        "security": [
          {
            "ApiKey": [],
            "EdiScheme": []
          }
        ]
      }
    },
    "/api/v2/scheduler/appointments": {
      "post": {
        "tags": [
          "Scheduler"
        ],
        "summary": "Creates a new Appointment with a given body.",
        "requestBody": {
          "description": "Appointment payload.",
          "content": {
            "application/merge-patch+json": {
              "schema": {
                "$ref": "#/components/schemas/AppointmentRequest"
              }
            },
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/AppointmentRequest"
              }
            },
            "text/json": {
              "schema": {
                "$ref": "#/components/schemas/AppointmentRequest"
              }
            },
            "application/*+json": {
              "schema": {
                "$ref": "#/components/schemas/AppointmentRequest"
              }
            }
          }
        },
        "responses": {
          "201": {
            "description": "Created",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/CreatedResponse"
                }
              }
            }
          }
        },
        "security": [
          {
            "ApiKey": [],
            "EdiScheme": []
          }
        ]
      }
    },
    "/api/v2/scheduler/appointment-statuses": {
      "get": {
        "tags": [
          "Scheduler"
        ],
        "summary": "Get a list of all available appointments statuses.",
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/AppointmentStatusesResponseIEnumerableListResponse"
                }
              }
            }
          }
        },
        "security": [
          {
            "ApiKey": [],
            "EdiScheme": []
          }
        ]
      }
    },
    "/api/v2/scheduler/appointment-outcomes": {
      "get": {
        "tags": [
          "Scheduler"
        ],
        "summary": "Get a list of all available appointments outcomes.",
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/AppointmentOutcomeResponseIEnumerableListResponse"
                }
              }
            }
          }
        },
        "security": [
          {
            "ApiKey": [],
            "EdiScheme": []
          }
        ]
      }
    },
    "/api/v2/scheduler/appointments/cancellation-reasons": {
      "get": {
        "tags": [
          "Scheduler"
        ],
        "summary": "Get a list of all available appointment cancellation reasons.",
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/ActiveDictionaryResponseIEnumerableListResponse"
                }
              }
            }
          }
        },
        "security": [
          {
            "ApiKey": [],
            "EdiScheme": []
          }
        ]
      }
    },
    "/api/v2/scheduler/appointments/reason-sources": {
      "get": {
        "tags": [
          "Scheduler"
        ],
        "summary": "Get a list of all available appointment reason sources.",
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/ActiveDictionaryResponseIEnumerableListResponse"
                }
              }
            }
          }
        },
        "security": [
          {
            "ApiKey": [],
            "EdiScheme": []
          }
        ]
      }
    },
    "/api/v2/scheduler/confirmation-types": {
      "get": {
        "tags": [
          "Scheduler"
        ],
        "summary": "Get a list of all confirmation types.",
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/ActiveDictionaryResponseIEnumerableListResponse"
                }
              }
            }
          }
        },
        "security": [
          {
            "ApiKey": [],
            "EdiScheme": []
          }
        ]
      }
    },
    "/api/v2/scheduler/cancellation-reasons": {
      "get": {
        "tags": [
          "Scheduler"
        ],
        "summary": "Returns a list of all cancellation reasons.",
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/CancellationReasonResponseListResponse"
                }
              }
            }
          }
        },
        "security": [
          {
            "ApiKey": [],
            "EdiScheme": []
          }
        ]
      }
    },
    "/api/v2/scheduler/reschedule-reasons": {
      "get": {
        "tags": [
          "Scheduler"
        ],
        "summary": "Get a list of all reschedule-reasons.",
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/ActiveDictionaryResponseIEnumerableListResponse"
                }
              }
            }
          }
        },
        "security": [
          {
            "ApiKey": [],
            "EdiScheme": []
          }
        ]
      }
    },
    "/api/v2/scheduler/resource-types": {
      "get": {
        "tags": [
          "Scheduler"
        ],
        "summary": "Returns a list of all resource types.",
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/ResourceTypeResponseListResponse"
                }
              }
            }
          }
        },
        "security": [
          {
            "ApiKey": [],
            "EdiScheme": []
          }
        ]
      }
    },
    "/api/v2/scheduler/resources": {
      "get": {
        "tags": [
          "Scheduler"
        ],
        "summary": "Returns paged list of resources, filtered by locationId.",
        "parameters": [
          {
            "name": "Page",
            "in": "query",
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          },
          {
            "name": "PerPage",
            "in": "query",
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          },
          {
            "name": "locationId",
            "in": "query",
            "description": "Location unique identifier.",
            "schema": {
              "type": "string",
              "format": "uuid"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/ResourceResponsePagedResponse"
                }
              }
            }
          }
        },
        "security": [
          {
            "ApiKey": [],
            "EdiScheme": []
          }
        ]
      }
    },
    "/api/v2/scheduler/specialists": {
      "get": {
        "tags": [
          "Scheduler"
        ],
        "summary": "Returns a list of all specialists. Supports paging.",
        "parameters": [
          {
            "name": "Page",
            "in": "query",
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          },
          {
            "name": "PerPage",
            "in": "query",
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          },
          {
            "name": "locationId",
            "in": "query",
            "description": "Optional. Unique identifier of a location to filter by.",
            "schema": {
              "type": "string",
              "format": "uuid"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/SpecialistResponsePagingResponse"
                }
              }
            }
          }
        },
        "security": [
          {
            "ApiKey": [],
            "EdiScheme": []
          }
        ]
      }
    },
    "/api/v2/scheduler/specialists/{id}": {
      "get": {
        "tags": [
          "Scheduler"
        ],
        "summary": "Returns a specialist info by id.",
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "description": "Unique identifier of a specialist.",
            "required": true,
            "schema": {
              "type": "string",
              "format": "uuid"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/SpecialistResponse"
                }
              }
            }
          }
        },
        "security": [
          {
            "ApiKey": [],
            "EdiScheme": []
          }
        ]
      }
    },
    "/api/v2/online-booking/epos/{eposNumber}/timeslots": {
      "get": {
        "tags": [
          "Timeslots"
        ],
        "parameters": [
          {
            "name": "eposNumber",
            "in": "path",
            "required": true,
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "appointmentReasonId",
            "in": "query",
            "required": true,
            "schema": {
              "type": "string",
              "format": "uuid"
            }
          },
          {
            "name": "startDate",
            "in": "query",
            "required": true,
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          },
          {
            "name": "numberOfDays",
            "in": "query",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "$ref": "#/components/schemas/TimeslotsAvailabilityResponse"
                }
              },
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/TimeslotsAvailabilityResponse"
                }
              },
              "text/json": {
                "schema": {
                  "$ref": "#/components/schemas/TimeslotsAvailabilityResponse"
                }
              },
              "application/xml": {
                "schema": {
                  "$ref": "#/components/schemas/TimeslotsAvailabilityResponse"
                }
              },
              "text/xml": {
                "schema": {
                  "$ref": "#/components/schemas/TimeslotsAvailabilityResponse"
                }
              }
            }
          }
        },
        "security": [
          {
            "ApiKey": [],
            "EdiScheme": []
          }
        ]
      }
    },
    "/api/v2/online-booking/locations/{locationId}/timeslots": {
      "get": {
        "tags": [
          "Timeslots"
        ],
        "parameters": [
          {
            "name": "locationId",
            "in": "path",
            "required": true,
            "schema": {
              "type": "string",
              "format": "uuid"
            }
          },
          {
            "name": "appointmentReasonId",
            "in": "query",
            "required": true,
            "schema": {
              "type": "string",
              "format": "uuid"
            }
          },
          {
            "name": "startDate",
            "in": "query",
            "required": true,
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          },
          {
            "name": "numberOfDays",
            "in": "query",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "$ref": "#/components/schemas/TimeslotsAvailabilityResponse"
                }
              },
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/TimeslotsAvailabilityResponse"
                }
              },
              "text/json": {
                "schema": {
                  "$ref": "#/components/schemas/TimeslotsAvailabilityResponse"
                }
              },
              "application/xml": {
                "schema": {
                  "$ref": "#/components/schemas/TimeslotsAvailabilityResponse"
                }
              },
              "text/xml": {
                "schema": {
                  "$ref": "#/components/schemas/TimeslotsAvailabilityResponse"
                }
              }
            }
          }
        },
        "security": [
          {
            "ApiKey": [],
            "EdiScheme": []
          }
        ]
      }
    },
    "/api/v2/online-booking/locations/{locationId}/specialists/timeslots": {
      "get": {
        "tags": [
          "Timeslots"
        ],
        "summary": "Returns list of available time slots with a Specialist available.",
        "parameters": [
          {
            "name": "locationId",
            "in": "path",
            "description": "Unique identifier of a Location.",
            "required": true,
            "schema": {
              "type": "string",
              "format": "uuid"
            }
          },
          {
            "name": "appointmentReasonId",
            "in": "query",
            "description": "Reason for an appointement.",
            "required": true,
            "schema": {
              "type": "string",
              "format": "uuid"
            }
          },
          {
            "name": "startDate",
            "in": "query",
            "description": "Time slot search Start date.",
            "required": true,
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          },
          {
            "name": "numberOfDays",
            "in": "query",
            "description": "Days to search.",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "$ref": "#/components/schemas/LocationSpecialistTimeSlotsResponse"
                }
              },
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/LocationSpecialistTimeSlotsResponse"
                }
              },
              "text/json": {
                "schema": {
                  "$ref": "#/components/schemas/LocationSpecialistTimeSlotsResponse"
                }
              }
            }
          }
        },
        "security": [
          {
            "ApiKey": [],
            "EdiScheme": []
          }
        ]
      }
    },
    "/api/v2/online-booking/epos/{epos}/specialists/timeslots": {
      "get": {
        "tags": [
          "Timeslots"
        ],
        "summary": "Returns list of available time slots with a Specialist available.",
        "parameters": [
          {
            "name": "epos",
            "in": "path",
            "description": "EPOS number of a location.",
            "required": true,
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "appointmentReasonId",
            "in": "query",
            "description": "Reason for an appointement.",
            "required": true,
            "schema": {
              "type": "string",
              "format": "uuid"
            }
          },
          {
            "name": "startDate",
            "in": "query",
            "description": "Time slot search Start date.",
            "required": true,
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          },
          {
            "name": "numberOfDays",
            "in": "query",
            "description": "Days to search.",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "$ref": "#/components/schemas/LocationSpecialistTimeSlotsResponse"
                }
              },
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/LocationSpecialistTimeSlotsResponse"
                }
              },
              "text/json": {
                "schema": {
                  "$ref": "#/components/schemas/LocationSpecialistTimeSlotsResponse"
                }
              }
            }
          }
        },
        "security": [
          {
            "ApiKey": [],
            "EdiScheme": []
          }
        ]
      }
    }
  },
  "components": {
    "schemas": {
      "AUProductRequest": {
        "type": "object",
        "allOf": [
          {
            "$ref": "#/components/schemas/ProductRequest"
          }
        ],
        "additionalProperties": false
      },
      "AcceptLineItemRequest": {
        "type": "object",
        "properties": {
          "invoiceReferenceNumber": {
            "type": "string",
            "nullable": true
          },
          "serialNumbers": {
            "type": "array",
            "items": {
              "type": "string"
            },
            "nullable": true
          }
        },
        "additionalProperties": false
      },
      "AcceptOrderLineItemRequest": {
        "type": "object",
        "properties": {
          "id": {
            "type": "string",
            "format": "uuid"
          },
          "serialNumbers": {
            "type": "array",
            "items": {
              "type": "string"
            },
            "nullable": true
          }
        },
        "additionalProperties": false
      },
      "AcceptOrderRequest": {
        "type": "object",
        "properties": {
          "invoiceReferenceNumber": {
            "type": "string",
            "nullable": true
          },
          "lineItems": {
            "type": "array",
            "items": {
              "$ref": "#/components/schemas/AcceptOrderLineItemRequest"
            }
          }
        },
        "additionalProperties": false
      },
      "ActiveDictionaryResponse": {
        "type": "object",
        "properties": {
          "id": {
            "type": "string",
            "format": "uuid"
          },
          "name": {
            "type": "string"
          },
          "isActive": {
            "type": "boolean"
          }
        },
        "additionalProperties": false
      },
      "ActiveDictionaryResponseIEnumerableListResponse": {
        "type": "object",
        "properties": {
          "data": {
            "type": "array",
            "items": {
              "$ref": "#/components/schemas/ActiveDictionaryResponse"
            },
            "nullable": true
          }
        },
        "additionalProperties": false
      },
      "ActiveDictionaryResponsePagingResponse": {
        "type": "object",
        "properties": {
          "data": {
            "type": "array",
            "items": {
              "$ref": "#/components/schemas/ActiveDictionaryResponse"
            },
            "nullable": true
          },
          "paginagion": {
            "$ref": "#/components/schemas/PagingOptions"
          }
        },
        "additionalProperties": false
      },
      "AdjustNotSerializedProductRequest": {
        "type": "object",
        "properties": {
          "quantity": {
            "type": "integer",
            "format": "int32"
          }
        },
        "additionalProperties": false
      },
      "AdjustSerializedProductRequest": {
        "type": "object",
        "properties": {
          "serialNumbers": {
            "type": "array",
            "items": {
              "type": "string"
            }
          },
          "batteryTypeId": {
            "type": "string",
            "format": "uuid",
            "nullable": true
          },
          "colorId": {
            "type": "string",
            "format": "uuid",
            "nullable": true
          },
          "attributes": {
            "type": "array",
            "items": {
              "$ref": "#/components/schemas/ProductItemAttributeRequest"
            }
          }
        },
        "additionalProperties": false
      },
      "Appointment": {
        "required": [
          "appointmentId",
          "appointmentReasonId",
          "endDateTime",
          "epos",
          "startDateTime"
        ],
        "type": "object",
        "properties": {
          "appointmentId": {
            "type": "string",
            "format": "uuid"
          },
          "startDateTime": {
            "type": "string",
            "format": "date-time"
          },
          "endDateTime": {
            "type": "string",
            "format": "date-time"
          },
          "epos": {
            "minLength": 1,
            "type": "string"
          },
          "appointmentReasonId": {
            "type": "string",
            "format": "uuid"
          },
          "patientId": {
            "type": "string",
            "format": "uuid"
          },
          "resourceIds": {
            "type": "array",
            "items": {
              "type": "string",
              "format": "uuid"
            }
          }
        },
        "additionalProperties": false
      },
      "AppointmentOutcomeResponse": {
        "type": "object",
        "allOf": [
          {
            "$ref": "#/components/schemas/BaseCodeResponse"
          }
        ],
        "properties": {
          "isActive": {
            "type": "boolean"
          },
          "rating": {
            "type": "string",
            "nullable": true
          }
        },
        "additionalProperties": false
      },
      "AppointmentOutcomeResponseIEnumerableListResponse": {
        "type": "object",
        "properties": {
          "data": {
            "type": "array",
            "items": {
              "$ref": "#/components/schemas/AppointmentOutcomeResponse"
            },
            "nullable": true
          }
        },
        "additionalProperties": false
      },
      "AppointmentRequest": {
        "type": "object",
        "allOf": [
          {
            "$ref": "#/components/schemas/BaseAppointmentRequest"
          }
        ],
        "properties": {
          "specialists": {
            "type": "array",
            "items": {
              "type": "string",
              "format": "uuid"
            }
          },
          "resources": {
            "type": "array",
            "items": {
              "type": "string",
              "format": "uuid"
            }
          }
        },
        "additionalProperties": false
      },
      "AppointmentStatusesResponse": {
        "type": "object",
        "allOf": [
          {
            "$ref": "#/components/schemas/BaseCodeResponse"
          }
        ],
        "properties": {
          "isActive": {
            "type": "boolean"
          },
          "description": {
            "type": "string"
          }
        },
        "additionalProperties": false
      },
      "AppointmentStatusesResponseIEnumerableListResponse": {
        "type": "object",
        "properties": {
          "data": {
            "type": "array",
            "items": {
              "$ref": "#/components/schemas/AppointmentStatusesResponse"
            },
            "nullable": true
          }
        },
        "additionalProperties": false
      },
      "AppointmentWithLocation": {
        "required": [
          "appointmentId",
          "appointmentReasonId",
          "endDateTime",
          "locationId",
          "startDateTime"
        ],
        "type": "object",
        "properties": {
          "appointmentId": {
            "type": "string",
            "format": "uuid"
          },
          "startDateTime": {
            "type": "string",
            "format": "date-time"
          },
          "endDateTime": {
            "type": "string",
            "format": "date-time"
          },
          "locationId": {
            "type": "string",
            "format": "uuid"
          },
          "appointmentReasonId": {
            "type": "string",
            "format": "uuid"
          },
          "patientId": {
            "type": "string",
            "format": "uuid"
          },
          "resourceIds": {
            "type": "array",
            "items": {
              "type": "string",
              "format": "uuid"
            }
          }
        },
        "additionalProperties": false
      },
      "AssignSkuConfigRequest": {
        "type": "object",
        "properties": {
          "useBattery": {
            "type": "boolean"
          },
          "useColor": {
            "type": "boolean"
          },
          "attributes": {
            "type": "array",
            "items": {
              "$ref": "#/components/schemas/SkuConfigAttribute"
            }
          }
        },
        "additionalProperties": false
      },
      "AssignSkuRequest": {
        "type": "object",
        "properties": {
          "sku": {
            "type": "string"
          },
          "colorId": {
            "type": "string",
            "format": "uuid",
            "nullable": true
          },
          "batteryId": {
            "type": "string",
            "format": "uuid",
            "nullable": true
          },
          "attributes": {
            "type": "array",
            "items": {
              "$ref": "#/components/schemas/ExternalSkuAttribute"
            }
          }
        },
        "additionalProperties": false
      },
      "AttributeRequest": {
        "type": "object",
        "properties": {
          "name": {
            "type": "string"
          },
          "isActive": {
            "type": "boolean"
          },
          "productCategories": {
            "type": "array",
            "items": {
              "type": "string",
              "format": "uuid"
            }
          },
          "values": {
            "type": "array",
            "items": {
              "$ref": "#/components/schemas/AttributeValueRequest"
            }
          }
        },
        "additionalProperties": false
      },
      "AttributeResponse": {
        "type": "object",
        "properties": {
          "id": {
            "type": "string",
            "format": "uuid"
          },
          "name": {
            "type": "string"
          },
          "isActive": {
            "type": "boolean"
          },
          "values": {
            "type": "array",
            "items": {
              "$ref": "#/components/schemas/AttributeValueResponse"
            }
          },
          "productCategories": {
            "type": "array",
            "items": {
              "$ref": "#/components/schemas/ProductCategoryResponse"
            }
          }
        },
        "additionalProperties": false
      },
      "AttributeResponseIEnumerableListResponse": {
        "type": "object",
        "properties": {
          "data": {
            "type": "array",
            "items": {
              "$ref": "#/components/schemas/AttributeResponse"
            },
            "nullable": true
          }
        },
        "additionalProperties": false
      },
      "AttributeValueRequest": {
        "type": "object",
        "properties": {
          "name": {
            "type": "string"
          },
          "isActive": {
            "type": "boolean"
          }
        },
        "additionalProperties": false
      },
      "AttributeValueResponse": {
        "type": "object",
        "properties": {
          "name": {
            "type": "string"
          },
          "isActive": {
            "type": "boolean"
          }
        },
        "additionalProperties": false
      },
      "AuCreditNoteProductResponse": {
        "type": "object",
        "properties": {
          "id": {
            "type": "string",
            "format": "uuid"
          },
          "saleProductId": {
            "type": "string",
            "format": "uuid"
          },
          "creditNoteAction": {
            "$ref": "#/components/schemas/CreditNoteAction"
          }
        },
        "additionalProperties": false
      },
      "AuCreditNoteResponse": {
        "type": "object",
        "allOf": [
          {
            "$ref": "#/components/schemas/BaseCreditNoteResponse"
          }
        ],
        "properties": {
          "id": {
            "type": "string",
            "format": "uuid"
          },
          "number": {
            "type": "string"
          },
          "amount": {
            "type": "number",
            "format": "double"
          },
          "reason": {
            "type": "string"
          },
          "invoiceNumber": {
            "type": "string"
          },
          "payerName": {
            "type": "string"
          },
          "payerType": {
            "type": "string"
          },
          "createdOn": {
            "type": "string",
            "format": "date-time"
          },
          "products": {
            "type": "array",
            "items": {
              "$ref": "#/components/schemas/AuCreditNoteProductResponse"
            }
          }
        },
        "additionalProperties": false
      },
      "AuDeliveryNoteResponse": {
        "type": "object",
        "allOf": [
          {
            "$ref": "#/components/schemas/BaseDeliveryNoteResponse"
          }
        ],
        "properties": {
          "id": {
            "type": "string",
            "format": "uuid"
          },
          "number": {
            "type": "string"
          },
          "actualDeliveryDate": {
            "type": "string",
            "format": "date-time"
          },
          "products": {
            "type": "array",
            "items": {
              "$ref": "#/components/schemas/DeliveryNoteProductResponse"
            }
          }
        },
        "additionalProperties": false
      },
      "AuManufacturerRequest": {
        "type": "object",
        "allOf": [
          {
            "$ref": "#/components/schemas/ManufacturerRequest"
          }
        ],
        "properties": {
          "stateId": {
            "type": "string",
            "format": "uuid",
            "nullable": true
          }
        },
        "additionalProperties": false
      },
      "AuManufacturerResponse": {
        "type": "object",
        "allOf": [
          {
            "$ref": "#/components/schemas/ManufacturerResponse"
          }
        ],
        "properties": {
          "stateId": {
            "type": "string",
            "format": "uuid",
            "nullable": true
          }
        },
        "additionalProperties": false
      },
      "AuSaleInvoiceResponse": {
        "type": "object",
        "allOf": [
          {
            "$ref": "#/components/schemas/BaseSaleInvoiceResponse"
          }
        ],
        "properties": {
          "number": {
            "type": "string"
          },
          "amount": {
            "type": "number",
            "format": "double"
          },
          "outstandingPayment": {
            "type": "number",
            "format": "double"
          },
          "createdOn": {
            "type": "string",
            "format": "date-time"
          },
          "isInBulkPayment": {
            "type": "boolean"
          },
          "payerName": {
            "type": "string"
          },
          "payerType": {
            "type": "string"
          }
        },
        "additionalProperties": false
      },
      "AuSaleProductDetailsResponse": {
        "type": "object",
        "allOf": [
          {
            "$ref": "#/components/schemas/BaseSaleProductDetailsResponse"
          }
        ],
        "properties": {
          "id": {
            "type": "string",
            "format": "uuid"
          },
          "isOrdered": {
            "type": "boolean"
          },
          "isLocked": {
            "type": "boolean"
          },
          "stockProductId": {
            "type": "string",
            "format": "uuid"
          },
          "stockProductItemId": {
            "type": "string",
            "format": "uuid",
            "nullable": true
          },
          "productId": {
            "type": "string",
            "format": "uuid"
          },
          "productCategoryCode": {
            "type": "string"
          },
          "productName": {
            "type": "string"
          },
          "side": {
            "$ref": "#/components/schemas/ProductSideType"
          },
          "color": {
            "type": "string"
          },
          "serialNumber": {
            "type": "string"
          },
          "quantity": {
            "type": "integer",
            "format": "int32"
          },
          "unitPrice": {
            "type": "number",
            "format": "double"
          },
          "clientPrice": {
            "type": "number",
            "format": "double"
          },
          "extPrice": {
            "type": "number",
            "format": "double"
          },
          "totalPrice": {
            "type": "number",
            "format": "double"
          },
          "discountAmount": {
            "type": "number",
            "format": "double"
          },
          "isSerialized": {
            "type": "boolean"
          },
          "isCredited": {
            "type": "boolean"
          },
          "isReplaced": {
            "type": "boolean"
          },
          "isReplacedByRepairOrder": {
            "type": "boolean"
          },
          "isDelivered": {
            "type": "boolean"
          },
          "isExchanged": {
            "type": "boolean"
          },
          "priceChangesAllowed": {
            "type": "boolean"
          },
          "productType": {
            "oneOf": [
              {
                "$ref": "#/components/schemas/DictionaryResponse"
              },
              {
                "$ref": "#/components/schemas/CountryResponse"
              }
            ]
          },
          "hearingAidType": {
            "oneOf": [
              {
                "$ref": "#/components/schemas/DictionaryResponse"
              },
              {
                "$ref": "#/components/schemas/CountryResponse"
              }
            ]
          },
          "manufacturer": {
            "oneOf": [
              {
                "$ref": "#/components/schemas/DictionaryResponse"
              },
              {
                "$ref": "#/components/schemas/CountryResponse"
              }
            ]
          },
          "batteryType": {
            "oneOf": [
              {
                "$ref": "#/components/schemas/DictionaryResponse"
              },
              {
                "$ref": "#/components/schemas/CountryResponse"
              }
            ]
          },
          "isHspMaintenance": {
            "type": "boolean",
            "nullable": true
          },
          "hspProductDetails": {
            "$ref": "#/components/schemas/ProductSaleHspProductDetailsResponse"
          },
          "hspServiceDetails": {
            "$ref": "#/components/schemas/ProductSaleHspServiceDetailsResponse"
          },
          "taxes": {
            "type": "array",
            "items": {
              "$ref": "#/components/schemas/ProductSaleTaxResponse"
            }
          },
          "payers": {
            "type": "array",
            "items": {
              "$ref": "#/components/schemas/PayerProductSaleResponse"
            }
          },
          "discounts": {
            "type": "array",
            "items": {
              "$ref": "#/components/schemas/DiscountTitleResponse"
            }
          }
        },
        "additionalProperties": false
      },
      "AuSaleProductResponse": {
        "type": "object",
        "allOf": [
          {
            "$ref": "#/components/schemas/BaseSaleProductResponse"
          }
        ],
        "properties": {
          "status": {
            "type": "string"
          },
          "stockProductId": {
            "type": "string",
            "format": "uuid"
          },
          "stockProductItemId": {
            "type": "string",
            "format": "uuid",
            "nullable": true
          },
          "productCategoryCode": {
            "type": "string"
          },
          "productName": {
            "type": "string"
          },
          "side": {
            "$ref": "#/components/schemas/ProductSideType"
          },
          "color": {
            "type": "string"
          },
          "serialNumber": {
            "type": "string"
          },
          "quantity": {
            "type": "integer",
            "format": "int32"
          },
          "unitPrice": {
            "type": "number",
            "format": "double"
          },
          "clientPrice": {
            "type": "number",
            "format": "double"
          },
          "extPrice": {
            "type": "number",
            "format": "double"
          },
          "totalPrice": {
            "type": "number",
            "format": "double"
          },
          "discountAmount": {
            "type": "number",
            "format": "double"
          },
          "isSerialized": {
            "type": "boolean"
          },
          "isCredited": {
            "type": "boolean"
          },
          "isReplaced": {
            "type": "boolean"
          },
          "isReplacedByRepairOrder": {
            "type": "boolean"
          },
          "isDelivered": {
            "type": "boolean"
          },
          "isExchanged": {
            "type": "boolean"
          },
          "priceChangesAllowed": {
            "type": "boolean"
          },
          "hearingAidType": {
            "oneOf": [
              {
                "$ref": "#/components/schemas/DictionaryResponse"
              },
              {
                "$ref": "#/components/schemas/CountryResponse"
              }
            ]
          },
          "productType": {
            "oneOf": [
              {
                "$ref": "#/components/schemas/DictionaryResponse"
              },
              {
                "$ref": "#/components/schemas/CountryResponse"
              }
            ]
          },
          "manufacturer": {
            "oneOf": [
              {
                "$ref": "#/components/schemas/DictionaryResponse"
              },
              {
                "$ref": "#/components/schemas/CountryResponse"
              }
            ]
          },
          "batteryType": {
            "oneOf": [
              {
                "$ref": "#/components/schemas/DictionaryResponse"
              },
              {
                "$ref": "#/components/schemas/CountryResponse"
              }
            ]
          },
          "hspProductDetails": {
            "$ref": "#/components/schemas/ProductSaleHspProductDetailsResponse"
          },
          "hspServiceDetails": {
            "$ref": "#/components/schemas/ProductSaleHspServiceDetailsResponse"
          },
          "taxes": {
            "type": "array",
            "items": {
              "$ref": "#/components/schemas/ProductSaleTaxResponse"
            }
          },
          "payers": {
            "type": "array",
            "items": {
              "$ref": "#/components/schemas/PayerProductSaleResponse"
            }
          },
          "discounts": {
            "type": "array",
            "items": {
              "$ref": "#/components/schemas/DiscountTitleResponse"
            }
          }
        },
        "additionalProperties": false
      },
      "AuSaleResponse": {
        "type": "object",
        "allOf": [
          {
            "$ref": "#/components/schemas/BaseSaleResponse"
          }
        ],
        "properties": {
          "id": {
            "type": "string",
            "format": "uuid"
          },
          "repairOrderId": {
            "type": "string",
            "format": "uuid",
            "nullable": true
          },
          "saleAmount": {
            "type": "number",
            "format": "double"
          },
          "appointmentId": {
            "type": "string",
            "format": "uuid",
            "nullable": true
          },
          "priceBookId": {
            "type": "string",
            "format": "uuid",
            "nullable": true
          },
          "hspDetailsId": {
            "type": "string",
            "format": "uuid",
            "nullable": true
          },
          "vatType": {
            "$ref": "#/components/schemas/SaleVatType"
          },
          "saleType": {
            "$ref": "#/components/schemas/SaleType"
          },
          "lnDOrderId": {
            "type": "string",
            "format": "uuid",
            "nullable": true
          },
          "pathway": {
            "$ref": "#/components/schemas/PathwayResponse"
          },
          "specialist": {
            "oneOf": [
              {
                "$ref": "#/components/schemas/DictionaryResponse"
              },
              {
                "$ref": "#/components/schemas/CountryResponse"
              }
            ]
          },
          "taxes": {
            "type": "array",
            "items": {
              "$ref": "#/components/schemas/SaleTaxResponse"
            }
          }
        },
        "additionalProperties": false
      },
      "AuSupplierRequest": {
        "type": "object",
        "allOf": [
          {
            "$ref": "#/components/schemas/SupplierRequest"
          }
        ],
        "properties": {
          "stateId": {
            "type": "string",
            "format": "uuid",
            "nullable": true
          }
        },
        "additionalProperties": false
      },
      "AuSupplierResponse": {
        "type": "object",
        "allOf": [
          {
            "$ref": "#/components/schemas/SupplierResponse"
          }
        ],
        "properties": {
          "stateId": {
            "type": "string",
            "format": "uuid",
            "nullable": true
          }
        },
        "additionalProperties": false
      },
      "AuTrialResponse": {
        "type": "object",
        "allOf": [
          {
            "$ref": "#/components/schemas/BaseTrialResponse"
          }
        ],
        "properties": {
          "id": {
            "type": "string",
            "format": "uuid"
          },
          "number": {
            "type": "string"
          },
          "status": {
            "type": "string"
          },
          "startDate": {
            "type": "string",
            "format": "date-time"
          },
          "endDate": {
            "type": "string",
            "format": "date-time"
          },
          "initialEndDate": {
            "type": "string",
            "format": "date-time"
          },
          "endDateLastChangedBy": {
            "type": "string"
          },
          "endDateLastChangedDate": {
            "type": "string",
            "format": "date-time",
            "nullable": true
          },
          "cancellationReasonName": {
            "type": "string"
          },
          "amount": {
            "type": "number",
            "format": "double"
          },
          "outstandingAmount": {
            "type": "number",
            "format": "double"
          },
          "depositsAmount": {
            "type": "number",
            "format": "double"
          },
          "writeOffsAmount": {
            "type": "number",
            "format": "double"
          },
          "products": {
            "type": "array",
            "items": {
              "$ref": "#/components/schemas/TrialProductResponse"
            }
          }
        },
        "additionalProperties": false
      },
      "BaseAppointmentRequest": {
        "type": "object",
        "properties": {
          "description": {
            "type": "string"
          },
          "locationId": {
            "type": "string",
            "format": "uuid"
          },
          "patientId": {
            "type": "string",
            "format": "uuid",
            "nullable": true
          },
          "campaignId": {
            "type": "string",
            "format": "uuid",
            "nullable": true
          },
          "startDate": {
            "type": "string",
            "format": "date-time"
          },
          "endDate": {
            "type": "string",
            "format": "date-time"
          },
          "appointmentReasonId": {
            "type": "string",
            "format": "uuid"
          },
          "appointmentStatusId": {
            "type": "string",
            "format": "uuid",
            "nullable": true
          },
          "confirmationTypeId": {
            "type": "string",
            "format": "uuid",
            "nullable": true
          },
          "outcomeId": {
            "type": "string",
            "format": "uuid",
            "nullable": true
          },
          "note": {
            "type": "string",
            "nullable": true
          },
          "cancellationReasonId": {
            "type": "string",
            "format": "uuid",
            "nullable": true
          },
          "reasonSourceId": {
            "type": "string",
            "format": "uuid",
            "nullable": true
          }
        },
        "additionalProperties": false
      },
      "BaseCodeResponse": {
        "type": "object",
        "properties": {
          "id": {
            "type": "string",
            "format": "uuid"
          },
          "code": {
            "type": "string"
          }
        },
        "additionalProperties": false
      },
      "BaseCreditNoteResponse": {
        "type": "object",
        "properties": {
          "patient": {
            "oneOf": [
              {
                "$ref": "#/components/schemas/DictionaryResponse"
              },
              {
                "$ref": "#/components/schemas/CountryResponse"
              }
            ]
          },
          "location": {
            "oneOf": [
              {
                "$ref": "#/components/schemas/DictionaryResponse"
              },
              {
                "$ref": "#/components/schemas/CountryResponse"
              }
            ]
          }
        },
        "additionalProperties": false
      },
      "BaseDeliveryNoteResponse": {
        "type": "object",
        "properties": {
          "patient": {
            "oneOf": [
              {
                "$ref": "#/components/schemas/DictionaryResponse"
              },
              {
                "$ref": "#/components/schemas/CountryResponse"
              }
            ]
          },
          "location": {
            "oneOf": [
              {
                "$ref": "#/components/schemas/DictionaryResponse"
              },
              {
                "$ref": "#/components/schemas/CountryResponse"
              }
            ]
          }
        },
        "additionalProperties": false
      },
      "BaseProductRequest": {
        "type": "object",
        "properties": {
          "description": {
            "type": "string",
            "nullable": true
          },
          "manufacturerId": {
            "type": "string",
            "format": "uuid",
            "nullable": true
          },
          "supplierId": {
            "type": "string",
            "format": "uuid",
            "nullable": true
          },
          "hearingAidTypeId": {
            "type": "string",
            "format": "uuid",
            "nullable": true
          },
          "warranty": {
            "type": "integer",
            "format": "int32",
            "nullable": true
          },
          "ldWarranty": {
            "type": "integer",
            "format": "int32",
            "nullable": true
          },
          "quantity": {
            "type": "integer",
            "format": "int32"
          },
          "retailPrice": {
            "type": "number",
            "format": "double"
          },
          "isSellable": {
            "type": "boolean"
          },
          "isActive": {
            "type": "boolean"
          },
          "isSerialized": {
            "type": "boolean"
          },
          "priceChangesAllowed": {
            "type": "boolean"
          },
          "autoDeliver": {
            "type": "boolean"
          },
          "controlledByStock": {
            "type": "boolean"
          },
          "vendorProductNumber": {
            "type": "string",
            "nullable": true
          },
          "maximumDiscount": {
            "type": "number",
            "format": "double",
            "nullable": true
          },
          "cost": {
            "type": "number",
            "format": "double",
            "nullable": true
          }
        },
        "additionalProperties": false
      },
      "BaseReasonResponse": {
        "type": "object",
        "allOf": [
          {
            "$ref": "#/components/schemas/BaseCodeResponse"
          }
        ],
        "properties": {
          "description": {
            "type": "string"
          }
        },
        "additionalProperties": false
      },
      "BaseSaleInvoiceResponse": {
        "type": "object",
        "properties": {
          "id": {
            "type": "string",
            "format": "uuid"
          },
          "patientId": {
            "type": "string",
            "format": "uuid"
          },
          "locationId": {
            "type": "string",
            "format": "uuid"
          },
          "products": {
            "type": "array",
            "items": {
              "$ref": "#/components/schemas/InvoiceProductResponse"
            }
          }
        },
        "additionalProperties": false
      },
      "BaseSaleProductDetailsResponse": {
        "type": "object",
        "additionalProperties": false
      },
      "BaseSaleProductResponse": {
        "type": "object",
        "properties": {
          "id": {
            "type": "string",
            "format": "uuid"
          },
          "productId": {
            "type": "string",
            "format": "uuid"
          }
        },
        "additionalProperties": false
      },
      "BaseSaleResponse": {
        "type": "object",
        "properties": {
          "patient": {
            "oneOf": [
              {
                "$ref": "#/components/schemas/DictionaryResponse"
              },
              {
                "$ref": "#/components/schemas/CountryResponse"
              }
            ]
          },
          "location": {
            "oneOf": [
              {
                "$ref": "#/components/schemas/DictionaryResponse"
              },
              {
                "$ref": "#/components/schemas/CountryResponse"
              }
            ]
          }
        },
        "additionalProperties": false
      },
      "BaseTrialResponse": {
        "type": "object",
        "additionalProperties": false
      },
      "BookAppointmentByLocationRequest": {
        "type": "object",
        "properties": {
          "startDateTime": {
            "type": "string",
            "description": "Appointment start date (in UTC). Mandatory field.",
            "format": "date-time"
          },
          "appointmentReasonId": {
            "type": "string",
            "description": "ID of appointment reason. Mandatory field.",
            "format": "uuid"
          },
          "customerDetails": {
            "$ref": "#/components/schemas/CustomerDetails"
          },
          "campaignId": {
            "type": "string",
            "description": "ID of campaign.\r\nMandatory field if appointment reason requires a campaign. If not, field is optional.",
            "format": "uuid",
            "nullable": true
          },
          "note": {
            "type": "string",
            "description": "Appointment note. Optional field.\r\nMaximum length: 1000 symbols.",
            "nullable": true
          },
          "resourceIds": {
            "type": "array",
            "items": {
              "type": "string",
              "format": "uuid"
            },
            "description": "Appointment resource ids, empty by default."
          }
        },
        "additionalProperties": false
      },
      "BookAppointmentByLocationResponse": {
        "type": "object",
        "properties": {
          "data": {
            "$ref": "#/components/schemas/AppointmentWithLocation"
          },
          "success": {
            "type": "boolean"
          },
          "error": {
            "$ref": "#/components/schemas/ErrorResponse"
          }
        },
        "additionalProperties": false
      },
      "BookAppointmentRequest": {
        "type": "object",
        "properties": {
          "epos": {
            "type": "string",
            "description": "EPOS number.\r\nMaximum length: 50 symbols (digits only)."
          },
          "startDateTime": {
            "type": "string",
            "description": "Appointment start date (in UTC). Mandatory field.",
            "format": "date-time"
          },
          "appointmentReasonId": {
            "type": "string",
            "description": "ID of appointment reason. Mandatory field.",
            "format": "uuid"
          },
          "customerDetails": {
            "$ref": "#/components/schemas/CustomerDetails"
          },
          "campaignId": {
            "type": "string",
            "description": "ID of campaign.\r\nMandatory field if appointment reason requires a campaign. If not, field is optional.",
            "format": "uuid",
            "nullable": true
          },
          "note": {
            "type": "string",
            "description": "Appointment note. Optional field.\r\nMaximum length: 1000 symbols.",
            "nullable": true
          },
          "resourceIds": {
            "type": "array",
            "items": {
              "type": "string",
              "format": "uuid"
            },
            "description": "Appointment resource ids, empty by default."
          }
        },
        "additionalProperties": false
      },
      "BookAppointmentResponse": {
        "type": "object",
        "properties": {
          "data": {
            "$ref": "#/components/schemas/Appointment"
          },
          "success": {
            "type": "boolean"
          },
          "error": {
            "$ref": "#/components/schemas/ErrorResponse"
          }
        },
        "additionalProperties": false
      },
      "CampaignRequest": {
        "type": "object",
        "properties": {
          "name": {
            "type": "string"
          },
          "startDate": {
            "type": "string",
            "format": "date-time",
            "nullable": true
          },
          "endDate": {
            "type": "string",
            "format": "date-time",
            "nullable": true
          },
          "isActive": {
            "type": "boolean"
          }
        },
        "additionalProperties": false
      },
      "CampaignResponse": {
        "type": "object",
        "properties": {
          "id": {
            "type": "string",
            "format": "uuid"
          },
          "name": {
            "type": "string"
          },
          "startDate": {
            "type": "string",
            "format": "date-time",
            "nullable": true
          },
          "endDate": {
            "type": "string",
            "format": "date-time",
            "nullable": true
          },
          "isActive": {
            "type": "boolean"
          }
        },
        "additionalProperties": false
      },
      "CampaignResponsePagingResponse": {
        "type": "object",
        "properties": {
          "data": {
            "type": "array",
            "items": {
              "$ref": "#/components/schemas/CampaignResponse"
            },
            "nullable": true
          },
          "paginagion": {
            "$ref": "#/components/schemas/PagingOptions"
          }
        },
        "additionalProperties": false
      },
      "CancellationReasonResponse": {
        "type": "object",
        "properties": {
          "id": {
            "type": "string",
            "format": "uuid"
          },
          "code": {
            "type": "string"
          },
          "description": {
            "type": "string"
          },
          "isActive": {
            "type": "boolean"
          }
        },
        "additionalProperties": false
      },
      "CancellationReasonResponseListResponse": {
        "type": "object",
        "properties": {
          "data": {
            "$ref": "#/components/schemas/CancellationReasonResponse"
          }
        },
        "additionalProperties": false
      },
      "ContactPerson": {
        "type": "object",
        "properties": {
          "name": {
            "type": "string",
            "nullable": true
          },
          "phoneNumber": {
            "type": "string",
            "nullable": true
          },
          "emailAddress": {
            "type": "string",
            "nullable": true
          },
          "extension": {
            "type": "string",
            "nullable": true
          }
        },
        "additionalProperties": false
      },
      "ContactPersonPreferredPhone": {
        "enum": [
          "MobilePhone",
          "WorkPhone",
          "HomePhone"
        ],
        "type": "string"
      },
      "ContactPersonV2Request": {
        "type": "object",
        "properties": {
          "name": {
            "type": "string",
            "nullable": true
          },
          "city": {
            "type": "string",
            "nullable": true
          },
          "state": {
            "type": "string",
            "nullable": true
          },
          "stateId": {
            "type": "string",
            "format": "uuid",
            "nullable": true
          },
          "postcode": {
            "type": "string",
            "nullable": true
          },
          "mobilePhone": {
            "type": "string",
            "nullable": true
          },
          "homePhone": {
            "type": "string",
            "nullable": true
          },
          "workPhone": {
            "type": "string",
            "nullable": true
          },
          "preferredPhone": {
            "$ref": "#/components/schemas/ContactPersonPreferredPhone"
          },
          "emailAddress": {
            "type": "string",
            "nullable": true
          },
          "relationshipToPatient": {
            "type": "string",
            "nullable": true
          },
          "preferredContact": {
            "type": "boolean"
          },
          "address1": {
            "type": "string",
            "nullable": true
          },
          "address2": {
            "type": "string",
            "nullable": true
          },
          "county": {
            "type": "string",
            "nullable": true
          },
          "address4": {
            "type": "string",
            "nullable": true
          },
          "address5": {
            "type": "string",
            "nullable": true
          },
          "suburb": {
            "type": "string",
            "nullable": true
          },
          "countryId": {
            "type": "string",
            "format": "uuid",
            "nullable": true,
            "deprecated": true
          },
          "country": {
            "type": "string",
            "description": "Country ISO 3166-1 alpha-2 code",
            "nullable": true
          }
        },
        "additionalProperties": false
      },
      "CountryResponse": {
        "type": "object",
        "allOf": [
          {
            "$ref": "#/components/schemas/DictionaryResponse"
          }
        ],
        "properties": {
          "code": {
            "type": "string"
          }
        },
        "additionalProperties": false
      },
      "CountryResponseIEnumerableListResponse": {
        "type": "object",
        "properties": {
          "data": {
            "type": "array",
            "items": {
              "$ref": "#/components/schemas/CountryResponse"
            },
            "nullable": true
          }
        },
        "additionalProperties": false
      },
      "CreateAUPatientRequest": {
        "type": "object",
        "allOf": [
          {
            "$ref": "#/components/schemas/CreatePatientRequest"
          }
        ],
        "properties": {
          "dva": {
            "type": "boolean"
          },
          "dvaNumber": {
            "type": "string",
            "nullable": true
          },
          "dvaCardType": {
            "$ref": "#/components/schemas/PatientDVACardType"
          },
          "dvaExpireDate": {
            "type": "string",
            "format": "date-time",
            "nullable": true
          }
        },
        "additionalProperties": false
      },
      "CreateNZPatientRequest": {
        "type": "object",
        "allOf": [
          {
            "$ref": "#/components/schemas/CreatePatientRequest"
          }
        ],
        "properties": {
          "nhiNumber": {
            "type": "string"
          }
        },
        "additionalProperties": false
      },
      "CreatePatientRequest": {
        "type": "object",
        "properties": {
          "patientLead": {
            "type": "boolean"
          },
          "originalPatientId": {
            "type": "string",
            "nullable": true
          },
          "firstName": {
            "type": "string",
            "nullable": true
          },
          "lastName": {
            "type": "string",
            "nullable": true
          },
          "middleName": {
            "type": "string",
            "nullable": true
          },
          "shortName": {
            "type": "string",
            "nullable": true
          },
          "dateOfBirth": {
            "type": "string",
            "format": "date-time",
            "nullable": true
          },
          "city": {
            "type": "string",
            "nullable": true
          },
          "stateId": {
            "type": "string",
            "description": "Unique identifier of State (GUID). Can be retrieved from /api/v2/states endpoint.",
            "format": "uuid",
            "nullable": true
          },
          "postcode": {
            "type": "string",
            "nullable": true
          },
          "mobilePhone": {
            "type": "string",
            "nullable": true
          },
          "homePhone": {
            "type": "string",
            "nullable": true
          },
          "workPhone": {
            "type": "string",
            "nullable": true
          },
          "preferredPhone": {
            "$ref": "#/components/schemas/PreferredPhone"
          },
          "faxNumber": {
            "type": "string",
            "nullable": true
          },
          "emailAddress": {
            "type": "string",
            "nullable": true
          },
          "hadAppointmentOnly": {
            "type": "boolean",
            "nullable": true
          },
          "gpId": {
            "type": "string",
            "nullable": true
          },
          "gpPracticeId": {
            "type": "string",
            "nullable": true
          },
          "note": {
            "type": "string",
            "nullable": true
          },
          "employer": {
            "type": "string",
            "nullable": true
          },
          "occupation": {
            "type": "string",
            "nullable": true
          },
          "homeFlatNumber": {
            "type": "string",
            "nullable": true
          },
          "street": {
            "type": "string",
            "nullable": true
          },
          "county": {
            "type": "string",
            "nullable": true
          },
          "address4": {
            "type": "string",
            "nullable": true
          },
          "address5": {
            "type": "string",
            "nullable": true
          },
          "suburb": {
            "type": "string",
            "nullable": true
          },
          "leadSourceId": {
            "type": "string",
            "format": "uuid",
            "nullable": true
          },
          "campaignId": {
            "type": "string",
            "format": "uuid",
            "nullable": true
          },
          "titleId": {
            "type": "string",
            "format": "uuid",
            "nullable": true
          },
          "genderId": {
            "type": "string",
            "format": "uuid",
            "nullable": true
          },
          "preferredLanguageId": {
            "type": "string",
            "format": "uuid",
            "nullable": true
          },
          "employmentStatusId": {
            "type": "string",
            "format": "uuid",
            "nullable": true
          },
          "locationId": {
            "type": "string",
            "format": "uuid",
            "nullable": true
          },
          "countryId": {
            "type": "string",
            "format": "uuid",
            "nullable": true,
            "deprecated": true
          },
          "country": {
            "type": "string",
            "description": "Country ISO 3166-1 alpha-2 code",
            "nullable": true
          },
          "specialistId": {
            "type": "string",
            "format": "uuid",
            "nullable": true
          },
          "patientStatusId": {
            "type": "string",
            "format": "uuid",
            "nullable": true
          },
          "contactPerson": {
            "$ref": "#/components/schemas/ContactPersonV2Request"
          },
          "initialOwner": {
            "type": "string",
            "nullable": true
          },
          "initialOwnerLocation": {
            "type": "string",
            "nullable": true
          }
        },
        "additionalProperties": false
      },
      "CreateROIPatientRequest": {
        "type": "object",
        "allOf": [
          {
            "$ref": "#/components/schemas/CreatePatientRequest"
          }
        ],
        "additionalProperties": false
      },
      "CreateUKPatientRequest": {
        "type": "object",
        "allOf": [
          {
            "$ref": "#/components/schemas/CreatePatientRequest"
          }
        ],
        "additionalProperties": false
      },
      "CreateUSPatientRequest": {
        "type": "object",
        "allOf": [
          {
            "$ref": "#/components/schemas/CreatePatientRequest"
          }
        ],
        "additionalProperties": false
      },
      "CreatedResponse": {
        "type": "object",
        "properties": {
          "id": {
            "type": "string",
            "format": "uuid"
          }
        },
        "additionalProperties": false
      },
      "CreditNoteAction": {
        "enum": [
          "DoNothing",
          "BackToStock",
          "ReturnToSupplier"
        ],
        "type": "string"
      },
      "CustomerDetails": {
        "required": [
          "firstname",
          "lastname"
        ],
        "type": "object",
        "properties": {
          "firstname": {
            "minLength": 1,
            "type": "string"
          },
          "lastname": {
            "minLength": 1,
            "type": "string"
          },
          "birthdate": {
            "type": "string",
            "format": "date-time",
            "nullable": true
          },
          "title": {
            "type": "string",
            "nullable": true
          },
          "email": {
            "type": "string",
            "nullable": true
          },
          "mobilePhone": {
            "type": "string",
            "nullable": true
          },
          "alternativePhone": {
            "type": "string",
            "nullable": true
          }
        },
        "additionalProperties": false
      },
      "DeliverLineItemRequest": {
        "type": "object",
        "properties": {
          "deliveryNumber": {
            "type": "string",
            "nullable": true
          },
          "quantity": {
            "type": "integer",
            "format": "int32"
          },
          "shippedDate": {
            "type": "string",
            "format": "date-time",
            "nullable": true
          },
          "note": {
            "type": "string",
            "nullable": true
          }
        },
        "additionalProperties": false
      },
      "DeliverOrderLineItemRequest": {
        "type": "object",
        "properties": {
          "id": {
            "type": "string",
            "format": "uuid"
          },
          "deliveryNumber": {
            "type": "string",
            "nullable": true
          },
          "quantity": {
            "type": "integer",
            "format": "int32"
          },
          "shippedDate": {
            "type": "string",
            "format": "date-time"
          },
          "note": {
            "type": "string",
            "nullable": true
          }
        },
        "additionalProperties": false
      },
      "DeliverOrderRequest": {
        "type": "object",
        "properties": {
          "lineItems": {
            "type": "array",
            "items": {
              "$ref": "#/components/schemas/DeliverOrderLineItemRequest"
            }
          }
        },
        "additionalProperties": false
      },
      "DeliveryNoteProductResponse": {
        "type": "object",
        "properties": {
          "id": {
            "type": "string",
            "format": "uuid"
          },
          "name": {
            "type": "string"
          },
          "saleProductId": {
            "type": "string",
            "format": "uuid"
          },
          "warrantyExpirationDate": {
            "type": "string",
            "format": "date-time",
            "nullable": true
          },
          "ldWarrantyExpirationDate": {
            "type": "string",
            "format": "date-time",
            "nullable": true
          }
        },
        "additionalProperties": false
      },
      "DictionaryRequest": {
        "type": "object",
        "properties": {
          "id": {
            "type": "string",
            "format": "uuid"
          },
          "name": {
            "type": "string"
          },
          "isActive": {
            "type": "boolean"
          }
        },
        "additionalProperties": false
      },
      "DictionaryResponse": {
        "type": "object",
        "properties": {
          "id": {
            "type": "string",
            "format": "uuid"
          },
          "name": {
            "type": "string"
          }
        },
        "additionalProperties": false
      },
      "DictionaryResponseIEnumerableListResponse": {
        "type": "object",
        "properties": {
          "data": {
            "type": "array",
            "items": {
              "oneOf": [
                {
                  "$ref": "#/components/schemas/DictionaryResponse"
                },
                {
                  "$ref": "#/components/schemas/CountryResponse"
                }
              ]
            },
            "nullable": true
          }
        },
        "additionalProperties": false
      },
      "DiscountTitleResponse": {
        "type": "object",
        "properties": {
          "id": {
            "type": "string",
            "format": "uuid"
          },
          "name": {
            "type": "string"
          },
          "type": {
            "$ref": "#/components/schemas/DiscountType"
          },
          "value": {
            "type": "number",
            "format": "double"
          }
        },
        "additionalProperties": false
      },
      "DiscountType": {
        "enum": [
          "Monetary",
          "Percentage",
          "Director"
        ],
        "type": "string"
      },
      "ErrorDetails": {
        "type": "object",
        "properties": {
          "propertyName": {
            "type": "string"
          },
          "errorMessage": {
            "type": "string"
          },
          "attemptedValue": {},
          "customState": {},
          "severity": {
            "type": "string"
          },
          "errorCode": {
            "type": "string"
          },
          "formattedMessagePlaceholderValues": {
            "type": "array",
            "items": {
              "type": "string"
            }
          }
        },
        "additionalProperties": false
      },
      "ErrorResponse": {
        "type": "object",
        "properties": {
          "message": {
            "type": "string"
          },
          "type": {
            "type": "string"
          },
          "notificationType": {
            "type": "string"
          },
          "data": {
            "type": "array",
            "items": {
              "$ref": "#/components/schemas/ErrorDetails"
            }
          },
          "requestId": {
            "type": "string"
          }
        },
        "additionalProperties": false
      },
      "ExternalSkuAttribute": {
        "type": "object",
        "properties": {
          "id": {
            "type": "string",
            "format": "uuid"
          },
          "value": {
            "type": "string"
          }
        },
        "additionalProperties": false
      },
      "FunderRequest": {
        "type": "object",
        "properties": {
          "payerId": {
            "type": "string",
            "nullable": true
          },
          "name": {
            "type": "string"
          },
          "type": {
            "type": "string",
            "description": "Values:\r\nOther,\r\nNHS,\r\nHSP,\r\nMedicare,\r\nACC,\r\nPRSI,\r\nWorkcover,\r\nMedicaid,\r\nTricare,\r\nChampva,\r\nGroupHealthPlan,\r\nFecaBlkLung,\r\nUsMedicare,\r\nUsOther,",
            "nullable": true
          },
          "status": {
            "type": "string",
            "description": "Values:\r\nActive,\r\nInactive",
            "nullable": true
          },
          "address1": {
            "type": "string",
            "nullable": true
          },
          "address2": {
            "type": "string",
            "nullable": true
          },
          "country": {
            "type": "string",
            "nullable": true
          },
          "city": {
            "type": "string",
            "nullable": true
          },
          "postalZip": {
            "type": "string",
            "nullable": true
          },
          "emailAddress": {
            "type": "string",
            "nullable": true
          },
          "phoneNumber": {
            "type": "string",
            "nullable": true
          },
          "contactPerson": {
            "type": "string",
            "nullable": true
          },
          "province": {
            "type": "string",
            "nullable": true
          }
        },
        "additionalProperties": false
      },
      "FunderResponse": {
        "type": "object",
        "properties": {
          "id": {
            "type": "string",
            "format": "uuid"
          },
          "payerId": {
            "type": "string",
            "nullable": true
          },
          "name": {
            "type": "string"
          },
          "type": {
            "type": "string",
            "nullable": true
          },
          "status": {
            "type": "string",
            "nullable": true
          },
          "address1": {
            "type": "string",
            "nullable": true
          },
          "address2": {
            "type": "string",
            "nullable": true
          },
          "country": {
            "type": "string",
            "nullable": true
          },
          "city": {
            "type": "string",
            "nullable": true
          },
          "postalZip": {
            "type": "string",
            "nullable": true
          },
          "emailAddress": {
            "type": "string",
            "nullable": true
          },
          "phoneNumber": {
            "type": "string",
            "nullable": true
          },
          "contactPerson": {
            "type": "string",
            "nullable": true
          },
          "province": {
            "type": "string",
            "nullable": true
          }
        },
        "additionalProperties": false
      },
      "FunderResponsePagingResponse": {
        "type": "object",
        "properties": {
          "data": {
            "type": "array",
            "items": {
              "$ref": "#/components/schemas/FunderResponse"
            },
            "nullable": true
          },
          "paginagion": {
            "$ref": "#/components/schemas/PagingOptions"
          }
        },
        "additionalProperties": false
      },
      "GeneralPractitionerResponse": {
        "type": "object",
        "properties": {
          "id": {
            "type": "string",
            "format": "uuid"
          },
          "name": {
            "type": "string"
          },
          "code": {
            "type": "string"
          }
        },
        "additionalProperties": false
      },
      "GeneralPractitionerResponsePagingResponse": {
        "type": "object",
        "properties": {
          "data": {
            "type": "array",
            "items": {
              "$ref": "#/components/schemas/GeneralPractitionerResponse"
            },
            "nullable": true
          },
          "paginagion": {
            "$ref": "#/components/schemas/PagingOptions"
          }
        },
        "additionalProperties": false
      },
      "GetAppointmentResponse": {
        "type": "object",
        "properties": {
          "id": {
            "type": "string",
            "format": "uuid"
          },
          "patientId": {
            "type": "string",
            "format": "uuid"
          },
          "campaignId": {
            "type": "string",
            "format": "uuid",
            "nullable": true
          },
          "startDate": {
            "type": "string",
            "format": "date-time"
          },
          "endDate": {
            "type": "string",
            "format": "date-time"
          },
          "confirmationTypeId": {
            "type": "string",
            "format": "uuid",
            "nullable": true
          },
          "specialistLinks": {
            "type": "array",
            "items": {
              "$ref": "#/components/schemas/SpecialistLinksResponse"
            },
            "nullable": true
          },
          "resourceLinks": {
            "type": "array",
            "items": {
              "oneOf": [
                {
                  "$ref": "#/components/schemas/BaseCodeResponse"
                },
                {
                  "$ref": "#/components/schemas/AppointmentOutcomeResponse"
                },
                {
                  "$ref": "#/components/schemas/AppointmentStatusesResponse"
                },
                {
                  "$ref": "#/components/schemas/BaseReasonResponse"
                }
              ]
            },
            "nullable": true
          },
          "source": {
            "type": "string"
          },
          "version": {
            "type": "string"
          },
          "description": {
            "type": "string"
          },
          "appointmentReason": {
            "oneOf": [
              {
                "$ref": "#/components/schemas/BaseCodeResponse"
              },
              {
                "$ref": "#/components/schemas/AppointmentOutcomeResponse"
              },
              {
                "$ref": "#/components/schemas/AppointmentStatusesResponse"
              },
              {
                "$ref": "#/components/schemas/BaseReasonResponse"
              }
            ]
          },
          "outcome": {
            "oneOf": [
              {
                "$ref": "#/components/schemas/BaseCodeResponse"
              },
              {
                "$ref": "#/components/schemas/AppointmentOutcomeResponse"
              },
              {
                "$ref": "#/components/schemas/AppointmentStatusesResponse"
              },
              {
                "$ref": "#/components/schemas/BaseReasonResponse"
              }
            ],
            "nullable": true
          },
          "didNotAttendReason": {
            "$ref": "#/components/schemas/BaseReasonResponse"
          },
          "cancellationReason": {
            "$ref": "#/components/schemas/BaseReasonResponse"
          },
          "appointmentStatus": {
            "oneOf": [
              {
                "$ref": "#/components/schemas/DictionaryResponse"
              },
              {
                "$ref": "#/components/schemas/CountryResponse"
              }
            ],
            "nullable": true
          },
          "location": {
            "oneOf": [
              {
                "$ref": "#/components/schemas/DictionaryResponse"
              },
              {
                "$ref": "#/components/schemas/CountryResponse"
              }
            ]
          }
        },
        "additionalProperties": false
      },
      "GetSkuConfigResponse": {
        "type": "object",
        "properties": {
          "useBattery": {
            "type": "boolean"
          },
          "useColor": {
            "type": "boolean"
          },
          "attributes": {
            "type": "array",
            "items": {
              "$ref": "#/components/schemas/SkuConfigAttribute"
            }
          }
        },
        "additionalProperties": false
      },
      "GetSkuResponse": {
        "type": "object",
        "properties": {
          "sku": {
            "type": "string"
          },
          "colorId": {
            "type": "string",
            "format": "uuid",
            "nullable": true
          },
          "batteryId": {
            "type": "string",
            "format": "uuid",
            "nullable": true
          },
          "attributes": {
            "type": "array",
            "items": {
              "$ref": "#/components/schemas/ExternalSkuAttribute"
            }
          }
        },
        "additionalProperties": false
      },
      "GnEdiResponse": {
        "type": "object",
        "properties": {
          "status": {
            "type": "string",
            "description": "Success - 1 or blank\r\nValidation Error - 0"
          },
          "error": {
            "type": "string",
            "description": "Human readable error message"
          },
          "reports": {
            "type": "array",
            "items": {
              "type": "string"
            }
          }
        },
        "additionalProperties": false
      },
      "HspServiceImportResult": {
        "enum": [
          "Rejected",
          "Approved"
        ],
        "type": "string"
      },
      "HspServiceType": {
        "enum": [
          "Fitting",
          "NonFitting"
        ],
        "type": "string"
      },
      "HttpValidationProblemDetails": {
        "type": "object",
        "allOf": [
          {
            "$ref": "#/components/schemas/ProblemDetails"
          }
        ],
        "properties": {
          "errors": {
            "type": "object",
            "additionalProperties": {
              "type": "array",
              "items": {
                "type": "string"
              }
            }
          }
        },
        "additionalProperties": {}
      },
      "IContractResolver": {
        "type": "object",
        "additionalProperties": false
      },
      "InvoiceProductResponse": {
        "type": "object",
        "properties": {
          "id": {
            "type": "string",
            "format": "uuid"
          },
          "saleProductId": {
            "type": "string",
            "format": "uuid"
          }
        },
        "additionalProperties": false
      },
      "LnDReasonResponse": {
        "type": "object",
        "properties": {
          "id": {
            "type": "string",
            "format": "uuid"
          },
          "reason": {
            "type": "string"
          },
          "isActive": {
            "type": "boolean"
          }
        },
        "additionalProperties": false
      },
      "LnDReasonResponseIEnumerableListResponse": {
        "type": "object",
        "properties": {
          "data": {
            "type": "array",
            "items": {
              "$ref": "#/components/schemas/LnDReasonResponse"
            },
            "nullable": true
          }
        },
        "additionalProperties": false
      },
      "LnDSettingsResponse": {
        "type": "object",
        "properties": {
          "id": {
            "type": "string",
            "format": "uuid"
          },
          "limit": {
            "type": "integer",
            "format": "int32"
          }
        },
        "additionalProperties": false
      },
      "LnDStatusResponse": {
        "type": "object",
        "properties": {
          "id": {
            "type": "string",
            "format": "uuid"
          },
          "name": {
            "type": "string"
          },
          "isActive": {
            "type": "boolean"
          },
          "isCompleted": {
            "type": "boolean"
          },
          "isInUse": {
            "type": "boolean"
          }
        },
        "additionalProperties": false
      },
      "LnDStatusResponseIEnumerableListResponse": {
        "type": "object",
        "properties": {
          "data": {
            "type": "array",
            "items": {
              "$ref": "#/components/schemas/LnDStatusResponse"
            },
            "nullable": true
          }
        },
        "additionalProperties": false
      },
      "LocationSpecialistTimeSlotsResponse": {
        "type": "object",
        "properties": {
          "locationId": {
            "type": "string",
            "format": "uuid"
          },
          "timeSlots": {
            "type": "array",
            "items": {
              "$ref": "#/components/schemas/SpecialistTimeSlotsResponse"
            }
          }
        },
        "additionalProperties": false
      },
      "ManufacturerRequest": {
        "type": "object",
        "properties": {
          "name": {
            "type": "string",
            "nullable": true
          },
          "isActive": {
            "type": "boolean"
          },
          "website": {
            "type": "string",
            "nullable": true
          },
          "emailAddress": {
            "type": "string",
            "nullable": true
          },
          "phoneNumber": {
            "type": "string",
            "nullable": true
          },
          "faxNumber": {
            "type": "string",
            "nullable": true
          },
          "address1": {
            "type": "string",
            "nullable": true
          },
          "address2": {
            "type": "string",
            "nullable": true
          },
          "country": {
            "type": "string",
            "description": "Country ISO 3166-1 alpha-2 code",
            "nullable": true
          },
          "city": {
            "type": "string",
            "nullable": true
          },
          "postalCode": {
            "type": "string",
            "nullable": true
          },
          "salesContact": {
            "$ref": "#/components/schemas/ContactPerson"
          },
          "accountReceivableContact": {
            "$ref": "#/components/schemas/ContactPerson"
          }
        },
        "additionalProperties": false
      },
      "ManufacturerResponse": {
        "type": "object",
        "properties": {
          "id": {
            "type": "string",
            "format": "uuid"
          },
          "name": {
            "type": "string",
            "nullable": true
          },
          "phoneNumber": {
            "type": "string",
            "nullable": true
          },
          "country": {
            "type": "string",
            "description": "Country ISO 3166-1 alpha-2 code",
            "nullable": true
          },
          "city": {
            "type": "string",
            "nullable": true
          },
          "isActive": {
            "type": "boolean"
          },
          "website": {
            "type": "string",
            "nullable": true
          },
          "emailAddress": {
            "type": "string",
            "nullable": true
          },
          "faxNumber": {
            "type": "string",
            "nullable": true
          },
          "address1": {
            "type": "string",
            "nullable": true
          },
          "address2": {
            "type": "string",
            "nullable": true
          },
          "postalCode": {
            "type": "string",
            "nullable": true
          },
          "salesContact": {
            "$ref": "#/components/schemas/ContactPerson"
          },
          "accountReceivableContact": {
            "$ref": "#/components/schemas/ContactPerson"
          }
        },
        "additionalProperties": false
      },
      "ManufacturerResponsePagingResponse": {
        "type": "object",
        "properties": {
          "data": {
            "type": "array",
            "items": {
              "oneOf": [
                {
                  "$ref": "#/components/schemas/AuManufacturerResponse"
                },
                {
                  "$ref": "#/components/schemas/UsManufacturerResponse"
                },
                {
                  "$ref": "#/components/schemas/UkManufacturerResponse"
                },
                {
                  "$ref": "#/components/schemas/NzManufacturerResponse"
                },
                {
                  "$ref": "#/components/schemas/RoiManufacturerResponse"
                }
              ]
            },
            "nullable": true
          },
          "paginagion": {
            "$ref": "#/components/schemas/PagingOptions"
          }
        },
        "additionalProperties": false
      },
      "NZProductRequest": {
        "type": "object",
        "allOf": [
          {
            "$ref": "#/components/schemas/ProductRequest"
          }
        ],
        "additionalProperties": false
      },
      "NhsContractResponse": {
        "type": "object",
        "properties": {
          "id": {
            "type": "string",
            "format": "uuid"
          },
          "name": {
            "type": "string"
          },
          "startDate": {
            "type": "string",
            "format": "date-time"
          },
          "endDate": {
            "type": "string",
            "format": "date-time"
          }
        },
        "additionalProperties": false
      },
      "NzCreditNotePayerResponse": {
        "type": "object",
        "properties": {
          "id": {
            "type": "string",
            "format": "uuid"
          },
          "name": {
            "type": "string"
          },
          "creditAmount": {
            "type": "number",
            "format": "double"
          }
        },
        "additionalProperties": false
      },
      "NzCreditNoteResponse": {
        "type": "object",
        "allOf": [
          {
            "$ref": "#/components/schemas/BaseCreditNoteResponse"
          }
        ],
        "properties": {
          "id": {
            "type": "string",
            "format": "uuid"
          },
          "number": {
            "type": "string"
          },
          "reason": {
            "type": "string"
          },
          "createdOn": {
            "type": "string",
            "format": "date-time"
          },
          "payers": {
            "type": "array",
            "items": {
              "$ref": "#/components/schemas/NzCreditNotePayerResponse"
            }
          }
        },
        "additionalProperties": false
      },
      "NzDeliveryNoteResponse": {
        "type": "object",
        "allOf": [
          {
            "$ref": "#/components/schemas/BaseDeliveryNoteResponse"
          }
        ],
        "properties": {
          "id": {
            "type": "string",
            "format": "uuid"
          },
          "number": {
            "type": "string"
          },
          "actualDeliveryDate": {
            "type": "string",
            "format": "date-time"
          },
          "startWarrentyDate": {
            "type": "string",
            "format": "date-time"
          },
          "products": {
            "type": "array",
            "items": {
              "$ref": "#/components/schemas/DeliveryNoteProductResponse"
            }
          }
        },
        "additionalProperties": false
      },
      "NzManufacturerRequest": {
        "type": "object",
        "allOf": [
          {
            "$ref": "#/components/schemas/ManufacturerRequest"
          }
        ],
        "properties": {
          "state": {
            "type": "string",
            "nullable": true
          }
        },
        "additionalProperties": false
      },
      "NzManufacturerResponse": {
        "type": "object",
        "allOf": [
          {
            "$ref": "#/components/schemas/ManufacturerResponse"
          }
        ],
        "properties": {
          "state": {
            "type": "string",
            "nullable": true
          }
        },
        "additionalProperties": false
      },
      "NzSaleProductDetailsResponse": {
        "type": "object",
        "allOf": [
          {
            "$ref": "#/components/schemas/BaseSaleProductDetailsResponse"
          }
        ],
        "properties": {
          "id": {
            "type": "string",
            "format": "uuid"
          },
          "isOrdered": {
            "type": "boolean"
          },
          "productId": {
            "type": "string",
            "format": "uuid"
          },
          "stockProductId": {
            "type": "string",
            "format": "uuid"
          },
          "stockProductItemId": {
            "type": "string",
            "format": "uuid",
            "nullable": true
          },
          "serialNumber": {
            "type": "string"
          },
          "side": {
            "$ref": "#/components/schemas/ProductSideType"
          },
          "batteryTypeId": {
            "type": "string",
            "format": "uuid",
            "nullable": true
          },
          "quantity": {
            "type": "integer",
            "format": "int32"
          },
          "unitPrice": {
            "type": "number",
            "format": "double"
          },
          "taxAmount": {
            "type": "number",
            "format": "double"
          },
          "color": {
            "oneOf": [
              {
                "$ref": "#/components/schemas/DictionaryResponse"
              },
              {
                "$ref": "#/components/schemas/CountryResponse"
              }
            ]
          },
          "discounts": {
            "type": "array",
            "items": {
              "$ref": "#/components/schemas/ProductDiscountResponse"
            }
          }
        },
        "additionalProperties": false
      },
      "NzSaleProductResponse": {
        "type": "object",
        "allOf": [
          {
            "$ref": "#/components/schemas/BaseSaleProductResponse"
          }
        ],
        "properties": {
          "stockProductId": {
            "type": "string",
            "format": "uuid"
          },
          "status": {
            "type": "string"
          },
          "color": {
            "type": "string"
          },
          "side": {
            "$ref": "#/components/schemas/ProductSideType"
          },
          "serialNumber": {
            "type": "string"
          },
          "name": {
            "type": "string"
          },
          "categoryName": {
            "type": "string"
          },
          "quantity": {
            "type": "integer",
            "format": "int32"
          },
          "unitPrice": {
            "type": "number",
            "format": "double"
          },
          "totalAmount": {
            "type": "number",
            "format": "double"
          },
          "taxAmount": {
            "type": "number",
            "format": "double"
          },
          "isSerialized": {
            "type": "boolean"
          },
          "isAcc": {
            "type": "boolean"
          },
          "discounts": {
            "type": "array",
            "items": {
              "$ref": "#/components/schemas/ProductDiscountResponse"
            }
          },
          "tax": {
            "oneOf": [
              {
                "$ref": "#/components/schemas/BaseCodeResponse"
              },
              {
                "$ref": "#/components/schemas/AppointmentOutcomeResponse"
              },
              {
                "$ref": "#/components/schemas/AppointmentStatusesResponse"
              },
              {
                "$ref": "#/components/schemas/BaseReasonResponse"
              }
            ]
          },
          "batteryType": {
            "oneOf": [
              {
                "$ref": "#/components/schemas/DictionaryResponse"
              },
              {
                "$ref": "#/components/schemas/CountryResponse"
              }
            ]
          },
          "accServiceCode": {
            "type": "string"
          },
          "accServiceAmountWithTax": {
            "type": "number",
            "format": "double",
            "nullable": true
          },
          "accServiceAmountWithoutTax": {
            "type": "number",
            "format": "double",
            "nullable": true
          }
        },
        "additionalProperties": false
      },
      "NzSaleResponse": {
        "type": "object",
        "allOf": [
          {
            "$ref": "#/components/schemas/BaseSaleResponse"
          }
        ],
        "properties": {
          "id": {
            "type": "string",
            "format": "uuid"
          },
          "saleType": {
            "$ref": "#/components/schemas/SaleType"
          },
          "totalSaleAmount": {
            "type": "number",
            "format": "double"
          },
          "totalTaxAmount": {
            "type": "number",
            "format": "double"
          },
          "outstandingAmount": {
            "type": "number",
            "format": "double"
          },
          "priceBookId": {
            "type": "string",
            "format": "uuid",
            "nullable": true
          },
          "pathwayTemplateId": {
            "type": "string",
            "format": "uuid",
            "nullable": true
          },
          "pathwayStepId": {
            "type": "string",
            "format": "uuid",
            "nullable": true
          },
          "createdBy": {
            "type": "string"
          },
          "lnDOrderId": {
            "type": "string",
            "format": "uuid",
            "nullable": true
          },
          "appointment": {
            "$ref": "#/components/schemas/SaleAppointmentResponse"
          },
          "appointmentReason": {
            "oneOf": [
              {
                "$ref": "#/components/schemas/BaseCodeResponse"
              },
              {
                "$ref": "#/components/schemas/AppointmentOutcomeResponse"
              },
              {
                "$ref": "#/components/schemas/AppointmentStatusesResponse"
              },
              {
                "$ref": "#/components/schemas/BaseReasonResponse"
              }
            ]
          },
          "specialist": {
            "oneOf": [
              {
                "$ref": "#/components/schemas/DictionaryResponse"
              },
              {
                "$ref": "#/components/schemas/CountryResponse"
              }
            ]
          }
        },
        "additionalProperties": false
      },
      "NzSupplierRequest": {
        "type": "object",
        "allOf": [
          {
            "$ref": "#/components/schemas/SupplierRequest"
          }
        ],
        "properties": {
          "state": {
            "type": "string",
            "nullable": true
          }
        },
        "additionalProperties": false
      },
      "NzSupplierResponse": {
        "type": "object",
        "allOf": [
          {
            "$ref": "#/components/schemas/SupplierResponse"
          }
        ],
        "properties": {
          "state": {
            "type": "string",
            "nullable": true
          }
        },
        "additionalProperties": false
      },
      "OperationType": {
        "enum": [
          "Add",
          "Remove",
          "Replace",
          "Move",
          "Copy",
          "Test",
          "Invalid"
        ],
        "type": "string"
      },
      "OrderLineItemAttributeRequest": {
        "type": "object",
        "properties": {
          "id": {
            "type": "string",
            "format": "uuid"
          },
          "name": {
            "type": "string"
          },
          "value": {
            "$ref": "#/components/schemas/DictionaryRequest"
          }
        },
        "additionalProperties": false
      },
      "OrderLineItemAttributeResponse": {
        "type": "object",
        "properties": {
          "attributeId": {
            "type": "string",
            "format": "uuid"
          },
          "name": {
            "type": "string"
          },
          "value": {
            "oneOf": [
              {
                "$ref": "#/components/schemas/DictionaryResponse"
              },
              {
                "$ref": "#/components/schemas/CountryResponse"
              }
            ]
          }
        },
        "additionalProperties": false
      },
      "OrderLineItemRequest": {
        "type": "object",
        "properties": {
          "productId": {
            "type": "string",
            "format": "uuid"
          },
          "quantity": {
            "type": "integer",
            "format": "int32"
          },
          "unitCost": {
            "type": "number",
            "format": "double"
          },
          "comment": {
            "type": "string",
            "nullable": true
          },
          "colorId": {
            "type": "string",
            "format": "uuid",
            "nullable": true
          },
          "batteryTypeId": {
            "type": "string",
            "format": "uuid",
            "nullable": true
          },
          "attributes": {
            "type": "array",
            "items": {
              "$ref": "#/components/schemas/OrderLineItemAttributeRequest"
            }
          }
        },
        "additionalProperties": false
      },
      "OrderLineItemResponse": {
        "type": "object",
        "properties": {
          "id": {
            "type": "string",
            "format": "uuid"
          },
          "product": {
            "oneOf": [
              {
                "$ref": "#/components/schemas/DictionaryResponse"
              },
              {
                "$ref": "#/components/schemas/CountryResponse"
              }
            ]
          },
          "productSku": {
            "type": "string",
            "nullable": true
          },
          "stockProductId": {
            "type": "string",
            "format": "uuid"
          },
          "quantity": {
            "type": "integer",
            "format": "int32"
          },
          "unitCost": {
            "type": "number",
            "format": "double",
            "nullable": true
          },
          "status": {
            "type": "string"
          },
          "comment": {
            "type": "string",
            "nullable": true
          },
          "total": {
            "type": "number",
            "format": "double",
            "nullable": true
          },
          "color": {
            "oneOf": [
              {
                "$ref": "#/components/schemas/DictionaryResponse"
              },
              {
                "$ref": "#/components/schemas/CountryResponse"
              }
            ],
            "nullable": true
          },
          "batteryType": {
            "oneOf": [
              {
                "$ref": "#/components/schemas/DictionaryResponse"
              },
              {
                "$ref": "#/components/schemas/CountryResponse"
              }
            ],
            "nullable": true
          },
          "attributes": {
            "type": "array",
            "items": {
              "$ref": "#/components/schemas/OrderLineItemAttributeResponse"
            }
          },
          "serialNumbers": {
            "type": "array",
            "items": {
              "type": "string"
            }
          }
        },
        "additionalProperties": false
      },
      "OrderRequest": {
        "type": "object",
        "properties": {
          "quoteNumber": {
            "type": "string",
            "nullable": true
          },
          "externalNumber": {
            "type": "string",
            "nullable": true
          },
          "locationId": {
            "type": "string",
            "format": "uuid"
          },
          "supplierId": {
            "type": "string",
            "format": "uuid"
          },
          "patientId": {
            "type": "string",
            "format": "uuid",
            "nullable": true
          },
          "specialistId": {
            "type": "string",
            "format": "uuid",
            "nullable": true
          },
          "lineItems": {
            "type": "array",
            "items": {
              "$ref": "#/components/schemas/OrderLineItemRequest"
            },
            "nullable": true
          }
        },
        "additionalProperties": false
      },
      "OrderResponse": {
        "type": "object",
        "properties": {
          "id": {
            "type": "string",
            "format": "uuid"
          },
          "number": {
            "type": "string",
            "nullable": true
          },
          "quoteNumber": {
            "type": "string",
            "nullable": true
          },
          "externalNumber": {
            "type": "string",
            "nullable": true
          },
          "invoiceReferenceNumber": {
            "type": "string",
            "nullable": true
          },
          "status": {
            "type": "string"
          },
          "location": {
            "oneOf": [
              {
                "$ref": "#/components/schemas/DictionaryResponse"
              },
              {
                "$ref": "#/components/schemas/CountryResponse"
              }
            ]
          },
          "supplier": {
            "oneOf": [
              {
                "$ref": "#/components/schemas/DictionaryResponse"
              },
              {
                "$ref": "#/components/schemas/CountryResponse"
              }
            ]
          },
          "patient": {
            "oneOf": [
              {
                "$ref": "#/components/schemas/DictionaryResponse"
              },
              {
                "$ref": "#/components/schemas/CountryResponse"
              }
            ],
            "nullable": true
          },
          "specialist": {
            "oneOf": [
              {
                "$ref": "#/components/schemas/DictionaryResponse"
              },
              {
                "$ref": "#/components/schemas/CountryResponse"
              }
            ],
            "nullable": true
          },
          "lineItems": {
            "type": "array",
            "items": {
              "$ref": "#/components/schemas/OrderLineItemResponse"
            }
          },
          "total": {
            "type": "number",
            "format": "double"
          }
        },
        "additionalProperties": false
      },
      "PaginationBase": {
        "type": "object",
        "properties": {
          "currentPage": {
            "type": "integer",
            "format": "int32"
          },
          "total": {
            "type": "integer",
            "format": "int32"
          },
          "perPage": {
            "type": "integer",
            "format": "int32"
          },
          "orderBy": {
            "type": "string"
          }
        },
        "additionalProperties": false
      },
      "PagingOptions": {
        "type": "object",
        "properties": {
          "currentPage": {
            "type": "integer",
            "format": "int32"
          },
          "perPage": {
            "type": "integer",
            "format": "int32"
          },
          "total": {
            "type": "integer",
            "format": "int32"
          },
          "orderBy": {
            "type": "string"
          }
        },
        "additionalProperties": false
      },
      "PartialDeliverLineItemRequest": {
        "type": "object",
        "properties": {
          "newQuantity": {
            "type": "integer",
            "format": "int32"
          }
        },
        "additionalProperties": false
      },
      "PatchAppointmentRequest": {
        "type": "object",
        "allOf": [
          {
            "$ref": "#/components/schemas/BaseAppointmentRequest"
          }
        ],
        "properties": {
          "specialists": {
            "type": "array",
            "items": {
              "type": "string",
              "format": "uuid"
            },
            "nullable": true
          },
          "resources": {
            "type": "array",
            "items": {
              "type": "string",
              "format": "uuid"
            },
            "nullable": true
          }
        },
        "additionalProperties": false
      },
      "PatchProductRequest": {
        "type": "object",
        "allOf": [
          {
            "$ref": "#/components/schemas/BaseProductRequest"
          }
        ],
        "properties": {
          "name": {
            "type": "string",
            "nullable": true
          },
          "categoryId": {
            "type": "string",
            "format": "uuid",
            "nullable": true
          },
          "firstVAT": {
            "type": "number",
            "format": "double",
            "nullable": true
          },
          "secondVAT": {
            "type": "number",
            "format": "double",
            "nullable": true
          },
          "colors": {
            "type": "array",
            "items": {
              "type": "string",
              "format": "uuid"
            },
            "nullable": true
          },
          "batteryTypes": {
            "type": "array",
            "items": {
              "type": "string",
              "format": "uuid"
            },
            "nullable": true
          },
          "suggestedProducts": {
            "type": "array",
            "items": {
              "type": "string",
              "format": "uuid"
            },
            "nullable": true
          },
          "attributes": {
            "type": "array",
            "items": {
              "$ref": "#/components/schemas/ProductAttributeRequest"
            },
            "nullable": true
          }
        },
        "additionalProperties": false
      },
      "PathwayResponse": {
        "type": "object",
        "properties": {
          "ccgId": {
            "type": "string",
            "format": "uuid"
          },
          "type": {
            "type": "string"
          },
          "correlationId": {
            "type": "string",
            "format": "uuid"
          },
          "templateId": {
            "type": "string",
            "format": "uuid"
          }
        },
        "additionalProperties": false
      },
      "PatientConsentRequest": {
        "type": "object",
        "properties": {
          "companyConsentId": {
            "type": "integer",
            "description": "Unique identifier of Parent Company consent.",
            "format": "int32"
          },
          "contactMethod": {
            "type": "string",
            "description": "Contact method for which consent is given. Enum: Phone, Sms, Email, Mail.",
            "nullable": true
          },
          "contactValue": {
            "type": "string",
            "description": "Actual contact value for which consent is given. E.g. phone number, email or mail address.",
            "nullable": true
          },
          "contactOrigin": {
            "type": "string",
            "description": "Manage field that is used for consent value. E.g. Phone1, Email1, etc.",
            "nullable": true
          },
          "isDisabled": {
            "type": "boolean",
            "description": "Indicates that consent is disabled for a Patient."
          }
        },
        "additionalProperties": false
      },
      "PatientConsentResponse": {
        "type": "object",
        "properties": {
          "companyConsentId": {
            "type": "integer",
            "description": "Unique identifier of Parent Company consent.",
            "format": "int32"
          },
          "companyConsentName": {
            "type": "string",
            "description": "Name of Parent Company consent.",
            "nullable": true
          },
          "contactMethod": {
            "type": "string",
            "description": "Contact method for which consent is given. Enum: Phone, Sms, Email, Mail.",
            "nullable": true
          },
          "contactValue": {
            "type": "string",
            "description": "Actual contact value for which consent is given. E.g. phone number, email or mail address.",
            "nullable": true
          },
          "contactOrigin": {
            "type": "string",
            "description": "Manage field that is used for consent value. E.g. Phone1, Email1, etc.",
            "nullable": true
          },
          "consentText": {
            "type": "string",
            "description": "Text that describes consent.",
            "nullable": true
          },
          "isDisabled": {
            "type": "boolean",
            "description": "Indicates that consent is disabled for a Patient."
          },
          "isExpired": {
            "type": "boolean",
            "description": "Indicates that consent is expired for a Patient."
          }
        },
        "additionalProperties": false
      },
      "PatientConsentResponseListResponse": {
        "type": "object",
        "properties": {
          "data": {
            "$ref": "#/components/schemas/PatientConsentResponse"
          }
        },
        "additionalProperties": false
      },
      "PatientDVACardType": {
        "enum": [
          "Gold",
          "Orange",
          "White"
        ],
        "type": "string"
      },
      "PayerProductSaleResponse": {
        "type": "object",
        "properties": {
          "id": {
            "type": "string",
            "format": "uuid"
          },
          "name": {
            "type": "string"
          },
          "amount": {
            "type": "number",
            "format": "double"
          },
          "hasQuote": {
            "type": "boolean"
          },
          "hasInvoice": {
            "type": "boolean"
          }
        },
        "additionalProperties": false
      },
      "PaymentRequest": {
        "type": "object",
        "properties": {
          "requestId": {
            "type": "string",
            "format": "uuid"
          },
          "status": {
            "type": "string"
          },
          "transactionId": {
            "type": "integer",
            "format": "int32",
            "nullable": true
          }
        },
        "additionalProperties": false
      },
      "PracticeResponse": {
        "type": "object",
        "properties": {
          "id": {
            "type": "string",
            "format": "uuid"
          },
          "name": {
            "type": "string"
          },
          "code": {
            "type": "string"
          },
          "addressLine1": {
            "type": "string"
          },
          "addressLine2": {
            "type": "string"
          },
          "addressLine3": {
            "type": "string"
          },
          "addressLine4": {
            "type": "string"
          },
          "addressLine5": {
            "type": "string"
          },
          "postcode": {
            "type": "string"
          },
          "provider": {
            "type": "string"
          },
          "commissioner": {
            "type": "string"
          },
          "contactTelephoneNumber": {
            "type": "string"
          }
        },
        "additionalProperties": false
      },
      "PracticeResponsePagingResponse": {
        "type": "object",
        "properties": {
          "data": {
            "type": "array",
            "items": {
              "$ref": "#/components/schemas/PracticeResponse"
            },
            "nullable": true
          },
          "paginagion": {
            "$ref": "#/components/schemas/PagingOptions"
          }
        },
        "additionalProperties": false
      },
      "PreferredPhone": {
        "enum": [
          "MobilePhone",
          "WorkPhone",
          "HomePhone"
        ],
        "type": "string"
      },
      "ProblemDetails": {
        "type": "object",
        "properties": {
          "type": {
            "type": "string",
            "nullable": true
          },
          "title": {
            "type": "string",
            "nullable": true
          },
          "status": {
            "type": "integer",
            "format": "int32",
            "nullable": true
          },
          "detail": {
            "type": "string",
            "nullable": true
          },
          "instance": {
            "type": "string",
            "nullable": true
          }
        },
        "additionalProperties": {}
      },
      "ProcessPaymentRequest": {
        "type": "object",
        "properties": {
          "id": {
            "type": "string",
            "format": "uuid"
          },
          "created": {
            "type": "string",
            "format": "date-time"
          },
          "type": {
            "type": "string"
          },
          "data": {
            "$ref": "#/components/schemas/PaymentRequest"
          }
        },
        "additionalProperties": false
      },
      "ProductAttributeRequest": {
        "type": "object",
        "properties": {
          "attributeId": {
            "type": "string",
            "format": "uuid"
          },
          "values": {
            "type": "array",
            "items": {
              "$ref": "#/components/schemas/AttributeValueRequest"
            }
          }
        },
        "additionalProperties": false
      },
      "ProductAttributeResponse": {
        "type": "object",
        "properties": {
          "attribute": {
            "oneOf": [
              {
                "$ref": "#/components/schemas/DictionaryResponse"
              },
              {
                "$ref": "#/components/schemas/CountryResponse"
              }
            ]
          },
          "values": {
            "type": "array",
            "items": {
              "$ref": "#/components/schemas/AttributeValueResponse"
            }
          }
        },
        "additionalProperties": false
      },
      "ProductCategoryCodeRequest": {
        "enum": [
          "Undefined",
          "HearingAids",
          "Earmolds",
          "Batteries",
          "Accessories",
          "Service",
          "LDWarranty",
          "Remote",
          "RepairService",
          "Other",
          "RICReceivers"
        ],
        "type": "string"
      },
      "ProductCategoryResponse": {
        "type": "object",
        "properties": {
          "id": {
            "type": "string",
            "format": "uuid"
          },
          "name": {
            "type": "string"
          },
          "code": {
            "type": "string"
          }
        },
        "additionalProperties": false
      },
      "ProductCategoryResponseIEnumerableListResponse": {
        "type": "object",
        "properties": {
          "data": {
            "type": "array",
            "items": {
              "$ref": "#/components/schemas/ProductCategoryResponse"
            },
            "nullable": true
          }
        },
        "additionalProperties": false
      },
      "ProductDiscountResponse": {
        "type": "object",
        "properties": {
          "id": {
            "type": "string",
            "format": "uuid"
          },
          "type": {
            "$ref": "#/components/schemas/DiscountType"
          },
          "value": {
            "type": "number",
            "format": "double"
          },
          "name": {
            "type": "string"
          }
        },
        "additionalProperties": false
      },
      "ProductItemAttributeRequest": {
        "type": "object",
        "properties": {
          "attributeId": {
            "type": "string",
            "format": "uuid"
          },
          "value": {
            "$ref": "#/components/schemas/ProductItemAttributeValueRequest"
          }
        },
        "additionalProperties": false
      },
      "ProductItemAttributeValueRequest": {
        "type": "object",
        "properties": {
          "id": {
            "type": "string",
            "format": "uuid"
          },
          "name": {
            "type": "string"
          }
        },
        "additionalProperties": false
      },
      "ProductListResponse": {
        "type": "object",
        "properties": {
          "id": {
            "type": "string",
            "format": "uuid"
          },
          "name": {
            "type": "string"
          },
          "category": {
            "oneOf": [
              {
                "$ref": "#/components/schemas/DictionaryResponse"
              },
              {
                "$ref": "#/components/schemas/CountryResponse"
              }
            ],
            "nullable": true
          },
          "manufacturer": {
            "oneOf": [
              {
                "$ref": "#/components/schemas/DictionaryResponse"
              },
              {
                "$ref": "#/components/schemas/CountryResponse"
              }
            ],
            "nullable": true
          },
          "warranty": {
            "type": "integer",
            "format": "int32",
            "nullable": true
          },
          "isActive": {
            "type": "boolean"
          },
          "isSellable": {
            "type": "boolean"
          },
          "retailPrice": {
            "type": "number",
            "format": "double"
          }
        },
        "additionalProperties": false
      },
      "ProductListResponsePagingResponse": {
        "type": "object",
        "properties": {
          "data": {
            "type": "array",
            "items": {
              "$ref": "#/components/schemas/ProductListResponse"
            },
            "nullable": true
          },
          "paginagion": {
            "$ref": "#/components/schemas/PagingOptions"
          }
        },
        "additionalProperties": false
      },
      "ProductRequest": {
        "type": "object",
        "allOf": [
          {
            "$ref": "#/components/schemas/BaseProductRequest"
          }
        ],
        "properties": {
          "name": {
            "type": "string"
          },
          "categoryId": {
            "type": "string",
            "format": "uuid"
          },
          "colors": {
            "type": "array",
            "items": {
              "type": "string",
              "format": "uuid"
            }
          },
          "batteryTypes": {
            "type": "array",
            "items": {
              "type": "string",
              "format": "uuid"
            }
          },
          "suggestedProductIds": {
            "type": "array",
            "items": {
              "type": "string",
              "format": "uuid"
            }
          },
          "attributes": {
            "type": "array",
            "items": {
              "$ref": "#/components/schemas/ProductAttributeRequest"
            }
          }
        },
        "additionalProperties": false
      },
      "ProductResponse": {
        "type": "object",
        "properties": {
          "id": {
            "type": "string",
            "format": "uuid"
          },
          "name": {
            "type": "string"
          },
          "description": {
            "type": "string",
            "nullable": true
          },
          "category": {
            "$ref": "#/components/schemas/ProductCategoryResponse"
          },
          "manufacturer": {
            "oneOf": [
              {
                "$ref": "#/components/schemas/DictionaryResponse"
              },
              {
                "$ref": "#/components/schemas/CountryResponse"
              }
            ],
            "nullable": true
          },
          "supplier": {
            "oneOf": [
              {
                "$ref": "#/components/schemas/DictionaryResponse"
              },
              {
                "$ref": "#/components/schemas/CountryResponse"
              }
            ],
            "nullable": true
          },
          "hearingAidType": {
            "oneOf": [
              {
                "$ref": "#/components/schemas/DictionaryResponse"
              },
              {
                "$ref": "#/components/schemas/CountryResponse"
              }
            ],
            "nullable": true
          },
          "warranty": {
            "type": "integer",
            "format": "int32",
            "nullable": true
          },
          "ldWarranty": {
            "type": "integer",
            "format": "int32",
            "nullable": true
          },
          "quantity": {
            "type": "integer",
            "format": "int32"
          },
          "retailPrice": {
            "type": "number",
            "format": "double"
          },
          "firstVAT": {
            "type": "number",
            "format": "double"
          },
          "secondVAT": {
            "type": "number",
            "format": "double"
          },
          "isSellable": {
            "type": "boolean"
          },
          "isActive": {
            "type": "boolean"
          },
          "isSerialized": {
            "type": "boolean"
          },
          "priceChangesAllowed": {
            "type": "boolean"
          },
          "autoDeliver": {
            "type": "boolean"
          },
          "controlledByStock": {
            "type": "boolean"
          },
          "vendorProductNumber": {
            "type": "string",
            "nullable": true
          },
          "maximumDiscount": {
            "type": "number",
            "format": "double",
            "nullable": true
          },
          "cost": {
            "type": "number",
            "format": "double",
            "nullable": true
          },
          "colors": {
            "type": "array",
            "items": {
              "oneOf": [
                {
                  "$ref": "#/components/schemas/DictionaryResponse"
                },
                {
                  "$ref": "#/components/schemas/CountryResponse"
                }
              ]
            },
            "nullable": true
          },
          "batteryTypes": {
            "type": "array",
            "items": {
              "oneOf": [
                {
                  "$ref": "#/components/schemas/DictionaryResponse"
                },
                {
                  "$ref": "#/components/schemas/CountryResponse"
                }
              ]
            },
            "nullable": true
          },
          "attributes": {
            "type": "array",
            "items": {
              "$ref": "#/components/schemas/ProductAttributeResponse"
            },
            "nullable": true
          },
          "suggestedProductIds": {
            "type": "array",
            "items": {
              "type": "string",
              "format": "uuid"
            },
            "nullable": true
          }
        },
        "additionalProperties": false
      },
      "ProductSaleHspProductDetailsResponse": {
        "type": "object",
        "properties": {
          "code": {
            "type": "string"
          },
          "category": {
            "type": "string"
          },
          "date": {
            "type": "string",
            "format": "date-time"
          },
          "appointmentId": {
            "type": "string",
            "format": "uuid"
          },
          "linkedFittingServiceImported": {
            "type": "boolean"
          },
          "importResult": {
            "$ref": "#/components/schemas/HspServiceImportResult"
          },
          "rejectionReason": {
            "type": "string"
          }
        },
        "additionalProperties": false
      },
      "ProductSaleHspServiceDetailsResponse": {
        "type": "object",
        "properties": {
          "serviceType": {
            "$ref": "#/components/schemas/HspServiceType"
          },
          "serviceNumber": {
            "type": "string"
          },
          "date": {
            "type": "string",
            "format": "date-time"
          },
          "appointmentId": {
            "type": "string",
            "format": "uuid"
          },
          "location": {
            "oneOf": [
              {
                "$ref": "#/components/schemas/DictionaryResponse"
              },
              {
                "$ref": "#/components/schemas/CountryResponse"
              }
            ]
          },
          "specialist": {
            "oneOf": [
              {
                "$ref": "#/components/schemas/DictionaryResponse"
              },
              {
                "$ref": "#/components/schemas/CountryResponse"
              }
            ]
          },
          "importResult": {
            "$ref": "#/components/schemas/HspServiceImportResult"
          },
          "rejectionReason": {
            "type": "string"
          },
          "customPrice": {
            "type": "number",
            "format": "double",
            "nullable": true
          },
          "customTax": {
            "type": "number",
            "format": "double",
            "nullable": true
          }
        },
        "additionalProperties": false
      },
      "ProductSaleTaxResponse": {
        "type": "object",
        "properties": {
          "taxId": {
            "type": "string",
            "format": "uuid",
            "nullable": true
          },
          "taxCode": {
            "type": "string"
          },
          "amount": {
            "type": "number",
            "format": "double"
          },
          "taxableGoodsAmount": {
            "type": "number",
            "format": "double"
          },
          "exemptServicesAmount": {
            "type": "number",
            "format": "double"
          }
        },
        "additionalProperties": false
      },
      "ProductSideType": {
        "enum": [
          "NA",
          "Left",
          "Right"
        ],
        "type": "string"
      },
      "ROIProductRequest": {
        "type": "object",
        "allOf": [
          {
            "$ref": "#/components/schemas/ProductRequest"
          }
        ],
        "additionalProperties": false
      },
      "ReceiveGnAsn": {
        "type": "object",
        "properties": {
          "orderNumber": {
            "type": "string"
          },
          "orderDate": {
            "type": "string"
          },
          "orderLocation": {
            "type": "string"
          },
          "orderVendor": {
            "type": "string"
          },
          "orderSubtotal": {
            "type": "string"
          },
          "orderTaxTotal": {
            "type": "string"
          },
          "orderTotal": {
            "type": "string"
          },
          "orderItemsShipped": {
            "type": "string"
          },
          "productItems": {
            "type": "array",
            "items": {
              "$ref": "#/components/schemas/ReceiveOrderItem"
            }
          }
        },
        "additionalProperties": false
      },
      "ReceiveGnEdiStatus": {
        "type": "object",
        "properties": {
          "orderNumber": {
            "type": "string",
            "description": "Uniquely identifies the order"
          },
          "errorStatus": {
            "type": "string",
            "description": "Error Status\r\nNo errors - 0. Means the order was processed and approved"
          },
          "errorDetails": {
            "type": "string",
            "description": "Detailed error description if any"
          }
        },
        "additionalProperties": false
      },
      "ReceiveOrderItem": {
        "type": "object",
        "properties": {
          "productNumber": {
            "type": "string"
          },
          "productQuantity": {
            "type": "string"
          },
          "productCost": {
            "type": "string"
          },
          "productTax": {
            "type": "string"
          },
          "productTotal": {
            "type": "string"
          },
          "productSellToCustomer": {
            "type": "string"
          },
          "serialNumber": {
            "type": "string"
          }
        },
        "additionalProperties": false
      },
      "ReferralResponse": {
        "type": "object",
        "properties": {
          "id": {
            "type": "string",
            "format": "uuid"
          },
          "serviceTypeCode": {
            "type": "string"
          },
          "referralStatus": {
            "type": "string"
          }
        },
        "additionalProperties": false
      },
      "RejectLineItemRequest": {
        "type": "object",
        "properties": {
          "quantity": {
            "type": "integer",
            "format": "int32"
          },
          "note": {
            "type": "string",
            "nullable": true
          }
        },
        "additionalProperties": false
      },
      "RejectOrderLineItemRequest": {
        "type": "object",
        "properties": {
          "id": {
            "type": "string",
            "format": "uuid"
          },
          "quantity": {
            "type": "integer",
            "format": "int32"
          },
          "note": {
            "type": "string",
            "nullable": true
          }
        },
        "additionalProperties": false
      },
      "RejectOrderRequest": {
        "type": "object",
        "properties": {
          "lineItems": {
            "type": "array",
            "items": {
              "$ref": "#/components/schemas/RejectOrderLineItemRequest"
            }
          }
        },
        "additionalProperties": false
      },
      "RepairOrderStatusResponse": {
        "type": "object",
        "properties": {
          "id": {
            "type": "string",
            "format": "uuid"
          },
          "name": {
            "type": "string"
          },
          "isDisabled": {
            "type": "boolean"
          },
          "isCompleted": {
            "type": "boolean"
          },
          "isInUse": {
            "type": "boolean"
          }
        },
        "additionalProperties": false
      },
      "RepairOrderStatusResponseIEnumerableListResponse": {
        "type": "object",
        "properties": {
          "data": {
            "type": "array",
            "items": {
              "$ref": "#/components/schemas/RepairOrderStatusResponse"
            },
            "nullable": true
          }
        },
        "additionalProperties": false
      },
      "RepairReasonResponse": {
        "type": "object",
        "properties": {
          "id": {
            "type": "string",
            "format": "uuid"
          },
          "reason": {
            "type": "string"
          },
          "code": {
            "type": "string"
          },
          "isDisabled": {
            "type": "boolean"
          }
        },
        "additionalProperties": false
      },
      "RepairReasonResponseIEnumerableListResponse": {
        "type": "object",
        "properties": {
          "data": {
            "type": "array",
            "items": {
              "$ref": "#/components/schemas/RepairReasonResponse"
            },
            "nullable": true
          }
        },
        "additionalProperties": false
      },
      "ResourceResponse": {
        "type": "object",
        "properties": {
          "id": {
            "type": "string",
            "format": "uuid"
          },
          "locationId": {
            "type": "string",
            "format": "uuid"
          },
          "code": {
            "type": "string",
            "nullable": true
          },
          "typeId": {
            "type": "string",
            "format": "uuid",
            "nullable": true
          },
          "description": {
            "type": "string",
            "nullable": true
          },
          "color": {
            "type": "string",
            "nullable": true
          },
          "availabilityType": {
            "type": "string",
            "nullable": true
          },
          "isActive": {
            "type": "boolean"
          }
        },
        "additionalProperties": false
      },
      "ResourceResponsePagedResponse": {
        "type": "object",
        "properties": {
          "result": {
            "type": "array",
            "items": {
              "$ref": "#/components/schemas/ResourceResponse"
            }
          },
          "pageNumber": {
            "type": "integer",
            "format": "int32"
          },
          "pageSize": {
            "type": "integer",
            "format": "int32"
          }
        },
        "additionalProperties": false
      },
      "ResourceTypeResponse": {
        "type": "object",
        "properties": {
          "id": {
            "type": "string",
            "format": "uuid"
          },
          "code": {
            "type": "string"
          },
          "isActive": {
            "type": "boolean"
          }
        },
        "additionalProperties": false
      },
      "ResourceTypeResponseListResponse": {
        "type": "object",
        "properties": {
          "data": {
            "$ref": "#/components/schemas/ResourceTypeResponse"
          }
        },
        "additionalProperties": false
      },
      "RoiManufacturerRequest": {
        "type": "object",
        "allOf": [
          {
            "$ref": "#/components/schemas/ManufacturerRequest"
          }
        ],
        "properties": {
          "state": {
            "type": "string",
            "nullable": true
          }
        },
        "additionalProperties": false
      },
      "RoiManufacturerResponse": {
        "type": "object",
        "allOf": [
          {
            "$ref": "#/components/schemas/ManufacturerResponse"
          }
        ],
        "properties": {
          "state": {
            "type": "string",
            "nullable": true
          }
        },
        "additionalProperties": false
      },
      "RoiSupplierRequest": {
        "type": "object",
        "allOf": [
          {
            "$ref": "#/components/schemas/SupplierRequest"
          }
        ],
        "properties": {
          "state": {
            "type": "string",
            "nullable": true
          }
        },
        "additionalProperties": false
      },
      "RoiSupplierResponse": {
        "type": "object",
        "allOf": [
          {
            "$ref": "#/components/schemas/SupplierResponse"
          }
        ],
        "properties": {
          "state": {
            "type": "string",
            "nullable": true
          }
        },
        "additionalProperties": false
      },
      "SaleAppointmentResponse": {
        "type": "object",
        "properties": {
          "id": {
            "type": "string",
            "format": "uuid"
          },
          "start": {
            "type": "string",
            "format": "date-time"
          },
          "end": {
            "type": "string",
            "format": "date-time"
          }
        },
        "additionalProperties": false
      },
      "SaleTaxResponse": {
        "type": "object",
        "properties": {
          "taxId": {
            "type": "string",
            "format": "uuid",
            "nullable": true
          },
          "taxCode": {
            "type": "string"
          },
          "amount": {
            "type": "number",
            "format": "double"
          }
        },
        "additionalProperties": false
      },
      "SaleType": {
        "enum": [
          "Hsp",
          "Nhs",
          "Common",
          "FastTrack"
        ],
        "type": "string"
      },
      "SaleVatType": {
        "enum": [
          "Exclusive",
          "Inclusive"
        ],
        "type": "string"
      },
      "SearchOrderResponse": {
        "type": "object",
        "properties": {
          "id": {
            "type": "string",
            "format": "uuid"
          },
          "number": {
            "type": "string"
          },
          "externalNumber": {
            "type": "string",
            "nullable": true
          },
          "status": {
            "type": "string"
          },
          "location": {
            "oneOf": [
              {
                "$ref": "#/components/schemas/DictionaryResponse"
              },
              {
                "$ref": "#/components/schemas/CountryResponse"
              }
            ]
          },
          "supplier": {
            "oneOf": [
              {
                "$ref": "#/components/schemas/DictionaryResponse"
              },
              {
                "$ref": "#/components/schemas/CountryResponse"
              }
            ]
          },
          "patient": {
            "oneOf": [
              {
                "$ref": "#/components/schemas/DictionaryResponse"
              },
              {
                "$ref": "#/components/schemas/CountryResponse"
              }
            ],
            "nullable": true
          },
          "createdOn": {
            "type": "string",
            "format": "date-time"
          }
        },
        "additionalProperties": false
      },
      "SearchOrderResponseTablePageResult": {
        "type": "object",
        "properties": {
          "rows": {
            "type": "array",
            "items": {
              "$ref": "#/components/schemas/SearchOrderResponseTableRow"
            },
            "nullable": true
          },
          "pagination": {
            "$ref": "#/components/schemas/PaginationBase"
          }
        },
        "additionalProperties": false
      },
      "SearchOrderResponseTableRow": {
        "type": "object",
        "properties": {
          "model": {
            "$ref": "#/components/schemas/SearchOrderResponse"
          },
          "isActive": {
            "type": "boolean"
          },
          "actionTypes": {
            "type": "array",
            "items": {
              "type": "string"
            }
          },
          "isEditable": {
            "type": "boolean"
          }
        },
        "additionalProperties": false
      },
      "ShipLineItemRequest": {
        "type": "object",
        "properties": {
          "deliveryNumber": {
            "type": "string",
            "nullable": true
          },
          "shippedDate": {
            "type": "string",
            "format": "date-time",
            "nullable": true
          },
          "note": {
            "type": "string",
            "nullable": true
          }
        },
        "additionalProperties": false
      },
      "ShipOrderLineItemRequest": {
        "type": "object",
        "properties": {
          "id": {
            "type": "string",
            "format": "uuid"
          },
          "deliveryNumber": {
            "type": "string",
            "nullable": true
          },
          "shippedDate": {
            "type": "string",
            "format": "date-time"
          },
          "note": {
            "type": "string",
            "nullable": true
          }
        },
        "additionalProperties": false
      },
      "ShipOrderRequest": {
        "type": "object",
        "properties": {
          "lineItems": {
            "type": "array",
            "items": {
              "$ref": "#/components/schemas/ShipOrderLineItemRequest"
            }
          }
        },
        "additionalProperties": false
      },
      "SkuConfigAttribute": {
        "type": "object",
        "properties": {
          "id": {
            "type": "string",
            "format": "uuid"
          }
        },
        "additionalProperties": false
      },
      "SpecialistLinksResponse": {
        "type": "object",
        "properties": {
          "id": {
            "type": "string",
            "format": "uuid"
          },
          "name": {
            "type": "string"
          }
        },
        "additionalProperties": false
      },
      "SpecialistResponse": {
        "type": "object",
        "properties": {
          "id": {
            "type": "string",
            "format": "uuid"
          },
          "name": {
            "type": "string"
          },
          "locations": {
            "type": "array",
            "items": {
              "type": "string",
              "format": "uuid"
            }
          },
          "isActive": {
            "type": "boolean"
          }
        },
        "additionalProperties": false
      },
      "SpecialistResponsePagingResponse": {
        "type": "object",
        "properties": {
          "data": {
            "type": "array",
            "items": {
              "$ref": "#/components/schemas/SpecialistResponse"
            },
            "nullable": true
          },
          "paginagion": {
            "$ref": "#/components/schemas/PagingOptions"
          }
        },
        "additionalProperties": false
      },
      "SpecialistTimeSlotsResponse": {
        "type": "object",
        "properties": {
          "specialistId": {
            "type": "string",
            "format": "uuid"
          },
          "resourceIds": {
            "type": "array",
            "items": {
              "type": "string",
              "format": "uuid"
            }
          },
          "start": {
            "type": "string",
            "format": "date-time"
          },
          "end": {
            "type": "string",
            "format": "date-time"
          },
          "isAllReason": {
            "type": "boolean"
          }
        },
        "additionalProperties": false
      },
      "StringObjectIDictionaryListResponse": {
        "type": "object",
        "properties": {
          "data": {
            "type": "object",
            "additionalProperties": {
              "nullable": true
            },
            "nullable": true
          }
        },
        "additionalProperties": false
      },
      "SupplierRequest": {
        "type": "object",
        "properties": {
          "name": {
            "type": "string",
            "nullable": true
          },
          "isActive": {
            "type": "boolean"
          },
          "isManufacturer": {
            "type": "boolean"
          },
          "website": {
            "type": "string",
            "nullable": true
          },
          "emailAddress": {
            "type": "string",
            "nullable": true
          },
          "phoneNumber": {
            "type": "string",
            "nullable": true
          },
          "faxNumber": {
            "type": "string",
            "nullable": true
          },
          "address1": {
            "type": "string",
            "nullable": true
          },
          "address2": {
            "type": "string",
            "nullable": true
          },
          "country": {
            "type": "string",
            "description": "Country ISO 3166-1 alpha-2 code",
            "nullable": true
          },
          "city": {
            "type": "string",
            "nullable": true
          },
          "postalCode": {
            "type": "string",
            "nullable": true
          },
          "salesContact": {
            "$ref": "#/components/schemas/ContactPerson"
          },
          "accountReceivableContact": {
            "$ref": "#/components/schemas/ContactPerson"
          }
        },
        "additionalProperties": false
      },
      "SupplierResponse": {
        "type": "object",
        "properties": {
          "id": {
            "type": "string",
            "format": "uuid"
          },
          "name": {
            "type": "string",
            "nullable": true
          },
          "phoneNumber": {
            "type": "string",
            "nullable": true
          },
          "country": {
            "type": "string",
            "description": "Country ISO 3166-1 alpha-2 code",
            "nullable": true
          },
          "city": {
            "type": "string",
            "nullable": true
          },
          "isActive": {
            "type": "boolean"
          },
          "website": {
            "type": "string",
            "nullable": true
          },
          "emailAddress": {
            "type": "string",
            "nullable": true
          },
          "faxNumber": {
            "type": "string",
            "nullable": true
          },
          "address1": {
            "type": "string",
            "nullable": true
          },
          "address2": {
            "type": "string",
            "nullable": true
          },
          "postalCode": {
            "type": "string",
            "nullable": true
          },
          "salesContact": {
            "$ref": "#/components/schemas/ContactPerson"
          },
          "accountReceivableContact": {
            "$ref": "#/components/schemas/ContactPerson"
          }
        },
        "additionalProperties": false
      },
      "SupplierResponsePagingResponse": {
        "type": "object",
        "properties": {
          "data": {
            "type": "array",
            "items": {
              "oneOf": [
                {
                  "$ref": "#/components/schemas/AuSupplierResponse"
                },
                {
                  "$ref": "#/components/schemas/UsSupplierResponse"
                },
                {
                  "$ref": "#/components/schemas/UkSupplierResponse"
                },
                {
                  "$ref": "#/components/schemas/NzSupplierResponse"
                },
                {
                  "$ref": "#/components/schemas/RoiSupplierResponse"
                }
              ]
            },
            "nullable": true
          },
          "paginagion": {
            "$ref": "#/components/schemas/PagingOptions"
          }
        },
        "additionalProperties": false
      },
      "TaxRequest": {
        "type": "object",
        "properties": {
          "taxCode": {
            "type": "string",
            "nullable": true
          },
          "percentage": {
            "type": "number",
            "format": "double",
            "nullable": true
          },
          "description": {
            "type": "string",
            "nullable": true
          },
          "isActive": {
            "type": "boolean"
          }
        },
        "additionalProperties": false
      },
      "TaxResponse": {
        "type": "object",
        "properties": {
          "id": {
            "type": "string",
            "format": "uuid"
          },
          "taxCode": {
            "type": "string",
            "nullable": true
          },
          "percentage": {
            "type": "number",
            "format": "double",
            "nullable": true
          },
          "description": {
            "type": "string",
            "nullable": true
          },
          "isActive": {
            "type": "boolean"
          }
        },
        "additionalProperties": false
      },
      "TaxResponsePagedResponse": {
        "type": "object",
        "properties": {
          "result": {
            "type": "array",
            "items": {
              "$ref": "#/components/schemas/TaxResponse"
            }
          },
          "pageNumber": {
            "type": "integer",
            "format": "int32"
          },
          "pageSize": {
            "type": "integer",
            "format": "int32"
          }
        },
        "additionalProperties": false
      },
      "Timeslot": {
        "required": [
          "endDateTime",
          "startDateTime"
        ],
        "type": "object",
        "properties": {
          "startDateTime": {
            "type": "string",
            "format": "date-time"
          },
          "endDateTime": {
            "type": "string",
            "format": "date-time"
          },
          "resourceIds": {
            "type": "array",
            "items": {
              "type": "string",
              "format": "uuid"
            }
          }
        },
        "additionalProperties": false
      },
      "Timeslots": {
        "required": [
          "epos",
          "timeslots"
        ],
        "type": "object",
        "properties": {
          "epos": {
            "minLength": 1,
            "type": "string"
          },
          "timeslots": {
            "type": "array",
            "items": {
              "$ref": "#/components/schemas/Timeslot"
            }
          }
        },
        "additionalProperties": false
      },
      "TimeslotsAvailabilityResponse": {
        "type": "object",
        "properties": {
          "data": {
            "$ref": "#/components/schemas/Timeslots"
          },
          "success": {
            "type": "boolean"
          },
          "error": {
            "$ref": "#/components/schemas/ErrorResponse"
          }
        },
        "additionalProperties": false
      },
      "TrialProductResponse": {
        "type": "object",
        "properties": {
          "id": {
            "type": "string",
            "format": "uuid"
          },
          "saleProductId": {
            "type": "string",
            "format": "uuid"
          },
          "name": {
            "type": "string"
          }
        },
        "additionalProperties": false
      },
      "TrialResponse": {
        "type": "object",
        "allOf": [
          {
            "$ref": "#/components/schemas/BaseTrialResponse"
          }
        ],
        "properties": {
          "id": {
            "type": "string",
            "format": "uuid"
          },
          "number": {
            "type": "string"
          },
          "status": {
            "type": "string"
          },
          "startDate": {
            "type": "string",
            "format": "date-time"
          },
          "endDate": {
            "type": "string",
            "format": "date-time"
          },
          "initialEndDate": {
            "type": "string",
            "format": "date-time"
          },
          "endDateLastChangedBy": {
            "type": "string"
          },
          "endDateLastChangedDate": {
            "type": "string",
            "format": "date-time",
            "nullable": true
          },
          "cancellationReasonName": {
            "type": "string"
          },
          "products": {
            "type": "array",
            "items": {
              "$ref": "#/components/schemas/TrialProductResponse"
            }
          }
        },
        "additionalProperties": false
      },
      "UKProductRequest": {
        "type": "object",
        "allOf": [
          {
            "$ref": "#/components/schemas/ProductRequest"
          }
        ],
        "properties": {
          "firstVAT": {
            "type": "number",
            "format": "double"
          },
          "secondVAT": {
            "type": "number",
            "format": "double"
          }
        },
        "additionalProperties": false
      },
      "USProductRequest": {
        "type": "object",
        "allOf": [
          {
            "$ref": "#/components/schemas/ProductRequest"
          }
        ],
        "additionalProperties": false
      },
      "UkCreditNotePayerResponse": {
        "type": "object",
        "properties": {
          "id": {
            "type": "string",
            "format": "uuid"
          },
          "name": {
            "type": "string"
          }
        },
        "additionalProperties": false
      },
      "UkCreditNoteResponse": {
        "type": "object",
        "allOf": [
          {
            "$ref": "#/components/schemas/BaseCreditNoteResponse"
          }
        ],
        "properties": {
          "id": {
            "type": "string",
            "format": "uuid"
          },
          "number": {
            "type": "string"
          },
          "reason": {
            "type": "string"
          },
          "amount": {
            "type": "number",
            "format": "double"
          },
          "createdOn": {
            "type": "string",
            "format": "date-time"
          },
          "payer": {
            "$ref": "#/components/schemas/UkCreditNotePayerResponse"
          }
        },
        "additionalProperties": false
      },
      "UkDeliveryNoteResponse": {
        "type": "object",
        "allOf": [
          {
            "$ref": "#/components/schemas/BaseDeliveryNoteResponse"
          }
        ],
        "properties": {
          "id": {
            "type": "string",
            "format": "uuid"
          },
          "number": {
            "type": "string"
          },
          "actualDeliveryDate": {
            "type": "string",
            "format": "date-time"
          },
          "startWarrentyDate": {
            "type": "string",
            "format": "date-time"
          },
          "products": {
            "type": "array",
            "items": {
              "$ref": "#/components/schemas/DeliveryNoteProductResponse"
            }
          }
        },
        "additionalProperties": false
      },
      "UkManufacturerRequest": {
        "type": "object",
        "allOf": [
          {
            "$ref": "#/components/schemas/ManufacturerRequest"
          }
        ],
        "properties": {
          "state": {
            "type": "string",
            "nullable": true
          }
        },
        "additionalProperties": false
      },
      "UkManufacturerResponse": {
        "type": "object",
        "allOf": [
          {
            "$ref": "#/components/schemas/ManufacturerResponse"
          }
        ],
        "properties": {
          "state": {
            "type": "string",
            "nullable": true
          }
        },
        "additionalProperties": false
      },
      "UkNhsSaleResponse": {
        "type": "object",
        "allOf": [
          {
            "$ref": "#/components/schemas/BaseSaleResponse"
          }
        ],
        "properties": {
          "id": {
            "type": "string",
            "format": "uuid"
          },
          "saleType": {
            "$ref": "#/components/schemas/SaleType"
          },
          "totalSaleAmount": {
            "type": "number",
            "format": "double"
          },
          "totalTaxAmount": {
            "type": "number",
            "format": "double"
          },
          "outstandingAmount": {
            "type": "number",
            "format": "double"
          },
          "priceBookId": {
            "type": "string",
            "format": "uuid",
            "nullable": true
          },
          "pathwayTemplateId": {
            "type": "string",
            "format": "uuid",
            "nullable": true
          },
          "pathwayStepId": {
            "type": "string",
            "format": "uuid",
            "nullable": true
          },
          "createdBy": {
            "type": "string"
          },
          "appointment": {
            "$ref": "#/components/schemas/SaleAppointmentResponse"
          },
          "appointmentReason": {
            "oneOf": [
              {
                "$ref": "#/components/schemas/BaseCodeResponse"
              },
              {
                "$ref": "#/components/schemas/AppointmentOutcomeResponse"
              },
              {
                "$ref": "#/components/schemas/AppointmentStatusesResponse"
              },
              {
                "$ref": "#/components/schemas/BaseReasonResponse"
              }
            ]
          },
          "specialist": {
            "oneOf": [
              {
                "$ref": "#/components/schemas/DictionaryResponse"
              },
              {
                "$ref": "#/components/schemas/CountryResponse"
              }
            ]
          },
          "referral": {
            "$ref": "#/components/schemas/ReferralResponse"
          },
          "nhsContract": {
            "$ref": "#/components/schemas/NhsContractResponse"
          },
          "clinicalCommissioningGroup": {
            "oneOf": [
              {
                "$ref": "#/components/schemas/DictionaryResponse"
              },
              {
                "$ref": "#/components/schemas/CountryResponse"
              }
            ]
          }
        },
        "additionalProperties": false
      },
      "UkSaleProductDetailsResponse": {
        "type": "object",
        "allOf": [
          {
            "$ref": "#/components/schemas/BaseSaleProductDetailsResponse"
          }
        ],
        "properties": {
          "id": {
            "type": "string",
            "format": "uuid"
          },
          "isOrdered": {
            "type": "boolean"
          },
          "isLocked": {
            "type": "boolean"
          },
          "productId": {
            "type": "string",
            "format": "uuid"
          },
          "stockProductId": {
            "type": "string",
            "format": "uuid"
          },
          "stockProductItemId": {
            "type": "string",
            "format": "uuid",
            "nullable": true
          },
          "serialNumber": {
            "type": "string"
          },
          "side": {
            "$ref": "#/components/schemas/ProductSideType"
          },
          "batteryTypeId": {
            "type": "string",
            "format": "uuid",
            "nullable": true
          },
          "quantity": {
            "type": "integer",
            "format": "int32"
          },
          "unitPrice": {
            "type": "number",
            "format": "double"
          },
          "taxAmount": {
            "type": "number",
            "format": "double"
          },
          "saleType": {
            "$ref": "#/components/schemas/SaleType"
          },
          "color": {
            "oneOf": [
              {
                "$ref": "#/components/schemas/DictionaryResponse"
              },
              {
                "$ref": "#/components/schemas/CountryResponse"
              }
            ]
          },
          "discounts": {
            "type": "array",
            "items": {
              "$ref": "#/components/schemas/ProductDiscountResponse"
            }
          }
        },
        "additionalProperties": false
      },
      "UkSaleProductResponse": {
        "type": "object",
        "allOf": [
          {
            "$ref": "#/components/schemas/BaseSaleProductResponse"
          }
        ],
        "properties": {
          "stockProductId": {
            "type": "string",
            "format": "uuid"
          },
          "status": {
            "type": "string"
          },
          "color": {
            "type": "string"
          },
          "side": {
            "$ref": "#/components/schemas/ProductSideType"
          },
          "serialNumber": {
            "type": "string"
          },
          "name": {
            "type": "string"
          },
          "categoryName": {
            "type": "string"
          },
          "quantity": {
            "type": "integer",
            "format": "int32"
          },
          "unitPrice": {
            "type": "number",
            "format": "double"
          },
          "totalAmount": {
            "type": "number",
            "format": "double"
          },
          "taxAmount": {
            "type": "number",
            "format": "double"
          },
          "taxableAmount": {
            "type": "number",
            "format": "double"
          },
          "exemptAmount": {
            "type": "number",
            "format": "double"
          },
          "isSerialized": {
            "type": "boolean"
          },
          "batteryType": {
            "oneOf": [
              {
                "$ref": "#/components/schemas/DictionaryResponse"
              },
              {
                "$ref": "#/components/schemas/CountryResponse"
              }
            ]
          },
          "discounts": {
            "type": "array",
            "items": {
              "$ref": "#/components/schemas/ProductDiscountResponse"
            }
          }
        },
        "additionalProperties": false
      },
      "UkSaleResponse": {
        "type": "object",
        "allOf": [
          {
            "$ref": "#/components/schemas/BaseSaleResponse"
          }
        ],
        "properties": {
          "id": {
            "type": "string",
            "format": "uuid"
          },
          "saleType": {
            "$ref": "#/components/schemas/SaleType"
          },
          "totalSaleAmount": {
            "type": "number",
            "format": "double"
          },
          "totalTaxAmount": {
            "type": "number",
            "format": "double"
          },
          "outstandingAmount": {
            "type": "number",
            "format": "double"
          },
          "priceBookId": {
            "type": "string",
            "format": "uuid",
            "nullable": true
          },
          "pathwayTemplateId": {
            "type": "string",
            "format": "uuid",
            "nullable": true
          },
          "pathwayStepId": {
            "type": "string",
            "format": "uuid",
            "nullable": true
          },
          "lnDOrderId": {
            "type": "string",
            "format": "uuid",
            "nullable": true
          },
          "createdBy": {
            "type": "string"
          },
          "appointment": {
            "$ref": "#/components/schemas/SaleAppointmentResponse"
          },
          "appointmentReason": {
            "oneOf": [
              {
                "$ref": "#/components/schemas/BaseCodeResponse"
              },
              {
                "$ref": "#/components/schemas/AppointmentOutcomeResponse"
              },
              {
                "$ref": "#/components/schemas/AppointmentStatusesResponse"
              },
              {
                "$ref": "#/components/schemas/BaseReasonResponse"
              }
            ]
          },
          "specialist": {
            "oneOf": [
              {
                "$ref": "#/components/schemas/DictionaryResponse"
              },
              {
                "$ref": "#/components/schemas/CountryResponse"
              }
            ]
          }
        },
        "additionalProperties": false
      },
      "UkSupplierRequest": {
        "type": "object",
        "allOf": [
          {
            "$ref": "#/components/schemas/SupplierRequest"
          }
        ],
        "properties": {
          "state": {
            "type": "string",
            "nullable": true
          }
        },
        "additionalProperties": false
      },
      "UkSupplierResponse": {
        "type": "object",
        "allOf": [
          {
            "$ref": "#/components/schemas/SupplierResponse"
          }
        ],
        "properties": {
          "state": {
            "type": "string",
            "nullable": true
          }
        },
        "additionalProperties": false
      },
      "UpdateAUPatientRequest": {
        "type": "object",
        "allOf": [
          {
            "$ref": "#/components/schemas/UpdatePatientRequest"
          }
        ],
        "properties": {
          "dva": {
            "type": "boolean"
          },
          "dvaNumber": {
            "type": "string",
            "nullable": true
          },
          "dvaCardType": {
            "$ref": "#/components/schemas/PatientDVACardType"
          },
          "dvaExpireDate": {
            "type": "string",
            "format": "date-time",
            "nullable": true
          }
        },
        "additionalProperties": false
      },
      "UpdateNZPatientRequest": {
        "type": "object",
        "allOf": [
          {
            "$ref": "#/components/schemas/UpdatePatientRequest"
          }
        ],
        "properties": {
          "nhiNumber": {
            "type": "string"
          }
        },
        "additionalProperties": false
      },
      "UpdatePatientRequest": {
        "type": "object",
        "properties": {
          "patientLead": {
            "type": "boolean"
          },
          "firstName": {
            "type": "string",
            "nullable": true
          },
          "lastName": {
            "type": "string",
            "nullable": true
          },
          "middleName": {
            "type": "string",
            "nullable": true
          },
          "shortName": {
            "type": "string",
            "nullable": true
          },
          "dateOfBirth": {
            "type": "string",
            "format": "date-time",
            "nullable": true
          },
          "city": {
            "type": "string",
            "nullable": true
          },
          "stateId": {
            "type": "string",
            "description": "Unique identifier of State (GUID). Can be retrieved from /api/v2/states endpoint.",
            "format": "uuid",
            "nullable": true
          },
          "postcode": {
            "type": "string",
            "nullable": true
          },
          "mobilePhone": {
            "type": "string",
            "nullable": true
          },
          "homePhone": {
            "type": "string",
            "nullable": true
          },
          "workPhone": {
            "type": "string",
            "nullable": true
          },
          "preferredPhone": {
            "$ref": "#/components/schemas/PreferredPhone"
          },
          "faxNumber": {
            "type": "string",
            "nullable": true
          },
          "emailAddress": {
            "type": "string",
            "nullable": true
          },
          "hadAppointmentOnly": {
            "type": "boolean",
            "nullable": true
          },
          "gpId": {
            "type": "string",
            "nullable": true
          },
          "gpPracticeId": {
            "type": "string",
            "nullable": true
          },
          "note": {
            "type": "string",
            "nullable": true
          },
          "employer": {
            "type": "string",
            "nullable": true
          },
          "occupation": {
            "type": "string",
            "nullable": true
          },
          "homeFlatNumber": {
            "type": "string",
            "nullable": true
          },
          "street": {
            "type": "string",
            "nullable": true
          },
          "county": {
            "type": "string",
            "nullable": true
          },
          "address4": {
            "type": "string",
            "nullable": true
          },
          "address5": {
            "type": "string",
            "nullable": true
          },
          "suburb": {
            "type": "string",
            "nullable": true
          },
          "leadSourceId": {
            "type": "string",
            "format": "uuid",
            "nullable": true
          },
          "campaignId": {
            "type": "string",
            "format": "uuid",
            "nullable": true
          },
          "titleId": {
            "type": "string",
            "format": "uuid",
            "nullable": true
          },
          "genderId": {
            "type": "string",
            "format": "uuid",
            "nullable": true
          },
          "preferredLanguageId": {
            "type": "string",
            "format": "uuid",
            "nullable": true
          },
          "employmentStatusId": {
            "type": "string",
            "format": "uuid",
            "nullable": true
          },
          "locationId": {
            "type": "string",
            "format": "uuid",
            "nullable": true
          },
          "countryId": {
            "type": "string",
            "format": "uuid",
            "nullable": true,
            "deprecated": true
          },
          "country": {
            "type": "string",
            "description": "Country ISO 3166-1 alpha-2 code",
            "nullable": true
          },
          "specialistId": {
            "type": "string",
            "format": "uuid",
            "nullable": true
          },
          "patientStatusId": {
            "type": "string",
            "format": "uuid",
            "nullable": true
          },
          "contactPerson": {
            "$ref": "#/components/schemas/ContactPersonV2Request"
          },
          "initialOwner": {
            "type": "string",
            "nullable": true
          },
          "initialOwnerLocation": {
            "type": "string",
            "nullable": true
          }
        },
        "additionalProperties": false
      },
      "UpdateROIPatientRequest": {
        "type": "object",
        "allOf": [
          {
            "$ref": "#/components/schemas/UpdatePatientRequest"
          }
        ],
        "additionalProperties": false
      },
      "UpdateUKPatientRequest": {
        "type": "object",
        "allOf": [
          {
            "$ref": "#/components/schemas/UpdatePatientRequest"
          }
        ],
        "additionalProperties": false
      },
      "UpdateUSPatientRequest": {
        "type": "object",
        "allOf": [
          {
            "$ref": "#/components/schemas/UpdatePatientRequest"
          }
        ],
        "additionalProperties": false
      },
      "UsCreditNoteProductResponse": {
        "type": "object",
        "properties": {
          "id": {
            "type": "string",
            "format": "uuid"
          },
          "name": {
            "type": "string"
          },
          "saleProductId": {
            "type": "string",
            "format": "uuid"
          }
        },
        "additionalProperties": false
      },
      "UsCreditNoteResponse": {
        "type": "object",
        "allOf": [
          {
            "$ref": "#/components/schemas/BaseCreditNoteResponse"
          }
        ],
        "properties": {
          "id": {
            "type": "string",
            "format": "uuid"
          },
          "number": {
            "type": "string"
          },
          "reason": {
            "type": "string"
          },
          "createdOn": {
            "type": "string",
            "format": "date-time"
          },
          "products": {
            "type": "array",
            "items": {
              "$ref": "#/components/schemas/UsCreditNoteProductResponse"
            }
          }
        },
        "additionalProperties": false
      },
      "UsDeliveryNoteResponse": {
        "type": "object",
        "allOf": [
          {
            "$ref": "#/components/schemas/BaseDeliveryNoteResponse"
          }
        ],
        "properties": {
          "id": {
            "type": "string",
            "format": "uuid"
          },
          "number": {
            "type": "string"
          },
          "actualDeliveryDate": {
            "type": "string",
            "format": "date-time"
          },
          "startWarrentyDate": {
            "type": "string",
            "format": "date-time"
          },
          "products": {
            "type": "array",
            "items": {
              "$ref": "#/components/schemas/DeliveryNoteProductResponse"
            }
          }
        },
        "additionalProperties": false
      },
      "UsManufacturerRequest": {
        "type": "object",
        "allOf": [
          {
            "$ref": "#/components/schemas/ManufacturerRequest"
          }
        ],
        "properties": {
          "stateId": {
            "type": "string",
            "format": "uuid",
            "nullable": true
          }
        },
        "additionalProperties": false
      },
      "UsManufacturerResponse": {
        "type": "object",
        "allOf": [
          {
            "$ref": "#/components/schemas/ManufacturerResponse"
          }
        ],
        "properties": {
          "stateId": {
            "type": "string",
            "format": "uuid",
            "nullable": true
          }
        },
        "additionalProperties": false
      },
      "UsProductPayerResponse": {
        "type": "object",
        "properties": {
          "payerName": {
            "type": "string"
          },
          "payerAmount": {
            "type": "number",
            "format": "double"
          }
        },
        "additionalProperties": false
      },
      "UsSaleProductDetailsResponse": {
        "type": "object",
        "allOf": [
          {
            "$ref": "#/components/schemas/BaseSaleProductDetailsResponse"
          }
        ],
        "properties": {
          "id": {
            "type": "string",
            "format": "uuid"
          },
          "isOrdered": {
            "type": "boolean"
          },
          "isLocked": {
            "type": "boolean"
          },
          "productId": {
            "type": "string",
            "format": "uuid"
          },
          "stockProductId": {
            "type": "string",
            "format": "uuid"
          },
          "stockProductItemId": {
            "type": "string",
            "format": "uuid",
            "nullable": true
          },
          "serialNumber": {
            "type": "string"
          },
          "cptCode": {
            "type": "string"
          },
          "color": {
            "type": "string"
          },
          "side": {
            "$ref": "#/components/schemas/ProductSideType"
          },
          "batteryTypeId": {
            "type": "string",
            "format": "uuid",
            "nullable": true
          },
          "quantity": {
            "type": "integer",
            "format": "int32"
          },
          "unitPrice": {
            "type": "number",
            "format": "double"
          },
          "taxAmount": {
            "type": "number",
            "format": "double"
          },
          "discounts": {
            "type": "array",
            "items": {
              "$ref": "#/components/schemas/ProductDiscountResponse"
            }
          }
        },
        "additionalProperties": false
      },
      "UsSaleProductResponse": {
        "type": "object",
        "allOf": [
          {
            "$ref": "#/components/schemas/BaseSaleProductResponse"
          }
        ],
        "properties": {
          "stockProductId": {
            "type": "string",
            "format": "uuid"
          },
          "status": {
            "type": "string"
          },
          "color": {
            "type": "string"
          },
          "side": {
            "$ref": "#/components/schemas/ProductSideType"
          },
          "serialNumber": {
            "type": "string"
          },
          "name": {
            "type": "string"
          },
          "categoryName": {
            "type": "string"
          },
          "quantity": {
            "type": "integer",
            "format": "int32"
          },
          "unitPrice": {
            "type": "number",
            "format": "double"
          },
          "totalAmount": {
            "type": "number",
            "format": "double"
          },
          "taxAmount": {
            "type": "number",
            "format": "double"
          },
          "taxableAmount": {
            "type": "number",
            "format": "double"
          },
          "exemptAmount": {
            "type": "number",
            "format": "double"
          },
          "isSerialized": {
            "type": "boolean"
          },
          "batteryType": {
            "oneOf": [
              {
                "$ref": "#/components/schemas/DictionaryResponse"
              },
              {
                "$ref": "#/components/schemas/CountryResponse"
              }
            ]
          },
          "discounts": {
            "type": "array",
            "items": {
              "$ref": "#/components/schemas/ProductDiscountResponse"
            }
          },
          "payers": {
            "type": "array",
            "items": {
              "$ref": "#/components/schemas/UsProductPayerResponse"
            }
          }
        },
        "additionalProperties": false
      },
      "UsSaleResponse": {
        "type": "object",
        "allOf": [
          {
            "$ref": "#/components/schemas/BaseSaleResponse"
          }
        ],
        "properties": {
          "id": {
            "type": "string",
            "format": "uuid"
          },
          "saleType": {
            "$ref": "#/components/schemas/SaleType"
          },
          "totalSaleAmount": {
            "type": "number",
            "format": "double"
          },
          "totalTaxAmount": {
            "type": "number",
            "format": "double"
          },
          "appointmentId": {
            "type": "string",
            "format": "uuid",
            "nullable": true
          },
          "priceBookId": {
            "type": "string",
            "format": "uuid",
            "nullable": true
          },
          "pathwayTemplateId": {
            "type": "string",
            "format": "uuid",
            "nullable": true
          },
          "pathwayStepId": {
            "type": "string",
            "format": "uuid",
            "nullable": true
          },
          "lnDOrderId": {
            "type": "string",
            "format": "uuid",
            "nullable": true
          },
          "specialist": {
            "oneOf": [
              {
                "$ref": "#/components/schemas/DictionaryResponse"
              },
              {
                "$ref": "#/components/schemas/CountryResponse"
              }
            ]
          }
        },
        "additionalProperties": false
      },
      "UsSupplierRequest": {
        "type": "object",
        "allOf": [
          {
            "$ref": "#/components/schemas/SupplierRequest"
          }
        ],
        "properties": {
          "stateId": {
            "type": "string",
            "format": "uuid",
            "nullable": true
          }
        },
        "additionalProperties": false
      },
      "UsSupplierResponse": {
        "type": "object",
        "allOf": [
          {
            "$ref": "#/components/schemas/SupplierResponse"
          }
        ],
        "properties": {
          "stateId": {
            "type": "string",
            "format": "uuid",
            "nullable": true
          }
        },
        "additionalProperties": false
      },
      "UserDefinedFieldMetadataResponse": {
        "type": "object",
        "properties": {
          "id": {
            "type": "string"
          },
          "name": {
            "type": "string"
          },
          "type": {
            "$ref": "#/components/schemas/UserDefinedFieldType"
          },
          "isRequired": {
            "type": "boolean"
          },
          "isMultipleValue": {
            "type": "boolean"
          },
          "options": {
            "type": "array",
            "items": {
              "$ref": "#/components/schemas/UserDefinedFieldOptionResponse"
            },
            "nullable": true
          }
        },
        "additionalProperties": false
      },
      "UserDefinedFieldMetadataResponseIEnumerableListResponse": {
        "type": "object",
        "properties": {
          "data": {
            "type": "array",
            "items": {
              "$ref": "#/components/schemas/UserDefinedFieldMetadataResponse"
            },
            "nullable": true
          }
        },
        "additionalProperties": false
      },
      "UserDefinedFieldOptionResponse": {
        "type": "object",
        "properties": {
          "name": {
            "type": "string"
          },
          "value": {
            "type": "string"
          }
        },
        "additionalProperties": false
      },
      "UserDefinedFieldType": {
        "enum": [
          "Dropdown",
          "Tags",
          "Checkboxes",
          "RadioButtons",
          "TextInput",
          "EmailInput",
          "NumberInput",
          "PhoneNumber",
          "TextArea"
        ],
        "type": "string"
      }
    },
    "securitySchemes": {
      "ApiKey": {
        "type": "apiKey",
        "name": "ApiKey",
        "in": "header"
      },
      "EdiScheme": {
        "type": "apiKey",
        "name": "X-Edi-ApiKey",
        "in": "header"
      }
    }
  }
}