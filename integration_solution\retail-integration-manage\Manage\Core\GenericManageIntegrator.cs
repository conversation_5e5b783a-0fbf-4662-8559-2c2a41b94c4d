﻿using Microsoft.Extensions.Options;
using System.Text.Json;
using WSA.Retail.Integration.Core;
using WSA.Retail.Integration.Manage.API;
using WSA.Retail.Integration.Manage.Configuration;
using WSA.Retail.Integration.Models.Configuration;

namespace WSA.Retail.Integration.Manage.Core;

public abstract class GenericManageIntegrator<
    TDomainService,
    TEntity,
    TRequestEntity,
    TResponseEntity,
    TPagingResponse,
    TEntitySubscriberService>(
        IOptions<AppSettings> appSettings,
        TDomainService domainService,
        IManageRequestAdapter<TRequestEntity, TEntity> requestAdapter,
        IManageResponseAdapter<TResponseEntity, TEntity> responseAdapter,
        IEntitySubscriberService entitySubscriberService,
        ManageAPI manageAPI,
        EntityType entityType) : IGenericManageIntegrator<TEntity, TResponseEntity>

    where TDomainService : IMasterDataServiceBase<TEntity>
    where TEntity : class, IMasterData
    where TRequestEntity : class
    where TResponseEntity : class
    where TPagingResponse : class
    where TEntitySubscriberService : class
{
    protected readonly AppSettings _appSettings = appSettings.Value;
    private readonly TDomainService _domainService = domainService;
    private readonly IManageRequestAdapter<TRequestEntity, TEntity> _requestAdapter = requestAdapter;
    private readonly IManageResponseAdapter<TResponseEntity, TEntity> _responseAdapter = responseAdapter;
    protected readonly IEntitySubscriberService _entitySubscriberService = entitySubscriberService;
    protected readonly ManageAPI _manageApi = manageAPI;
    protected readonly EntityType entityType = entityType;


    public async Task<TResponseEntity?> GetFromManageAsync(Guid id)
    {
        LogMethodStart(callingMethod: nameof(GetFromManageAsync));
        try
        {
            TResponseEntity? response = null;
            BeforeGetEntity(response, out bool BeforeGetEntityOk, out bool BeforeGetEntityIsHandled);
            if (BeforeGetEntityIsHandled) return response;
            if (!BeforeGetEntityOk) return null;

            response = await GetEntity(id);
            if (response != null)
            {
                return response;
            }
        }
        catch (Exception ex)
        {
            LogCustomError(
                ex: ex,
                callingMethod: nameof(GetFromManageAsync));
        }
        return null;
    }

    public async Task<List<TResponseEntity>> GetListFromManageAsync()
    {
        LogMethodStart(callingMethod: nameof(GetListFromManageAsync));
        try
        {
            TPagingResponse? response = null;
            List<TResponseEntity> responseList = [];

            BeforeGetEntityList(responseList, out bool BeforeGetEntityOk, out bool BeforeGetEntityIsHandled);
            if (BeforeGetEntityIsHandled) return responseList;
            if (!BeforeGetEntityOk) return [];

            response = await GetEntityList();
            if (response != null)
            {
                responseList = [];
                foreach (var entityResponse in GetEntitiesFromEntityList(response))
                {
                    if (entityResponse != null)
                    {
                        responseList.Add(entityResponse);
                    }
                }
                return responseList;
            }
        }
        catch (Exception ex)
        {
            LogCustomError(
                ex: ex,
                callingMethod: nameof(GetListFromManageAsync));
        }
        return [];
    }

    public async Task<TEntity?> UpdateManageAsync(TEntity domainEntity)
    {
        LogMethodStart(
            callingMethod: nameof(UpdateManageAsync));

        TEntity? existingDomainEntity = default;
        if (domainEntity.Id != Guid.Empty)
        {
            existingDomainEntity = await _domainService.GetAsync(
                _appSettings.ExternalSystemCode,
                id: domainEntity.Id);
        }

        ArgumentNullException.ThrowIfNull(existingDomainEntity);

        TResponseEntity? existingManageEntity = null;
        if (!string.IsNullOrWhiteSpace(existingDomainEntity.ExternalCode) &&
            Guid.TryParse(existingDomainEntity.ExternalCode, out var manageId))
        {
            existingManageEntity = await GetFromManageAsync(manageId);
        }

        existingManageEntity = await AfterGetManageEntity(existingManageEntity, existingDomainEntity);

        if (existingDomainEntity != null && existingManageEntity == null) // <-- Add to Manage
        {
            return await PostToManageAsync(existingDomainEntity);
        }
        else
        {
            if (existingDomainEntity != null && existingManageEntity != null) // <-- Update Manage
            {
                return await PutToManageAsync(existingDomainEntity, existingManageEntity);
            }
        }
        return default;
    }

    public async Task<TEntity?> PostToManageAsync(TEntity domainEntity)
    {
        LogMethodStart(
            callingMethod: nameof(PostToManageAsync));

        var request = _requestAdapter.ToManageRequest(domainEntity);
        await AfterConvertDomainEntityToPostRequest(request, domainEntity);

        CreatedResponse? response = null;
        try
        {
            var json = JsonSerializer.Serialize(request, Utilities.Common.GetJsonOptions());
            response = await PostEntity(request);
            
            LogCustomInformation(
                message: $"Inserted new {typeof(TEntity).Name} with Manage Id {response?.Id} in Manage",
                callingMethod: nameof(PostToManageAsync));
        }
        catch(Exception ex)
        {
            await OnAfterPostToManageFailed(ex, domainEntity, request);
        }

        if (response != null)
        {
            try
            {
                domainEntity.ExternalSystemCode = _appSettings.ExternalSystemCode;
                domainEntity.ExternalCode = response.Id.ToString();
                
                if (domainEntity.Id != Guid.Empty)
                {
                    await _domainService.CoupleAsync(domainEntity);
                }

                return domainEntity;
            }
            catch (Exception ex)
            {
                LogCustomError(
                    ex: ex,
                    callingMethod: nameof(PostToManageAsync));

                return default;
            }
        }
        return default;
    }

    public async Task<TEntity?> PutToManageAsync(TEntity existingDomainEntity, TResponseEntity existingManageEntity)
    {
        LogMethodStart(
            callingMethod: nameof(PutToManageAsync));

        if (existingDomainEntity != null && existingManageEntity != null)
        {
            var oldEntity = _responseAdapter.ToDomainModel(existingManageEntity, _appSettings.ExternalSystemCode);
            if (existingDomainEntity.HasChanges(oldEntity))
            {
                var request = _requestAdapter.ToManageRequest(existingDomainEntity);
                await AfterConvertDomainEntityToPutRequest(request, existingManageEntity, existingDomainEntity);

                if (!string.IsNullOrEmpty(existingDomainEntity.ExternalCode) &&
                    Guid.TryParse(existingDomainEntity.ExternalCode, out var manageId))
                {
                    try
                    {
                        await PutEntity(manageId, request);

                        LogCustomInformation(
                            message: $"Updated {typeof(TEntity).Name} with Manage Id {manageId} in Manage",
                            callingMethod: nameof(PutToManageAsync));

                        var response = await GetFromManageAsync(manageId);
                        if (response == null) return default;
                        return _responseAdapter.ToDomainModel(response, _appSettings.ExternalSystemCode);
                    }
                    catch (Exception ex)
                    {
                        LogCustomError(
                            ex: ex,
                            callingMethod: nameof(PutToManageAsync));

                        return null;
                    }
                }
            }
            else
            {
                return existingDomainEntity;
            }
        }
        return null;
    }


    protected abstract Task<TResponseEntity?> GetEntity(Guid id);

    protected abstract Task<TPagingResponse?> GetEntityList();

    protected abstract Task<CreatedResponse?> PostEntity(TRequestEntity request);

    protected abstract Task PutEntity(Guid id, TRequestEntity request);

    protected abstract ICollection<TResponseEntity> GetEntitiesFromEntityList(TPagingResponse responseList);

    protected virtual void BeforeGetEntity(TResponseEntity? response, out bool ok, out bool isHandled)
    {
        ok = true;
        isHandled = false;
        return;
    }

    protected virtual void BeforeGetEntityList(List<TResponseEntity>? responseList, out bool ok, out bool isHandled)
    {
        ok = true;
        isHandled = false;
        return;
    }

    protected virtual Task<TResponseEntity?> AfterGetManageEntity(TResponseEntity? existingManageEntity, TEntity entity)
    {
        return Task.FromResult(existingManageEntity);
    }

    protected virtual Task AfterConvertDomainEntityToPostRequest(
        TRequestEntity request,
        TEntity entity)
    {
        return Task.CompletedTask;
    }

    protected virtual Task AfterConvertDomainEntityToPutRequest(
        TRequestEntity request, 
        TResponseEntity existingManageEntity, 
        TEntity entity)
    {
        return Task.CompletedTask;
    }

    protected virtual Task OnAfterPostToManageFailed(Exception ex, TEntity entity, TRequestEntity request)
    {
        LogCustomError(
            ex: ex,
            callingMethod: nameof(OnAfterPostToManageFailed));
        return Task.CompletedTask;
    }

    protected virtual void LogMethodStart(string? callingMethod = null) { }
    protected virtual void LogCustomInformation(string message, string? callingMethod = null) { }
    protected virtual void LogCustomWarning(string message, string? callingMethod = null) { }
    protected virtual void LogCustomError(Exception ex, string message, string? callingMethod = null) { }
    protected virtual void LogCustomError(Exception ex, string? callingMethod = null) { }
}