namespace WSA.Integration.API.V1;


using Microsoft.Inventory.Item;
using Microsoft.Inventory.Location;
using Microsoft.Inventory.Ledger;
using Microsoft.Sales.Customer;
using Microsoft.Sales.Receivables;
using Microsoft.Sales.Document;
using Microsoft.Sales.History;
using Microsoft.Sales.Posting;
using Microsoft.Inventory.Tracking;
using System.Utilities;
using System.Reflection;
using System.Security.User;
using Microsoft.Finance.GeneralLedger.Setup;
using WSA.Integration;
using Microsoft.Integration.Entity;
using Microsoft.Finance.VAT.Setup;

codeunit 50123 "WSA Sales Invoice Handler" implements "WSA Integration Request"
{
    TableNo = "WSA Integration Request Log";

    trigger OnRun()
    begin
        Code(Rec);
    end;


    procedure HandleRequest(var Request: Record "WSA Integration Request Log")
    begin
        if not Codeunit.Run(Codeunit::"WSA Sales Invoice Handler", Request) then begin
            Common.SetErrorResponse(Request, '');
        end;
    end;


    local procedure Code(var Request: Record "WSA Integration Request Log")
    var
        json: JsonObject;

    begin
        case Request.Method of
            Request.Method::post:
                HandlePost(Request);
            Request.Method::get:
                HandleGet(Request);
        end;
    end;


    local procedure HandlePost(var Request: Record "WSA Integration Request Log")
    var
        salesInvoice: Record "Sales Header";

    begin
        if not TryHandlePost(Request, salesInvoice) then
            Common.SetErrorResponse(Request, '');
    end;


    local procedure HandleGet(var Request: Record "WSA Integration Request Log")
    var
        SalesInvoice: Record "Sales Invoice Header";

    begin
        if not TryHandleGet(Request, SalesInvoice) then
            Common.SetErrorResponse(Request, '');
    end;


    [TryFunction]
    local procedure TryHandlePost(
        var Request: Record "WSA Integration Request Log";
        var SalesHeader: Record "Sales Header")

    var
        salesInvoiceHeader: Record "Sales Invoice Header";
        custLedgerEntry: Record "Cust. Ledger Entry";
        apiCommon: Codeunit "WSA API Common";
        json: JsonObject;
        jvalue: JsonValue;
        documentNo: Code[20];

    begin
        json := Common.GetJsonFromBlob(Request);
        UpdateRequestLog(Request, json);

        jValue := Common.GetJsonValue(Json, '$.documentNumber');
        if not jValue.IsNull() then begin
            documentNo := jValue.AsCode();
            if salesInvoiceHeader.Get(documentNo) then begin
                Common.SetSkippedResponse(Request, 'Duplicate sales invoice number');
                exit;
            end;
        end;


        TempErrorMessage.ClearLog();
        ValidateExternalReferences(json);
        if TempErrorMessage.HasErrors(false) then begin
            TempErrorMessage.ThrowError();
        end;

        if not ValidatePostingDate(json) then begin
            Common.SetErrorResponse(Request, 'Invalid posting date');
            exit;
        end;

        if not GetSalesHeader(json, SalesHeader) then begin
            Common.SetErrorResponse(Request, 'Sales order not found');
            exit;
        end;

        OpenSalesOrder(SalesHeader);
        ValidateTaxAreaCode(SalesHeader);
        UpdateInvoiceNo(json, SalesHeader);
        UpdateDates(json, SalesHeader);
        SalesHeader.Modify();
        UpdateLines(json, SalesHeader, Request);
        ReleaseSalesOrder(SalesHeader);

        if PostInvoice(SalesHeader, salesInvoiceHeader) then begin
            if (salesInvoiceHeader."Cust. Ledger Entry No." > 0) and (custLedgerEntry.Get(salesInvoiceHeader."Cust. Ledger Entry No.")) then
                ApiCommon.ApplyCustLedgEntries(custLedgerEntry."Customer No.", custLedgerEntry."Document No.", custLedgerEntry."External Document No.");
            Common.SetCreatedResponse(Request, Common.SalesInvoiceToJson(salesInvoiceHeader))
        end else begin
            CleanupAfterFailure(SalesHeader."No.");
            Common.SetErrorResponse(Request, '');
        end;
    end;


    [TryFunction]
    local procedure TryHandleGet(
         var Request: Record "WSA Integration Request Log";
         var SalesInvoice: Record "Sales Invoice Header")

    var
        recRef: RecordRef;
        json: JsonObject;
        idValue: JsonValue;
        documentNoValue: JsonValue;

    begin
        Request.TestField("Request Content");
        json := Common.GetJsonFromBlob(Request);

        idValue := Common.GetJsonValue(json, '$.id');
        if not (idValue.IsNull) then begin
            if SalesInvoice.GetBySystemId(idValue.AsText()) then begin
                Request."Sales Invoice No." := SalesInvoice."No.";
                Request.Modify();
                Common.SetOkResponse(Request, Common.SalesInvoiceToJson(SalesInvoice));
                exit;
            end;
        end;

        documentNoValue := Common.GetJsonValue(json, '$.documentNumber');
        if not (documentNoValue.IsNull) then begin
            SalesInvoice.SetRange("No.", documentNoValue.AsCode());
            if SalesInvoice.FindFirst() then begin
                Request."Sales Invoice No." := SalesInvoice."No.";
                Request.Modify();
                Common.SetOkResponse(Request, Common.SalesInvoiceToJson(SalesInvoice));
                exit;
            end
        end;

        Common.SetNoContentResponse(Request);
    end;


    local procedure UpdateRequestLog(var Request: Record "WSA Integration Request Log"; json: JsonObject)
    var
        recRef: RecordRef;

    begin
        recRef.GetTable(Request);

        Common.ValidateFieldFromJson(Json, '$.documentNumber', recRef, Request.FieldNo("Sales Invoice No."), TempFieldSet);
        Common.ValidateFieldFromJson(Json, '$.documentNumber', recRef, Request.FieldNo("Document No."), TempFieldSet);
        Common.ValidateFieldFromJson(Json, '$.documentDate', recRef, Request.FieldNo("Document Date"), TempFieldSet);
        Common.ValidateFieldFromJson(Json, '$.clinic.code', recRef, Request.FieldNo("Clinic No."), TempFieldSet);
        Common.ValidateFieldFromJson(Json, '$.patient.code', recRef, Request.FieldNo("Patient No."), TempFieldSet);

        recRef.SetTable(Request);
        if Request.Modify() then
            Commit();
    end;


    local procedure GetSalesHeader(
        Json: JsonObject;
        var SalesHeader: Record "Sales Header") Ok: boolean

    var
        jValue: JsonValue;
        documentNo: Code[20];
        patientNo: Code[20];
        clinicCode: Code[10];
        HideValidation: Boolean;

    begin
        jValue := Common.GetJsonValue(Json, '$.salesOrder.documentNumber');
        if not jValue.IsNull() then
            exit(SalesHeader.Get(SalesHeader."Document Type"::Order, jValue.AsCode()));

        jValue := Common.GetJsonValue(Json, '$.documentNumber');
        if jValue.IsNull() then
            exit(false)
        else
            documentNo := jValue.AsCode();

        if SalesHeader.Get(SalesHeader."Document Type"::Invoice, documentNo) then
            exit(true);

        jValue := Common.GetJsonValue(Json, '$.patient.code');
        if jValue.IsNull() then
            exit(false)
        else
            patientNo := jValue.AsCode();

        jValue := Common.GetJsonValue(Json, '$.clinic.code');
        if jValue.IsNull() then
            exit(false)
        else
            clinicCode := jValue.AsCode();

        SalesHeader.Init();
        SalesHeader."Document Type" := SalesHeader."Document Type"::Invoice;
        SalesHeader."No." := Common.GetJsonValue(Json, '$.documentNumber').AsCode();

        HideValidation := SalesHeader.GetHideValidationDialog();
        SalesHeader.SetHideValidationDialog(true);
        SalesHeader.Validate("Sell-to Customer No.", patientNo);
        SalesHeader.Validate("Responsibility Center", clinicCode);
        SalesHeader."Posting Description" := SalesHeader."Sell-to Customer Name";
        SalesHeader.SetHideValidationDialog(HideValidation);

        exit(SalesHeader.Insert);
    end;


    local procedure OpenSalesOrder(
        var SalesHeader: Record "Sales Header") Ok: boolean

    begin
        if (SalesHeader.Status <> SalesHeader.Status::Open) then begin
            SalesHeader.Status := SalesHeader.Status::Open;
            exit(SalesHeader.Modify());
        end;
    end;


    local procedure ReleaseSalesOrder(
        var SalesHeader: Record "Sales Header") Ok: boolean

    begin
        if (SalesHeader.Status <> SalesHeader.Status::Released) then begin
            SalesHeader.Status := SalesHeader.Status::Released;
            exit(SalesHeader.Modify());
        end;
    end;


    local procedure UpdateInvoiceNo(
        Json: JsonObject;
        var SalesHeader: Record "Sales Header")

    var
        newInvoiceNo: Code[20];
        alternateNumber: JsonValue;

    begin
        newInvoiceNo := Common.GetJsonValue(Json, '$.documentNumber').AsCode();
        if newInvoiceNo <> '' then
            SalesHeader."Posting No." := newInvoiceNo;

        alternateNumber := Common.GetJsonValue(Json, '$.alternateNumber');
        if not alternateNumber.IsNull then
            SalesHeader."External Document No." := alternateNumber.AsCode();
    end;


    local procedure UpdateDates(
        Json: JsonObject;
        var SalesHeader: Record "Sales Header")

    var
        newDate: Date;

    begin
        newDate := Common.GetJsonValue(Json, '$.documentDate').AsDate();
        if newDate <> 0D then begin
            SalesHeader."Posting Date" := newDate;
            SalesHeader.Validate("Document Date", newDate);
            SalesHeader.Validate("VAT Reporting Date", newDate);
        end;
    end;


    local procedure UpdateLines(
        Json: JsonObject;
        var SalesHeader: Record "Sales Header";
        var Request: Record "WSA Integration Request Log")

    var
        SalesLine: Record "Sales Line";
        jToken: JsonToken;
        lineToken: JsonToken;

    begin
        SalesLine.SetRange("Document Type", SalesHeader."Document Type");
        SalesLine.SetRange("Document No.", SalesHeader."No.");
        SalesLine.ModifyAll("Qty. to Invoice", 0);

        Json.SelectToken('$.salesInvoiceLines', jToken);
        foreach lineToken in jToken.AsArray do begin
            UpdateLine(lineToken.AsObject(), SalesHeader, Request);
        end;
    end;


    local procedure UpdateLine(
        Json: JsonObject;
        var SalesHeader: Record "Sales Header";
        var Request: Record "WSA Integration Request Log")

    var
        salesLine: Record "Sales Line";
        vatSetup: Record "VAT Posting Setup";
        glSetup: Record "General Ledger Setup";
        serialNumber: Code[50];
        discountAmount: Decimal;
        amountExludingTax: Decimal;
        amountIncludingTax: Decimal;
        taxAmount: Decimal;
        taxRate: Decimal;
        taxDiff: Decimal;

    begin
        glSetup.Get();

        if not GetSalesLine(Json, SalesHeader, salesLine) then begin
            Common.SetErrorResponse(Request, 'Sales line could not be retrieved/inserted');
            exit;
        end;

        salesLine.Validate("Qty. to Invoice", Common.GetJsonValue(Json, '$.quantity').AsDecimal());
        if not Common.GetJsonValue(Json, '$.serialNumber').IsNull then begin
            serialNumber := Common.GetJsonValue(Json, '$.serialNumber').AsCode();
            if serialNumber <> '' then
                salesLine.Validate("WSA Serial No.", serialNumber);
        end;

        if (Common.GetJsonValue(Json, '$.unitPrice').AsDecimal() <> salesLine."Unit Price") then
            salesLine.Validate("Unit Price", Common.GetJsonValue(Json, '$.unitPrice').AsDecimal());

        discountAmount := Common.GetJsonValue(Json, '$.discountAmount').AsDecimal();
        if discountAmount <> 0 then begin
            salesLine.Validate("Line Discount Amount", discountAmount);
        end;

        if (Common.GetJsonValue(Json, '$.amountExclTax').AsDecimal() <> salesLine.Amount) then
            salesLine.Validate("Amount", Common.GetJsonValue(Json, '$.amountExclTax').AsDecimal());

        if (Common.GetJsonValue(Json, '$.amountInclTax').AsDecimal() <> salesLine."Amount Including VAT") then begin
            amountExludingTax := Common.GetJsonValue(Json, '$.amountExclTax').AsDecimal();
            amountIncludingTax := Common.GetJsonValue(Json, '$.amountInclTax').AsDecimal();
            taxAmount := amountIncludingTax - amountExludingTax;
            if (taxAmount <> 0) and (amountExludingTax <> 0) then
                taxRate := taxAmount / amountExludingTax * 100
            else if (taxAmount = 0) and (amountExludingTax = 0) then
                taxRate := 0
            else if (taxAmount <> 0) and (amountExludingTax = 0) then
                taxRate := 100;

            salesLine.Validate("WSA Tax from POS", true);
            salesLine.Validate("WSA POS Tax Amount", taxAmount);

            VatSetup.Get();
            if vatSetup."VAT Calculation Type" = vatSetup."VAT Calculation Type"::"Sales Tax" then begin
                common.ValidateTaxGroup(SalesLine, amountExludingTax, taxAmount, taxRate);
            end else begin
                Common.ValidateVATPostingGroup(SalesLine, taxRate);
                if (salesLine."Amount Including VAT" <> amountIncludingTax) then begin
                    taxDiff := amountIncludingTax - salesLine."Amount Including VAT";
                    salesLine."VAT Difference" := Round(taxDiff, glSetup."Amount Rounding Precision");
                    salesLine."VAT Difference (ACY)" := Round(taxDiff, glSetup."Amount Rounding Precision");
                    salesLine."Amount Including VAT" := amountIncludingTax;
                    salesLine."Amount Including VAT (ACY)" := amountIncludingTax;
                end;
            end;
        end;

        salesLine.Modify();
    end;


    local procedure GetSalesLine(
        Json: JsonObject;
        var SalesHeader: Record "Sales Header";
        var SalesLine: Record "Sales Line") Ok: Boolean

    var
        jValue: JsonValue;
        recRef: RecordRef;
        LastError: Text;

    begin
        jValue := Common.GetJsonValue(Json, '$.salesOrderLine.sequence');
        if not jValue.IsNull() then
            exit(SalesLine.Get(
                SalesHeader."Document Type",
                SalesHeader."No.",
                jValue.AsInteger()));

        if SalesLine.Get(SalesHeader."Document Type", SalesHeader."No.",
            Common.GetJsonValue(Json, '$.sequence').AsInteger()) then
            exit(true);

        SalesLine.Init();
        SalesLine."Document Type" := SalesHeader."Document Type";
        SalesLine."Document No." := SalesHeader."No.";
        SalesLine."Line No." := Common.GetJsonValue(Json, '$.sequence').AsInteger();
        SalesLine.Validate("Sell-to Customer No.", SalesHeader."Sell-to Customer No.");
        if SalesHeader."Tax Area Code" <> '' then
            SalesLine.Validate("Tax Area Code", SalesHeader."Tax Area Code");
        SalesLine.Insert();

        SalesLine.Validate(Type, SalesLine.Type::Item);
        recRef.GetTable(SalesLine);
        Common.ValidateFieldFromJson(Json, '$.product.code', recRef, SalesLine.FieldNo("No."), TempFieldSet);
        Common.ValidateFieldFromJson(Json, '$.description', recRef, SalesLine.FieldNo(Description), TempFieldSet);
        Common.ValidateFieldFromJson(Json, '$.quantity', recRef, SalesLine.FieldNo(Quantity), TempFieldSet);
        recRef.SetTable(SalesLine);
        if Common.GetJsonValue(Json, '$.amountInclTax').AsDecimal() <> 0 then
            Common.ValidateVATPostingGroup(SalesLine, Common.GetTaxRate(Json))
        else
            LastError := GetLastErrorText();
        exit(SalesLine.Modify());
    end;


    local procedure InsertNewSalesLine(
        Json: JsonObject;
        var SalesHeader: Record "Sales Header";
        var SalesLine: Record "Sales Line") Ok: Boolean

    var
        serialNumber: Code[50];
        amountIncludingTax: Decimal;
        amountExcludingTax: Decimal;
        taxAmount: Decimal;

    begin
        SalesLine.Init();
        SalesLine."Document Type" := SalesHeader."Document Type";
        SalesLine."Document No." := SalesHeader."No.";
        SalesLine."Line No." := Common.GetJsonValue(Json, '$.salesOrderLine.sequence').AsInteger();
        SalesLine.Insert();

        amountIncludingTax := Common.GetJsonValue(Json, '$.amountInclTax').AsDecimal();
        amountExcludingTax := Common.GetJsonValue(Json, '$.amountExclTax').AsDecimal();
        taxAmount := amountIncludingTax - amountExcludingTax;

        SalesLine.Validate("Sell-to Customer No.", SalesHeader."Sell-to Customer No.");
        SalesLine.Validate("Responsibility Center", SalesHeader."Responsibility Center");
        SalesLine.Validate("Type", SalesLine.Type::Item);
        SalesLine.Validate("No.", Common.GetJsonValue(Json, '$.product.code').AsCode());
        SalesLine.Validate("Quantity", Common.GetJsonValue(Json, '$.quantity').AsDecimal());
        SalesLine.Validate("Unit Price", Common.GetJsonValue(Json, '$.unitPrice').AsDecimal());
        SalesLine.Validate("Line Amount", Common.GetJsonValue(Json, '$.amountExclTax').AsDecimal());
        SalesLine.Validate("Amount Including VAT", Common.GetJsonValue(Json, '$.amountInclTax').AsDecimal());
        if not Common.GetJsonValue(Json, '$.serialNumber').IsNull then begin
            serialNumber := Common.GetJsonValue(Json, '$.serialNumber').AsCode();
            if serialNumber <> '' then
                salesLine.Validate("WSA Serial No.", serialNumber);
        end;
        SalesLine.Modify();
    end;


    local procedure PostInvoice(
        SalesHeader: Record "Sales Header";
        var SalesInvoiceHeader: Record "Sales Invoice Header") Ok: Boolean

    var
        SalesLine: Record "Sales Line";
        PostingNo: Code[20];

    begin
        ClearLastError();
        PostingNo := SalesHeader."Posting No.";

        SalesHeader.Ship := true;
        SalesHeader.Invoice := true;
        if SalesHeader.SendToPosting(Codeunit::"Sales-Post") then begin
            Commit();
            SalesLine.SetRange("Document Type", SalesHeader."Document Type");
            SalesLine.SetRange("Document No.", SalesHeader."No.");
            if not SalesLine.IsEmpty() then
                SalesLine.ModifyAll("Qty. to Invoice", 0);

            exit(SalesInvoiceHeader.Get(PostingNo));
        end else
            exit(false);
    end;


    local procedure ValidateExternalReferences(json: JsonObject)
    var
        ClinicCode: Code[10];
        LocationCode: Code[10];

    begin
        ValidatePatient(json);
        ValidateClinic(json, ClinicCode, LocationCode);
        ValidateProducts(json, LocationCode);
    end;


    local procedure ValidatePatient(json: JsonObject): Boolean
    var
        Customer: Record Customer;
        IntegrationManagement: Codeunit "Integration Management";
        jValue: JsonValue;

    begin
        jValue := Common.GetJsonValue(json, '$.patient.code');
        if jValue.IsNull() then begin
            TempErrorMessage.LogSimpleMessage(TempErrorMessage."Message Type"::Error, '$.patient.code is missing from request json.');
            exit(false);
        end;

        if Customer.Get(jValue.AsCode()) then
            exit(true)
        else begin
            if (IntegrationManagement.GetPatient(jValue.AsCode(), Customer)) then begin
                if Customer.Get(jValue.AsCode()) then
                    exit(true);
            end else begin
                TempErrorMessage.LogSimpleMessage(TempErrorMessage."Message Type"::Error, StrSubstNo('Unable to add patient %1 via API.', jValue.AsCode()));
                exit(false);
            end;
        end;

        TempErrorMessage.LogSimpleMessage(TempErrorMessage."Message Type"::Error, StrSubstNo('Unable to validated patient %1.', jValue.AsCode()));
        exit(false);
    end;


    local procedure ValidateClinic(json: JsonObject; var ClinicCode: Code[10]; var LocationCode: Code[10]): Boolean
    var
        ResponsibilityCenter: Record "Responsibility Center";
        Location: Record Location;
        jValue: JsonValue;

    begin
        jValue := Common.GetJsonValue(json, '$.clinic.code');
        if jValue.IsNull() then begin
            TempErrorMessage.LogSimpleMessage(TempErrorMessage."Message Type"::Error, '$.clinic.code is missing from request json.');
            exit(false);
        end;

        if not ResponsibilityCenter.Get(jValue.AsCode()) then begin
            TempErrorMessage.LogSimpleMessage(TempErrorMessage."Message Type"::Error, StrSubstNo('Responsibility Center %1 not found.', jValue.AsCode()));
            exit(false);
        end;

        if ResponsibilityCenter."Location Code" = '' then begin
            TempErrorMessage.LogSimpleMessage(TempErrorMessage."Message Type"::Error, StrSubstNo('Location Code is missing for Responsibility Center %1.', jValue.AsCode()));
            exit(false);
        end;

        if not Location.Get(ResponsibilityCenter."Location Code") then begin
            TempErrorMessage.LogSimpleMessage(TempErrorMessage."Message Type"::Error, StrSubstNo('Location %1 not found.', ResponsibilityCenter."Location Code"));
            exit(false);
        end;

        ClinicCode := ResponsibilityCenter."Code";
        LocationCode := ResponsibilityCenter."Location Code";
        exit(true);
    end;


    local procedure ValidateProducts(json: JsonObject; LocationCode: Code[10]): Boolean
    var
        jToken: JsonToken;
        lineToken: JsonToken;

    begin
        Json.SelectToken('$.salesInvoiceLines', jToken);
        foreach lineToken in jToken.AsArray do begin
            ValidateProduct(lineToken.AsObject());
            ValidateItemAvailability(lineToken.AsObject(), LocationCode);
        end;
    end;


    local procedure ValidateProduct(json: JsonObject): Boolean
    var
        Item: Record Item;
        IntegrationManagement: Codeunit "Integration Management";
        jValue: JsonValue;

    begin
        jValue := Common.GetJsonValue(json, '$.product.code');
        if jValue.IsNull() then begin
            TempErrorMessage.LogSimpleMessage(TempErrorMessage."Message Type"::Error, '$.product.code is missing from request json.');
            exit(false);
        end;

        if not Item.Get(jValue.AsCode()) then
            exit(IntegrationManagement.GetProduct(jValue.AsCode(), Item));


        if Item."Blocked" then
            exit(false);

        exit(true);
    end;


    local procedure ValidateItemAvailability(json: JsonObject; LocationCode: Code[10]): Boolean
    var
        Item: Record Item;
        ItemLedgerEntry: Record "Item Ledger Entry";
        jValue: JsonValue;
        serialNumberValue: JsonValue;

    begin
        jValue := Common.GetJsonValue(json, '$.product.code');
        if jValue.IsNull() then
            exit(false);

        if not Item.Get(jValue.AsCode()) then
            exit(false);

        if Item.Type <> Item.Type::Inventory then
            exit(true);

        if Item."Item Tracking Code" <> '' then begin
            serialNumberValue := Common.GetJsonValue(json, '$.serialNumber');
            if serialNumberValue.IsNull() then begin
                TempErrorMessage.LogSimpleMessage(TempErrorMessage."Message Type"::Error, '$.serialNumber is missing from request json.');
                exit(false);
            end;
            if serialNumberValue.AsCode() = '' then begin
                TempErrorMessage.LogSimpleMessage(TempErrorMessage."Message Type"::Error, StrSubstNo('Serial Number is required for Item: %1, but was not provided.', Item."No."));
                exit(false);
            end;
        end;

        ItemLedgerEntry.SetRange("Item No.", Item."No.");
        ItemLedgerEntry.SetRange("Location Code", LocationCode);
        ItemLedgerEntry.SetRange(Open, true);
        if Item."Item Tracking Code" <> '' then
            ItemLedgerEntry.SetRange("Serial No.", serialNumberValue.AsCode());
        if ItemLedgerEntry.IsEmpty() then begin
            if (Item."Item Tracking Code" <> '') then
                TempErrorMessage.LogSimpleMessage(TempErrorMessage."Message Type"::Error, StrSubstNo('Item %1, Serial Number: %2 is not available.',
                    Item."No.", serialNumberValue.AsCode()))
            else
                TempErrorMessage.LogSimpleMessage(TempErrorMessage."Message Type"::Error, StrSubstNo('Item %1 is not available.', Item."No."));
            exit(false);
        end;
    end;


    local procedure CleanupAfterFailure(DocumentNo: Code[20])
    var
        SalesHeader: Record "Sales Header";
        ReservationEntry: Record "Reservation Entry";
        SalesLine: Record "Sales Line";

    begin
        if not SalesHeader.Get(SalesHeader."Document Type"::Invoice, DocumentNo) then
            exit;

        ReservationEntry.SetRange("Source Type", Database::"Sales Line");
        ReservationEntry.SetRange("Source Subtype", SalesHeader."Document Type".AsInteger());
        ReservationEntry.SetRange("Source ID", DocumentNo);
        if not ReservationEntry.IsEmpty() then
            ReservationEntry.DeleteAll();

        SalesLine.SetRange("Document Type", SalesHeader."Document Type");
        SalesLine.SetRange("Document No.", DocumentNo);
        if not SalesLine.IsEmpty() then
            SalesLine.DeleteAll();

        SalesHeader."Posting No." := '';
        SalesHeader.Delete();
        Commit();
    end;


    local procedure ValidateTaxAreaCode(var SalesHeader: Record "Sales Header")
    var
        Field: Record Field;
        Location: Record Location;
        RecRef: RecordRef;
        FldRef: FieldRef;

    begin
        if SalesHeader."Location Code" = '' then
            exit;

        Field.SetRange(TableNo, Database::Location);
        Field.SetRange("No.", 10010);
        if Field.IsEmpty then
            exit;

        if not Location.Get(SalesHeader."Location Code") then
            exit;

        RecRef.GetTable(Location);
        FldRef := RecRef.Field(10010);
        if Format(FldRef.Value) <> '' then
            SalesHeader.Validate("Tax Area Code", FldRef.Value);
    end;


    local procedure ValidatePostingDate(json: JsonObject): Boolean
    var
        userSetupManagement: Codeunit "User Setup Management";
        genLedgerSetup: Record "General Ledger Setup";
        jValue: JsonValue;
        postingDate: Date;

    begin
        jValue := Common.GetJsonValue(json, '$.documentDate');
        if jValue.IsNull() then
            exit(false);

        postingDate := jValue.AsDate();

        exit(userSetupManagement.IsPostingDateValid(postingDate));
    end;

    var
        TempFieldSet: Record 2000000041 temporary;
        TempErrorMessage: Record "Error Message" temporary;
        Common: Codeunit "WSA Common";
}
