﻿using System.Text.Json.Serialization;

namespace WSA.Retail.Integration.PIM.GraphQL;

public class PisProductAssetOutputType
{
    [JsonPropertyName("images")] public List<PisImageAssetOutputType> Images { get; set; } = [];

    [JsonPropertyName("technicalDocuments")] public List<PisTechnicalDocumentAssetOutputType> TechnicalDocuments { get; set; } = [];

    [JsonPropertyName("type")] public PisProductAssetTypeOutputType? Type { get; set; }
}
