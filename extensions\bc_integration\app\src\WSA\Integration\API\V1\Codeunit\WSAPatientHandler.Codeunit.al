namespace WSA.Integration.API.V1;

using Microsoft.Integration.Graph;
using Microsoft.Sales.Customer;
using System.Utilities;
using WSA.Integration.Customer;
using WSA.Integration;


codeunit 50112 "WSA Patient Handler" implements "WSA Integration Request"
{
    TableNo = "WSA Integration Request Log";

    trigger OnRun()
    begin
        Code(Rec);
    end;


    procedure HandleRequest(var Request: Record "WSA Integration Request Log")
    begin
        if not Codeunit.Run(Codeunit::"WSA Patient Handler", Request) then begin
            Common.SetErrorResponse(Request, '');
        end;
    end;


    local procedure Code(var Request: Record "WSA Integration Request Log")
    var
        json: JsonObject;

    begin
        case Request.Method of
            Request.Method::post:
                HandlePost(Request);
            Request.Method::get:
                HandleGet(Request);
        end;
    end;


    local procedure HandlePost(var Request: Record "WSA Integration Request Log")
    var
        customer: Record Customer;

    begin
        if TryHandlePost(Request, customer) then
            Common.SetCreatedResponse(Request, Common.PatientToJson(customer))
        else
            Common.SetErrorResponse(Request, '');
    end;


    local procedure HandleGet(var Request: Record "WSA Integration Request Log")
    var
        customer: Record Customer;

    begin
        if not TryHandleGet(Request, customer) then
            Common.SetErrorResponse(Request, '');
    end;


    [TryFunction]
    local procedure TryHandlePost(
        var Request: Record "WSA Integration Request Log";
        var Customer: Record Customer)

    var
        recRef: RecordRef;
        json: JsonObject;

    begin
        Request.TestField("Request Content");
        json := Common.GetJsonFromBlob(Request);

        if not Customer.Get(Common.GetJsonValue(json, '$.code').AsCode()) then begin
            Customer.Init();
            Customer."No." := Common.GetJsonValue(json, '$.code').AsCode();
            Customer.Insert(false);
        end;

        Customer.Validate("WSA Customer Type", Customer."WSA Customer Type"::Patient);
        recRef.GetTable(Customer);
        Common.ValidateFieldFromJson(json, '$.name', recRef, Customer.FieldNo(Name), TempFieldSet);
        Common.ValidateFieldFromJson(json, '$.address', recRef, Customer.FieldNo(Address), TempFieldSet);
        Common.ValidateFieldFromJson(json, '$.address2', recRef, Customer.FieldNo("Address 2"), TempFieldSet);
        Common.ValidateFieldFromJson(json, '$.country', recRef, Customer.FieldNo("Country/Region Code"), TempFieldSet);
        Common.ValidateFieldFromJson(json, '$.region', recRef, Customer.FieldNo(County), TempFieldSet);
        Common.ValidateFieldFromJson(json, '$.postalCode', recRef, Customer.FieldNo("Post Code"), TempFieldSet);
        Common.ValidateFieldFromJson(json, '$.city', recRef, Customer.FieldNo(City), TempFieldSet);
        Common.ValidateFieldFromJson(json, '$.phone', recRef, Customer.FieldNo("Phone No."), TempFieldSet);
        Common.ValidateFieldFromJson(json, '$.email', recRef, Customer.FieldNo("E-Mail"), TempFieldSet);
        Common.ValidateFieldFromJson(json, '$.identificationNumber', recRef, Customer.FieldNo("WSA Patient Id No."), TempFieldSet);
        Common.ValidateFieldFromJson(json, '$.alternateCode', recRef, Customer.FieldNo("WSA External No."), TempFieldSet);

        GraphMgtGeneralTools.ProcessNewRecordFromAPI(recRef, TempFieldSet, CurrentDateTime());

        recRef.SetTable(Customer);
        Customer.TestField("WSA External No.");
        Customer.Modify(true);

        Request."Patient No." := Customer."No.";
        Request.Modify();
    end;


    [TryFunction]
    local procedure TryHandleGet(
        var Request: Record "WSA Integration Request Log";
        var Customer: Record Customer)

    var
        recRef: RecordRef;
        json: JsonObject;

    begin
        Request.TestField("Request Content");
        json := Common.GetJsonFromBlob(Request);

        if not (Common.GetJsonValue(json, '$.id').IsNull) then begin
            if Customer.GetBySystemId(Common.GetJsonValue(json, '$.id').AsText()) then begin
                Request."Patient No." := Customer."No.";
                Request.Modify();
                Common.SetOkResponse(Request, Common.PatientToJson(Customer));
                exit;
            end;
        end;

        if not (Common.GetJsonValue(json, '$.code').IsNull) then begin
            Customer.SetRange("No.", Common.GetJsonValue(json, '$.code').AsCode());
            if Customer.FindFirst() then begin
                Request."Patient No." := Customer."No.";
                Request.Modify();
                Common.SetOkResponse(Request, Common.PatientToJson(Customer));
                exit;
            end
        end;

        if not (Common.GetJsonValue(json, '$.name').IsNull) then begin
            Customer.SetRange(Name, Common.GetJsonValue(json, '$.name').AsCode());
            if Customer.FindFirst() then begin
                Request."Patient No." := Customer."No.";
                Request.Modify();
                Common.SetOkResponse(Request, Common.PatientToJson(Customer));
                exit;
            end
        end;

        Common.SetNoContentResponse(Request);
    end;

    procedure ValidatePatient(patientNo: Code[20]; var TempErrorMessage: Record "Error Message" temporary): Boolean
    var
        Customer: Record Customer;

    begin
        if Customer.Get(patientNo) then begin
            if Customer.Blocked <> Customer.Blocked::" " then begin
                TempErrorMessage.LogSimpleMessage(TempErrorMessage."Message Type"::Error, StrSubstNo('Patient %1 is blocked.', patientNo));
                exit(false);
            end;

            if Customer."Customer Posting Group" = '' then begin
                TempErrorMessage.LogSimpleMessage(TempErrorMessage."Message Type"::Error, StrSubstNo('Patient %1 does not have a valid customer posting group.', patientNo));
                exit(false);
            end;

            if Customer."WSA Customer Type" <> "WSA Customer Type"::Patient then begin
                TempErrorMessage.LogSimpleMessage(TempErrorMessage."Message Type"::Error, StrSubstNo('Customer %1 is not a patient.', patientNo));
                exit(false);
            end;
            exit(true);
        end else begin
            TempErrorMessage.LogSimpleMessage(TempErrorMessage."Message Type"::Error, StrSubstNo('Patient %1 does not exist.', patientNo));
            exit(false);
        end;

        TempErrorMessage.LogSimpleMessage(TempErrorMessage."Message Type"::Error, StrSubstNo('Unable to validated patient %1.', patientNo));
        exit(false);
    end;


    var

        TempFieldSet: Record ********** temporary;
        GraphMgtGeneralTools: Codeunit "Graph Mgt - General Tools";
        Common: Codeunit "WSA Common";
}
