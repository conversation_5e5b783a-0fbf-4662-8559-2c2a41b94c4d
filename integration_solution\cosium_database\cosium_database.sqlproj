﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003" ToolsVersion="4.0">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <Name>cosium_database</Name>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectVersion>4.1</ProjectVersion>
    <ProjectGuid>{f9ca2e6a-c25a-492a-bbf4-fbc984beca34}</ProjectGuid>
    <DSP>Microsoft.Data.Tools.Schema.Sql.SqlAzureV12DatabaseSchemaProvider</DSP>
    <OutputType>Database</OutputType>
    <RootPath>
    </RootPath>
    <RootNamespace>cosium_database</RootNamespace>
    <AssemblyName>cosium_database</AssemblyName>
    <ModelCollation>1033,CI</ModelCollation>
    <DefaultFileStructure>BySchemaAndSchemaType</DefaultFileStructure>
    <DeployToDatabase>True</DeployToDatabase>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <TargetLanguage>CS</TargetLanguage>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <SqlServerVerification>False</SqlServerVerification>
    <IncludeCompositeObjects>True</IncludeCompositeObjects>
    <TargetDatabaseSet>True</TargetDatabaseSet>
    <DefaultCollation>SQL_Latin1_General_CP1_CI_AS</DefaultCollation>
    <DefaultFilegroup>PRIMARY</DefaultFilegroup>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <OutputPath>bin\Release\</OutputPath>
    <BuildScriptName>$(MSBuildProjectName).sql</BuildScriptName>
    <TreatWarningsAsErrors>False</TreatWarningsAsErrors>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <DefineDebug>false</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <OutputPath>bin\Debug\</OutputPath>
    <BuildScriptName>$(MSBuildProjectName).sql</BuildScriptName>
    <TreatWarningsAsErrors>false</TreatWarningsAsErrors>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <DefineDebug>true</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup>
    <VisualStudioVersion Condition="'$(VisualStudioVersion)' == ''">11.0</VisualStudioVersion>
    <!-- Default to the v11.0 targets path if the targets file for the current VS version is not found -->
    <SSDTExists Condition="Exists('$(MSBuildExtensionsPath)\Microsoft\VisualStudio\v$(VisualStudioVersion)\SSDT\Microsoft.Data.Tools.Schema.SqlTasks.targets')">True</SSDTExists>
    <VisualStudioVersion Condition="'$(SSDTExists)' == ''">11.0</VisualStudioVersion>
  </PropertyGroup>
  <Import Condition="'$(SQLDBExtensionsRefPath)' != ''" Project="$(SQLDBExtensionsRefPath)\Microsoft.Data.Tools.Schema.SqlTasks.targets" />
  <Import Condition="'$(SQLDBExtensionsRefPath)' == ''" Project="$(MSBuildExtensionsPath)\Microsoft\VisualStudio\v$(VisualStudioVersion)\SSDT\Microsoft.Data.Tools.Schema.SqlTasks.targets" />
  <ItemGroup>
    <Folder Include="Properties" />
    <Folder Include="cosium\" />
    <Folder Include="cosium\tables" />
    <Folder Include="cosium\schemas" />
    <Folder Include="cosium\views" />
    <Folder Include="cosium\storedProcedures" />
  </ItemGroup>
  <ItemGroup>
    <Build Include="cosium\Tables\articles.sql" />
    <Build Include="cosium\Tables\ActionCaisse.sql" />
    <Build Include="cosium\Tables\mutuelle.sql" />
    <Build Include="cosium\Tables\medecin.sql" />
    <Build Include="cosium\Tables\TiersPayant.sql" />
    <Build Include="cosium\Tables\typemvtstock.sql" />
    <Build Include="cosium\Tables\facture.sql" />
    <Build Include="cosium\Tables\site.sql" />
    <Build Include="cosium\Tables\client.sql" />
    <Build Include="cosium\tables\staging_articlefacture.sql" />
    <Build Include="cosium\Tables\produit.sql" />
    <Build Include="cosium\Tables\fournisseur.sql" />
    <Build Include="cosium\Tables\marque.sql" />
    <Build Include="cosium\Tables\appareildivers.sql" />
    <Build Include="cosium\Tables\typefamille.sql" />
    <Build Include="cosium\Tables\typereglement.sql" />
    <Build Include="cosium\Tables\ts_typefacture.sql" />
    <Build Include="cosium\Tables\TauxTVA.sql" />
    <Build Include="cosium\Tables\Sav.sql" />
    <Build Include="cosium\Tables\RemiseBanque.sql" />
    <Build Include="cosium\Tables\ReglementFacture.sql" />
    <Build Include="cosium\Tables\origine.sql" />
    <Build Include="cosium\Tables\mvtstock.sql" />
    <Build Include="cosium\Tables\lienreglementreglement.sql" />
    <Build Include="cosium\Tables\lienreglementfacture.sql" />
    <Build Include="cosium\Tables\famillerdv.sql" />
    <Build Include="cosium\Tables\familleorigine.sql" />
    <Build Include="cosium\Tables\CaisseSecu.sql" />
    <Build Include="cosium\schemas\cosium.sql" />
    <Build Include="cosium\views\Products.sql" />
    <Build Include="cosium\tables\LastUpdate.sql" />
    <Build Include="cosium\views\Patients.sql" />
    <Build Include="cosium\views\Clinics.sql" />
    <Build Include="cosium\views\Manufacturers.sql" />
    <Build Include="cosium\views\Vendors.sql" />
    <Build Include="cosium\views\SalesInvoices.sql" />
    <Build Include="cosium\views\PaymentTypes.sql" />
    <Build Include="cosium\views\Payments.sql" />
    <Build Include="cosium\tables\staging_client.sql" />
    <Build Include="cosium\storedProcedures\UpsertStagedClient.sql" />
    <Build Include="cosium\views\PurchaseReceipts.sql" />
    <Build Include="cosium\views\PurchaseOrders.sql" />
    <Build Include="cosium\views\SalesCredits.sql" />
    <Build Include="cosium\views\SalesInvoiceData.sql" />
    <Build Include="cosium\tables\staging_produit.sql" />
    <Build Include="cosium\storedProcedures\UpsertStagedProduit.sql" />
    <Build Include="cosium\tables\staging_articles.sql" />
    <Build Include="cosium\storedProcedures\UpsertStagedArticles.sql" />
    <Build Include="cosium\tables\articlefacture.sql" />
    <Build Include="cosium\storedProcedures\UpsertStagedArticleFacture.sql" />
    <Build Include="cosium\tables\staging_facture.sql" />
    <Build Include="cosium\tables\staging_ActionCaisse.sql" />
    <Build Include="cosium\storedProcedures\UpsertStagedFacture.sql" />
    <Build Include="cosium\storedProcedures\UpsertStagedActionCaisse.sql" />
    <Build Include="cosium\tables\staging_lienreglementfacture.sql" />
    <Build Include="cosium\storedProcedures\UpsertStagedLienReglementFacture.sql" />
    <Build Include="cosium\tables\staging_mvtstock.sql" />
    <Build Include="cosium\storedProcedures\UpsertStagedMvtStock.sql" />
    <Build Include="cosium\Tables\Appareillage.sql" />
    <Build Include="cosium\Tables\Agenda.sql" />
    <Build Include="cosium\Tables\Categorierdv.sql" />
    <Build Include="cosium\Tables\Codemailing.sql" />
    <Build Include="cosium\Tables\Codemarketingclient.sql" />
    <Build Include="cosium\Tables\Codemarketingproduit.sql" />
    <Build Include="cosium\Tables\Utilisateurs.sql" />
    <Build Include="cosium\Tables\staging_ReglementFacture.sql" />
    <Build Include="cosium\storedProcedures\UpsertStagedReglementFacture.sql" />
    <Build Include="cosium\Tables\stg_agenda.sql" />
    <Build Include="cosium\Tables\stg_appareillage.sql" />
    <Build Include="cosium\Tables\stg_codemarketingclient.sql" />
    <Build Include="cosium\Tables\stg_codemarketingproduit.sql" />
    <Build Include="cosium\Tables\stg_TiersPayant.sql" />
    <Build Include="cosium\storedProcedures\sp_upsert_TiersPayant.sql" />
    <Build Include="cosium\storedProcedures\sp_upsert_codemarketingproduit.sql" />
    <Build Include="cosium\storedProcedures\sp_upsert_codemarketingclient.sql" />
    <Build Include="cosium\storedProcedures\sp_upsert_appareillage.sql" />
    <Build Include="cosium\storedProcedures\sp_upsert_agenda.sql" />
  </ItemGroup>
</Project>