﻿CREATE VIEW [cosium].[Clinics]
AS

SELECT Source.id,
       Source.codesite AS Code,
       Source.id AS ExternalCode,
       Source.nomsite AS [Name],
       Source.IntegrationRequired,
       Source.IntegrationDate,
       Source.ModifiedOn,
       t1.JSON

  FROM cosium.[site] AS Source

 OUTER APPLY (
       SELECT (
              SELECT [site].id AS 'externalCode',
                     [site].nomsite AS 'name',
                     [site].adressesite AS 'address',
                     [site].villesite AS 'city',
                     CASE WHEN UPPER([site].pays) = 'FRANCE' THEN 'FR'
                          WHEN UPPER([site].pays) = 'INDIA' THEN 'IN'
                          WHEN UPPER([site].pays) = 'GERMANY' THEN 'DE'
                          ELSE [site].pays
                      END AS 'country',
                     [site].cpsite AS 'postalCode',
                     [site].telsite AS 'phone'

                FROM cosium.[site]

               WHERE [site].id = Source.id

                 FOR JSON PATH, WITHOUT_ARRAY_WRAPPER

                     ) AS JSON
              ) AS t1