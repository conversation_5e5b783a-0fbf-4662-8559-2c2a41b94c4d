﻿CREATE TABLE [cosium].[TiersPayant] (
    [id]                NVARCHAR (50)  NOT NULL,
    [tauxss]            NVARCHAR (MAX) NULL,
    [montantsttc]       NVARCHAR (MAX) NULL,
    [montantslpp]       NVARCHAR (MAX) NULL,
    [montantsrc]        NVARCHAR (MAX) NULL,
    [montantsrc2]       NVARCHAR (MAX) NULL,
    [montantsrc3]       NVARCHAR (MAX) NULL,
    [montantsro]        NVARCHAR (MAX) NULL,
    [haselement]        NVARCHAR (MAX) NULL,
    [numeroslpp]        NVARCHAR (MAX) NULL,
    [montantpartrc]     NVARCHAR (MAX) NULL,
    [refmutuelles]      NVARCHAR (MAX) NULL,
    [montantsmutuelles] NVARCHAR (MAX) NULL,
    [reffacture]        NVARCHAR (MAX) NULL,
    [optionstp]         NVARCHAR (MAX) NULL,
    [natureassurance]   NVARCHAR (MAX) NULL,
    [numprisecharge]    NVARCHAR (MAX) NULL,
    [dateprisecharge]   NVARCHAR (MAX) NULL,
    [dateexport]        NVARCHAR (MAX) NULL,
    [dateaccident]      NVARCHAR (MAX) NULL,
    [numeroaccident]    NVARCHAR (MAX) NULL,
    [datemodif]         NVARCHAR (MAX) NULL,
    PRIMARY KEY CLUSTERED ([id] ASC)
);

