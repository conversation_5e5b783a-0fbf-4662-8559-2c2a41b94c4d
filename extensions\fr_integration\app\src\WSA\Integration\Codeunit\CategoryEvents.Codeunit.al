namespace WSA.Integration;
using Microsoft.Inventory.Item;

codeunit 50107 "Category Events"
{
    [EventSubscriber(ObjectType::Table, Database::"Item Category", OnAfterInsertEvent, '', true, true)]
    local procedure SendEventOnAfterInsertEvent(
        var Rec: Record "Item Category";
        RunTrigger: Boolean)

    begin
        if Rec.IsTemporary then
            exit;

        if RunTrigger then
            SendItemCategoryToIntegrationAPI(Rec);
    end;


    [EventSubscriber(ObjectType::Table, Database::"Item Category", OnAfterModifyEvent, '', true, true)]
    local procedure SendEventOnAfterModifyEvent(
        var Rec: Record "Item Category";
        var xRec: Record "Item Category";
        RunTrigger: Boolean)

    begin
        if Rec.IsTemporary then
            exit;

        if RunTrigger then
            SendItemCategoryToIntegrationAPI(Rec);
    end;


    local procedure SendItemCategoryToIntegrationAPI(ItemCategory: Record "Item Category")
    var
        RetailIntegrationSetup: Record "Retail Integration Setup";
        IntegrationManagement: Codeunit "Integration Management";
        JObject: JsonObject;

    begin
        RetailIntegrationSetup.SafeGet();
        if not RetailIntegrationSetup.Enabled then
            exit;

        if ItemCategory.Code = '' then
            exit;

        if ItemCategory.Description = '' then
            exit;

        if IsNullGuid(ItemCategory.SystemId) then
            exit;

        if ItemCategory."Parent Category" = '' then begin
            JObject.Add('code', ItemCategory.Code);
            JObject.Add('externalCode', Format(ItemCategory.SystemId).TrimStart('{').TrimEnd('}'));
            JObject.Add('name', ItemCategory.Description);

            if not IntegrationManagement.TrySendToApi(JObject, 'productcategories') then
                exit;
        end else begin
            JObject.Add('code', ItemCategory.Code);
            JObject.Add('externalCode', Format(ItemCategory.SystemId).TrimStart('{').TrimEnd('}'));
            JObject.Add('name', ItemCategory.Description);
            JObject.Add('parentCategoryCode', ItemCategory."Parent Category");

            if not IntegrationManagement.TrySendToApi(JObject, 'productsubcategories') then
                exit;
        end;

    end;
}