﻿CREATE PROCEDURE [cosium].[sp_upsert_codemarketingproduit]
AS
BEGIN
    SET NOCOUNT ON;

    -- Deduplicate staging table by (id, refproduit, codemailing), taking the latest datemodif
    SELECT 
        id,
        refproduit,
        codemailing,
        datemodif
    INTO #DedupedStaging
    FROM (
        SELECT 
            *,
            ROW_NUMBER() OVER (
                PARTITION BY id, refproduit, codemailing
                ORDER BY 
                    CASE WHEN datemodif IS NULL THEN 0 ELSE 1 END DESC,
                    datemodif DESC
            ) AS rn
        FROM [cosium].[stg_codemarketingproduit]
    ) ranked
    WHERE rn = 1;

    BEGIN TRY
        BEGIN TRANSACTION;

        MERGE [cosium].[codemarketingproduit] AS target
        USING #DedupedStaging AS source
        ON target.[id] = source.[id] 
           AND target.[refproduit] = source.[refproduit]
           AND target.[codemailing] = source.[codemailing]

        WHEN MATCHED AND (
                ISNULL(target.[datemodif], '') <> ISNULL(source.[datemodif], '')
            )
            THEN UPDATE SET
                target.[datemodif] = source.[datemodif],
                target.[ModifiedOn] = SYSUTCDATETIME()

        WHEN NOT MATCHED BY TARGET
            THEN INSERT (
                [id], 
                [refproduit], 
                [codemailing], 
                [datemodif], 
                [CreatedOn], 
                [ModifiedOn],
                [guid_id]
            )
            VALUES (
                source.[id], 
                source.[refproduit], 
                source.[codemailing], 
                source.[datemodif], 
                SYSUTCDATETIME(), 
                SYSUTCDATETIME(),
                NEWID()
            );

        COMMIT TRANSACTION;
    END TRY
    BEGIN CATCH
        IF @@TRANCOUNT > 0
            ROLLBACK TRANSACTION;

        DECLARE @ErrorMessage NVARCHAR(4000) = ERROR_MESSAGE();
        DECLARE @ErrorSeverity INT = ERROR_SEVERITY();
        DECLARE @ErrorState INT = ERROR_STATE();

        RAISERROR(@ErrorMessage, @ErrorSeverity, @ErrorState);
        RETURN;
    END CATCH

    DROP TABLE #DedupedStaging;
END
GO