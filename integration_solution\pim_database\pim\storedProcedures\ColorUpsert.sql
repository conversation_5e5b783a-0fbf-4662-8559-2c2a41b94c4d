﻿CREATE PROCEDURE [pim].[ColorUpsert]
(
@source [pim].[ColorUpsertTableType] READONLY,

-- Add output parameters for diagnostics
@rowsReceived INT = NULL OUTPUT,
@diagnosticMessage NVARCHAR(MAX) = NULL OUTPUT
)
AS

BEGIN
DECLARE @rowCount INT;
DECLARE @initialCount INT;
DECLARE @diagnosticLog NVARCHAR(MAX) = '';
DECLARE @firstMergeAffected INT;
DECLARE @finalCount INT;

-- Populate variables
SELECT @rowCount = COUNT(*) FROM @source;
SELECT @initialCount = COUNT(*) FROM pim.Color;

-- Build diagnostic info
SET @diagnosticLog = 'Received ' + CAST(@rowCount AS VARCHAR(20)) + ' rows in @source parameter' + CHAR(13) + CHAR(10) +
                    'Current pim.Color count: ' + CAST(@initialCount AS VARCHAR(20));
    
BEGIN TRY
SET XACT_ABORT ON
BEGIN TRANSACTION

 MERGE pim.Color AS Target
 USING (SELECT t1.Code,
               t2.[Name],
               t2.[HexCode]
          FROM (SELECT DISTINCT Code FROM @source) AS t1
         OUTER APPLY (
               SELECT TOP 1 [Name], [HexCode]
                 FROM @source AS s1
                WHERE s1.[Code] = t1.[Code]
                ORDER BY s1.[Name]) AS t2
       ) AS Source
    ON Source.Code = Target.Code

  WHEN NOT MATCHED BY Target 
  THEN
INSERT (Code,
       [Name],
       HexCode)

VALUES (Source.Code,
       Source.[Name],
       Source.HexCode)

  WHEN MATCHED AND (
       (Target.Name <> Source.Name) OR (Target.Name IS NULL AND Source.Name IS NOT NULL)
    OR (Target.HexCode <> Source.HexCode) OR (Target.HexCode IS NULL AND Source.HexCode IS NOT NULL))
  
  THEN 
UPDATE 
   SET Name = Source.Name,
       HexCode = Source.HexCode,
       SystemModifiedOn = sysutcdatetime(); 

   SET @firstMergeAffected = @@rowCount;
   SET @diagnosticLog = @diagnosticLog + CHAR(13) + CHAR(10) + 'Rows affected by MERGE: ' + CAST(@firstMergeAffected AS VARCHAR(20));

COMMIT TRANSACTION;

-- Final counts
SELECT @finalCount = COUNT(*) FROM pim.Color;
   SET @diagnosticLog = @diagnosticLog + CHAR(13) + CHAR(10) + 'Final pim.Color count: ' + CAST(@finalCount AS VARCHAR(20))

   END TRY
 BEGIN CATCH
    IF @@TRANCOUNT > 0
       ROLLBACK TRANSACTION;
            
       SET @diagnosticLog = @diagnosticLog + CHAR(13) + CHAR(10) + 'Error occurred: ' + ERROR_MESSAGE();
       SET @diagnosticMessage = @diagnosticLog;
       THROW;
   END CATCH
    
   SET @diagnosticMessage = @diagnosticLog;
END