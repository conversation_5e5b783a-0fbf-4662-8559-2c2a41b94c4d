﻿CREATE VIEW [cosium].[Manufacturers]
AS 

SELECT c.id AS Code,
       c.id AS ExternalCode,
       c.nommarque AS [Name],
       c.adresse AS [Address],
       c.adresse2 AS [Address2],
       c.ville AS [City],
       CASE WHEN UPPER(c.pays) = 'FRANCE' THEN 'FR'
            WHEN UPPER(c.pays) = 'INDIA' THEN 'IN'
            WHEN UPPER(c.pays) = 'GERMANY' THEN 'DE'
            ELSE c.pays
       END AS Country,
       c.codepostal AS PostalCode,
       c.tel AS Phone,
       c.ModifiedOn

 FROM [cosium].[marque] c 