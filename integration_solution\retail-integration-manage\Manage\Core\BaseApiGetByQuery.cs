﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using WSA.Retail.Integration.Manage.Configuration;
using WSA.Retail.Integration.Utilities;
using WSA.Retail.Integration.Core;

namespace WSA.Retail.Integration.Manage.Core;

public abstract class BaseApiGetByQuery<TEntity, TService>(
    IOptions<AppSettings> appSettings,
    TService entityService)
    where TEntity : IMasterData
    where TService : IMasterDataServiceBase<TEntity>
{
    protected readonly AppSettings _appSettings = appSettings.Value;
    protected readonly TService _entityService = entityService;

    public async Task<IActionResult> GetByQueryInternalAsync(
        HttpRequest req)
    {
        Guid? id = null;
        string? idValue = Common.GetQueryValue(req.Query, "id");
        if (idValue != null)
        {
            if (Guid.TryParse(idValue, out Guid id2))
            {
                id = (Guid?)id2;
            }
        }

        string? code = Common.GetQueryValue(req.Query, "code");
        string? externalCode = Common.GetQueryValue(req.Query, "externalCode");
        string? name = Common.GetQueryValue(req.Query, "name");
        bool raiseEvent = bool.Parse(Common.GetQueryValue(req.Query, "raiseEvent") ?? "false");

        try
        {
            var recordList = await _entityService.GetListAsync(
                _appSettings.ExternalSystemCode,
                id,
                code,
                name,
                externalCode);

            if (recordList != null && recordList.Count > 0)
            {
                if (raiseEvent)
                {
                    var taskList = new List<Task>();
                    foreach (TEntity record in recordList)
                    {
                        taskList.Add(_entityService.RaiseMockEventAsync(record));
                    }
                    await Task.WhenAll(taskList);
                }

                return new OkObjectResult(recordList);
            }
            else if (recordList != null && recordList.Count == 0)
            {
                return new OkObjectResult(null);
            }
            else
            {
                return new BadRequestResult();
            }
        }
        catch (Exception ex)
        {
            return new BadRequestObjectResult(ex.Message);
        }
    }
}
