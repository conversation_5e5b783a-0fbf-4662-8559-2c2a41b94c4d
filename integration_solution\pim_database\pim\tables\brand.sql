﻿CREATE TABLE [pim].[Brand]
(
    [Id]                    UNIQUEIDENTIFIER CONSTRAINT [DF_Brand_Id] DEFAULT NEWID() NOT NULL,
    [Code]                  NVARCHAR(20) NOT NULL,
    [Name]                  NVARCHAR(100) NULL,
    [SystemCreatedOn]       DATETIME2 CONSTRAINT [DF_Brand_SystemCreatedOn] DEFAULT (sysutcdatetime()) NOT NULL,
    [SystemModifiedOn]      DATETIME2 CONSTRAINT [DF_Brand_SystemModifiedOn] DEFAULT (sysutcdatetime()) NOT NULL,
    CONSTRAINT              [PK_Brand_Id] PRIMARY KEY CLUSTERED ([Id] ASC)
)
GO

CREATE UNIQUE INDEX IX_Brand_Code
ON [pim].[Brand] ([Code])
GO