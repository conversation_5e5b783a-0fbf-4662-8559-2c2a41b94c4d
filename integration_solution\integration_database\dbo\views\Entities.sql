﻿CREATE VIEW [dbo].[Entities]
AS 

SELECT Source.Id,
       Source.Code,
       Source.[Name],
       Source.[ModifiedOn],
       t1.JSON

  FROM dbo.Entity AS Source

 OUTER APPLY (SELECT(
              SELECT Entity.Id AS 'id',
                     UPPER(Entity.Code) AS 'code',
                     Entity.[Name] AS 'name'

                FROM dbo.Entity

                WHERE Entity.Id = Source.Id

                  FOR JSON PATH, WITHOUT_ARRAY_WRAPPER
                      ) AS JSON
                      ) AS t1