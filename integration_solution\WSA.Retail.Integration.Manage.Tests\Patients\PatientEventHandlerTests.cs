using Moq;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System.Text.Json;
using WSA.Retail.Integration.Data;
using WSA.Retail.Integration.Events;
using WSA.Retail.Integration.Manage.EventProcessing;
using WSA.Retail.Integration.Manage.Tests.Configuration;
using WSA.Retail.Integration.Manage.Models.Patients;
using Azure.Storage.Queues;
using WSA.Retail.Integration.Configuration;
using Microsoft.Extensions.Options;

namespace WSA.Retail.Integration.Manage.Tests.Patients;

public class PatientEventHandlerTests
{
    private static string LoadJsonFromFile(string entity, string fileName)
    {
        var path = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, entity, "TestData", fileName);
        if (!File.Exists(path))
        {
            throw new FileNotFoundException($"Test JSON file not found: {path}");
        }
        return File.ReadAllText(path);
    }

    [Fact]
    public void HandleFromQueueAsync_ValidMessage_ShouldContainExpectedData()
    {
        // Arrange
        var mockLogger = new Mock<ILogger<PatientEventHandler>>();
        var mockLoggerFactory = Mock.Of<ILoggerFactory>(lf => lf.CreateLogger(It.IsAny<string>()) == mockLogger.Object);


        var mockAppSettings = TestAppSettings.CreateDefault();
        var mockDbContextFactory = new Mock<IDbContextFactory<IntegrationContext>>();
        var mockHandler = new Mock<HttpMessageHandler>(MockBehavior.Strict);
        var httpClient = new HttpClient(mockHandler.Object);

        var appSettings = new Mock<IOptions<AppSettings>>();
        var queueClients = new Dictionary<string, QueueClient>();
        var mockEventGridPublisher = new EventGridPublisher(httpClient, appSettings.Object, queueClients);

        // Load JSON from file
        var jsonMessage = LoadJsonFromFile("Patients", "ValidPatientMessage.json");
        var eventMessage = JsonSerializer.Deserialize<EventHubMessage>(jsonMessage);

        // Deserialize the patient data from EventHubMessage.Message
        var msg = eventMessage!.Message?.ToString();
        var dto = JsonSerializer.Deserialize<PatientEventHubEvent>(msg!, Utilities.Common.GetJsonOptions());

        Assert.True(dto != null);

       // // Act
       //var model = dto!.upda(
       //     mockAppSettings,
       //     mockLoggerFactory,
       //     mockDbContextFactory.Object,
       //     mockEventGridPublisher); 
       

       // // Assert
       // Assert.Equal(dto!.PatientNumber, model!.Code);
       // Assert.Equal(dto!.FullName, model!.Name); 
    }
}