CREATE TABLE [manage].[CategoryMapping]
(
    [Id]                UNIQUEIDENTIFIER CONSTRAINT [DF_CategoryMapping_Id] DEFAULT NEWID() NOT NULL,
    [CategoryId]        UNIQUEIDENTIFIER NOT NULL,
    [IsHearingAid]      BIT CONSTRAINT [DF_CategoryMapping_IsHearingHead] DEFAULT((0)) NOT NULL,
    [HearingAidTypeId]  UNIQUEIDENTIFIER NULL,
    [CreatedOn]         DATETIME2 CONSTRAINT [DF_CategoryMapping_CreatedOn] DEFAULT (sysutcdatetime()) NOT NULL,
    [ModifiedOn]        DATETIME2 CONSTRAINT [DF_CategoryMapping_ModifiedOn] DEFAULT (sysutcdatetime()) NOT NULL,
    CONSTRAINT          [PK_CategoryMapping_Id] PRIMARY KEY CLUSTERED ([Id] ASC)
)
GO

CREATE UNIQUE INDEX IX_CategoryMapping_CategoryId
ON [manage].[CategoryMapping] ([CategoryId])
GO
