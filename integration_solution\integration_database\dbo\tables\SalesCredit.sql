﻿CREATE TABLE [dbo].[SalesCredit]
(
    [Id]             UNIQUEIDENTIFIER CONSTRAINT [DF_SalesCredit_Id] DEFAULT NEWID() NOT NULL,
    [DocumentNumber] NVARCHAR(20) NOT NULL,
    [AlternateNumber] NVARCHAR(50) NULL,
    [SalesOrderId]   UNIQUEIDENTIFIER NULL,
    [PatientId]      UNIQUEIDENTIFIER NOT NULL,
    [ClinicId]       UNIQUEIDENTIFIER NOT NULL,
    [DocumentDate]   DATE NULL,
    [IsActive]       BIT CONSTRAINT [DF_SalesCredit_IsActive] DEFAULT((1)) NOT NULL,
    [CreatedOn]      DATETIME2 CONSTRAINT [DF_SalesCredit_CreatedOn] DEFAULT (sysutcdatetime())  NOT NULL,
    [ModifiedOn]     DATETIME2 CONSTRAINT [DF_SalesCredit_ModifiedOn] DEFAULT (sysutcdatetime())  NOT NULL,
    CONSTRAINT       [PK_SalesCredit_Id] PRIMARY KEY CLUSTERED ([Id] ASC),
    CONSTRAINT       [FK_SalesCredit_SalesOrder] FOREIGN KEY (SalesOrderId) REFERENCES [dbo].[SalesOrder](Id),
    CONSTRAINT       [FK_SalesCredit_Patient] FOREIGN KEY (PatientId) REFERENCES [dbo].[Patient](Id),
    CONSTRAINT       [FK_SalesCredit_Clinic] FOREIGN KEY (ClinicId) REFERENCES [dbo].[Clinic](Id)
)
GO

CREATE UNIQUE INDEX IX_SalesCredit_Code
ON [dbo].[SalesCredit] ([DocumentNumber])
GO

CREATE INDEX IX_SalesCredit_SalesOrder
ON [dbo].[SalesCredit] ([SalesOrderId])
GO