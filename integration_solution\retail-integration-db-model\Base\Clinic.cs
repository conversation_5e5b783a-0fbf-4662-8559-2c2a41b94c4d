﻿namespace WSA.Retail.Integration.Base.Clinic;

public class Clinic
{
    private string? externalSystemCode;
    private string? code;
    private string? externalCode;
    private string? name;
    private string? address;
    private string? address2;
    private string? city;
    private string? region;
    private string? country;
    private string? postalCode;
    private string? phone;

    public Guid Id { get; set; } = Guid.NewGuid();
    public string? Code
    {
        get { return code; }
        set
        {
            code = value;
            ValidateNullOrEmpty(code, nameof(Code));
            ValidateLength(code, 20, nameof(Code));
        }
    }

    public string? ExternalSystemCode
    {
        get { return externalSystemCode; }
        set
        {
            externalSystemCode = value;
            ValidateNullOrEmpty(externalSystemCode, nameof(ExternalSystemCode));
            ValidateLength(externalSystemCode, 20, nameof(ExternalSystemCode));
        }
    }

    public string? ExternalCode
    {
        get { return externalCode; }
        set
        {
            externalCode = value;
            ValidateNullOrEmpty(externalCode, nameof(ExternalCode));
            ValidateLength(externalCode, 100, nameof(ExternalCode));
        }
    }

    public string? Name
    {
        get { return name; }
        set
        {
            name = value;
            ValidateLength(name, 100, nameof(Name));
        }
    }

    public string? Address
    {
        get { return address; }
        set
        {
            address = value;
            ValidateLength(address, 100, nameof(Address));
        }
    }

    public string? Address2
    {
        get { return address2; }
        set
        {
            address2 = value;
            ValidateLength(address2, 50, nameof(Address2));
        }
    }

    public string? City
    {
        get { return city; }
        set
        {
            city = value;
            ValidateLength(city, 30, nameof(City));
        }
    }

    public string? Region
    {
        get { return region; }
        set
        {
            region = value;
            ValidateLength(region, 30, nameof(Region));
        }
    }

    public string? Country
    {
        get { return country; }
        set
        {
            country = value;
            ValidateLength(country, 10, nameof(Country));
        }
    }

    public string? PostalCode
    {
        get { return postalCode; }
        set
        {
            postalCode = value;
            ValidateLength(postalCode, 20, nameof(PostalCode));
        }
    }

    public string? Phone
    {
        get { return phone; }
        set
        {
            phone = value;
            ValidateLength(phone, 30, nameof(Phone));
        }
    }

    public bool? IsActive { get; set; }


    private static void ValidateNullOrEmpty(string? value, string paramerterName)
    {
        if (value == null)
        {
            throw new ArgumentNullException(paramerterName);
        }
        else if (value == string.Empty)
        {
            throw new ArgumentException("Value cannot be empty.", paramerterName);
        }
    }

    private static void ValidateLength(string? value, int length, string paramerterName)
    {
        if (value is not null && value.Length > length)
        {
            throw new ArgumentOutOfRangeException(paramerterName, "Value cannot exceed " + length.ToString() + " characters.");
        }
    }
}

