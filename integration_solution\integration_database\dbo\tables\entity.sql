﻿CREATE TABLE [dbo].[Entity]
(
    [Id]            UNIQUEIDENTIFIER CONSTRAINT [DF_Entity_Id] DEFAULT NEWID() NOT NULL,
    [Code]          NVARCHAR(20) NOT NULL,
    [Name]          NVARCHAR(50) NULL,
    [Prefix]        NVARCHAR(10) NULL,
    [LastNumber]    INT NULL,
    [CreatedOn]     DATETIME2 CONSTRAINT [DF_Entity_CreatedOn] DEFAULT (sysutcdatetime()) NOT NULL,
    [ModifiedOn]    DATETIME2 CONSTRAINT [DF_Entity_ModifiedOn] DEFAULT (sysutcdatetime()) NOT NULL,
    CONSTRAINT      [PK_Entity_Id]	PRIMARY KEY CLUSTERED ([Id] ASC),
)
GO

CREATE UNIQUE INDEX IX_Entity_Code
ON [dbo].[Entity] ([Code])
GO

CREATE INDEX IX_Entity_Name
ON [dbo].[Entity] ([Name])
GO