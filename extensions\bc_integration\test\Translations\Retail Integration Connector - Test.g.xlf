﻿<?xml version="1.0" encoding="utf-8"?>
<xliff version="1.2" xmlns="urn:oasis:names:tc:xliff:document:1.2" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="urn:oasis:names:tc:xliff:document:1.2 xliff-core-1.2-transitional.xsd">
  <file datatype="xml" source-language="en-US" target-language="en-US" original="Retail Integration Connector - Test">
    <body>
      <group id="body">
        <trans-unit id="Codeunit ********** - NamedType **********" size-unit="char" translate="yes" xml:space="preserve">
          <source>JSON should not be empty.</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Codeunit APIV1 - Patients E2E - NamedType EmptyJSONErr</note>
        </trans-unit>
        <trans-unit id="Codeunit ********** - NamedType 866661614" size-unit="char" translate="yes" xml:space="preserve">
          <source>patients</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Codeunit APIV1 - Patients E2E - NamedType ServiceNameTxt</note>
        </trans-unit>
      </group>
    </body>
  </file>
</xliff>