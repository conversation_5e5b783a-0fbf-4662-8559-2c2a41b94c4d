﻿using WSA.Retail.Integration.Manage.API;
using WSA.Retail.Integration.Manage.Core;
using WSA.Retail.Integration.Manage.EventProcessing;
using WSA.Retail.Integration.Models.Products;

namespace WSA.Retail.Integration.Manage.Models.Products;

public class ProductAdapter :
    IEventHubEntityAdapter<ProductEventHubEvent, Product>,
    IManageRequestAdapter<ProductRequest, Product>,
    IManageResponseAdapter<ProductResponse, Product>
{
    public Product ToDomainModel(
        ProductEventHubEvent source,
        string externalSystemCode)

    {
        var product = new Product()
        {
            ExternalSystemCode = externalSystemCode,
            ExternalCode = source.Id.ToString(),
            Code = source.Id.ToString(),
            Name = source.Name
        };
        return product;
    }


    public ProductRequest ToManageRequest(Product product)
    {
        var request = new ProductRequest()
        {
            Quantity = 1,
            IsSellable = true,
            IsActive = true,
            IsSerialized = product.IsSerialized ?? false,
            PriceChangesAllowed = true,
            AutoDeliver = false,
            ControlledByStock = product.IsInventory ?? false,
            VendorProductNumber = product.VendorItemNo,
            Name = product.Name,
        };

        request.Colors = [];
        request.BatteryTypes = [];
        request.Attributes = [];
        request.SuggestedProductIds = [];

        if (product.Manufacturer?.ExternalCode != null)
        {
            request.ManufacturerId = Guid.Parse(product.Manufacturer?.ExternalCode!);
        }
        if (product.Vendor?.ExternalCode != null)
        {
            request.SupplierId = Guid.Parse(product.Vendor?.ExternalCode!);
        }
        if (product.Battery?.ExternalCode != null)
        {
            request.BatteryTypes = [];
            request.BatteryTypes.Add(Guid.Parse(product.Battery?.ExternalCode!));
        }
        if (product.Category?.ExternalCode != null)
        {
            request.CategoryId = Guid.Parse(product.Category?.ExternalCode!);
        }

        return request;
    }

    public Product ToDomainModel(ProductResponse source, string externalSystemCode)
    {
        return new Product()
        {
            Code = "UNSPECIFIED"
        };
    }
}