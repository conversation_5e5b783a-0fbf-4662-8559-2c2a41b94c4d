﻿CREATE TABLE dbo.ReconciliationPaymentTable (
    Id UNIQUEIDENTIFIER NOT NULL DEFAULT NEWID(),
    CreatedOn DATETIME2 NOT NULL DEFAULT SYSUTCDATETIME(),
    ModifiedOn DATETIME2 NOT NULL DEFAULT SYSUTCDATETIME(),
    ExternalSystemId UNIQUEIDENTIFIER NULL, -- This field should reference an external system
    DateField DATE NOT NULL,
    ClinicCode NVARCHAR(50) NOT NULL,
    PatientCode NVARCHAR(50) NOT NULL,
    PaymentOrderNumber NVARCHAR(50) NOT NULL,
    SalesOrderNumber NVARCHAR(50) NOT NULL,
    PaymentMethod NVARCHAR(50) NOT NULL,
    Amount DECIMAL(18, 2) NOT NULL,
    CONSTRAINT PK_ReconciliationPaymentTable PRIMARY KEY (Id)
);
GO


CREATE NONCLUSTERED INDEX IX_ReconciliationPaymentTable_ExternalSystemId_Date_Clinic_Patient_SalesOrder
ON dbo.ReconciliationPaymentTable (ExternalSystemId, DateField, ClinicCode, PatientCode, PaymentOrderNumber);
GO


CREATE TRIGGER dbo.ReconciliationPaymentTable_UpdateTrigger
ON dbo.ReconciliationPaymentTable
AFTER UPDATE
AS
BEGIN
    SET NOCOUNT ON;

    UPDATE dbo.ReconciliationPaymentTable
    SET ModifiedOn = SYSUTCDATETIME()
    FROM dbo.ReconciliationPaymentTable AS target
    INNER JOIN inserted AS i ON target.Id = i.Id;
END;