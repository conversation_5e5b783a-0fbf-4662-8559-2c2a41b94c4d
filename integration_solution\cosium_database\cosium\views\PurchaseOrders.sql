﻿CREATE VIEW [cosium].[PurchaseOrders]
AS
SELECT Source.id,
       CONCAT('PORD-', FORMAT(CAST(Source.id AS INT), '00000000')) AS DocumentNumber,
       Source.datefacture AS DocumentDate,
       Source.IntegrationRequired,
       Source.IntegrationDate,
       Source.ModifiedOn,
       t1.JSON

  FROM cosium.facture AS Source

       OUTER APPLY (SELECT 
                   (SELECT CONCAT('PORD-', FORMAT(CAST(facture.id AS INT), '00000000')) AS 'documentNumber',
                           facture.id AS 'externalReference',
                           LEFT(facture.numfacture, 35) AS 'alternateNumber',
                           Vendors.Code AS 'vendor.code',
                           Vendors.ExternalCode AS 'vendor.externalCode',
                           Vendors.[Name] AS 'vendor.name',
                           Clinics.Code AS 'clinic.code',
                           Clinics.ExternalCode AS 'clinic.externalCode',
                           Clinics.[Name] AS 'clinic.name',
                           facture.datefacture AS 'documentDate',
                           (SELECT articlefacture.id AS 'sequence',
                                   Products.Code AS 'product.code',
                                   Products.ExternalCode AS 'product.externalCode',
                                   Products.[Name] AS 'product.name',
                                   articlefacture.libelle AS 'description',
                                   CAST(IIF(ISNUMERIC(articlefacture.qtearticle) = 1, articlefacture.qtearticle, 0E0) AS DECIMAL) AS 'quantity',
                                   CAST(IIF(ISNUMERIC(articlefacture.prixunitttc) = 1, articlefacture.prixunitttc, 0E0) AS MONEY) AS 'unitPrice',
                                   CAST(CAST(IIF(ISNUMERIC(articlefacture.qtearticle) = 1, articlefacture.qtearticle, 0E0) AS DECIMAL) * 
                                        CAST(IIF(ISNUMERIC(articlefacture.prixunitttc) = 1, articlefacture.prixunitttc, 0E0) AS DECIMAL(18,4)) AS MONEY) AS 'grossAmount',
                                   CAST(IIF(ISNUMERIC(articlefacture.remise) = 1, articlefacture.remise, 0E0) AS MONEY) AS 'discountAmount',
                                   CAST(CAST(IIF(ISNUMERIC(articlefacture.totalarticlettc) = 1, articlefacture.totalarticlettc, 0E0) AS MONEY) / 
                                        (1.00 + (CAST(IIF(ISNUMERIC(articlefacture.tauxtva) = 1, articlefacture.tauxtva, 0E0) AS DECIMAL(18,4))/100)) AS MONEY) AS 'amountExclTax', 
                                   CAST(IIF(ISNUMERIC(articlefacture.totalarticlettc) = 1, articlefacture.totalarticlettc, 0E0) AS DECIMAL(18,4)) - 
                                        CAST(CAST(IIF(ISNUMERIC(articlefacture.totalarticlettc) = 1, articlefacture.totalarticlettc, 0E0) AS DECIMAL(18,4)) / 
                                        (1 + (CAST(IIF(ISNUMERIC(articlefacture.tauxtva) = 1, articlefacture.tauxtva, 0E0) AS DECIMAL(18,4))/100)) AS MONEY) AS 'taxAmount',
                                   CAST(IIF(ISNUMERIC(articlefacture.totalarticlettc) = 1, articlefacture.totalarticlettc, 0E0) AS MONEY) AS 'amountInclTax'

                              FROM cosium.articlefacture

                                   LEFT JOIN cosium.articles
                                          ON articlefacture.refarticle = articles.id

                                   LEFT JOIN cosium.Products
                                          ON articlefacture.refproduit = Products.ExternalCode

                             WHERE articlefacture.reffacture = facture.id

                               FOR JSON PATH
                                   ) AS 'purchaseOrderLines'

                      FROM cosium.facture

                           LEFT JOIN cosium.Vendors
                                  ON facture.refclient = Vendors.ExternalCode

                           LEFT JOIN cosium.Clinics
                                  ON facture.centre = Clinics.ExternalCode

                   WHERE facture.id = Source.id

                     FOR JSON PATH, WITHOUT_ARRAY_WRAPPER
                         ) AS JSON
                         ) AS t1

 WHERE Source.typefacture = 50