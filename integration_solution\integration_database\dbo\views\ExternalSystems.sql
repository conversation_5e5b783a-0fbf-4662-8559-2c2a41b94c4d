﻿CREATE VIEW [dbo].[ExternalSystems]
AS 

SELECT Source.Id,
       Source.Code,
       Source.[Name],
       Source.[CreatedOn],
       Source.[ModifiedOn],
       t1.JSON

  FROM dbo.ExternalSystem AS Source

 OUTER APPLY (SELECT(
              SELECT ExternalSystem.Id AS 'id',
                     UPPER(ExternalSystem.Code) AS 'code',
                     ExternalSystem.[Name] AS 'name'

                FROM dbo.ExternalSystem

                WHERE ExternalSystem.Id = Source.Id

                  FOR JSON PATH, WITHOUT_ARRAY_WRAPPER
                      ) AS JSON
                      ) AS t1