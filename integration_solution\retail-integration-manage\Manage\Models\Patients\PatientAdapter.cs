﻿using WSA.Retail.Integration.Manage.EventProcessing;
using WSA.Retail.Integration.Models.Patients;

namespace WSA.Retail.Integration.Manage.Models.Patients;

public class PatientAdapter : IEventHubEntityAdapter<PatientEventHubEvent, Patient>
{
    public Patient ToDomainModel(
        PatientEventHubEvent source,
        string externalSystemCode)
    {
        return new Patient()
        {
            ExternalSystemCode = externalSystemCode,
            ExternalCode = source.Id.ToString(),
            AlternateCode = source.PatientNumber,
            Code = source.PatientNumber,
            Name = source.FullName,
            Address = null,
            Address2 = null,
            City = null,
            Region = null,
            Country = null,
            PostalCode = null,
            Phone = null,
            Email = null
        };
    }
}