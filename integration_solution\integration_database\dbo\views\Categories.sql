﻿CREATE VIEW [dbo].[Categories]
AS 

SELECT Source.Id,
       Source.Code,
       (SELECT TOP 1 ExternalRecordId
          FROM dbo.Coupling
         WHERE Coupling.RecordId = Source.Id AND Coupling.ExternalSystemId = ExternalSystem.Id) AS ExternalCode,
       Source.[Name],
       ExternalSystem.Id AS ExternalSystemId,
       ExternalSystem.Code AS ExternalSystemCode,
       Source.[ModifiedOn],
       t1.JSON

  FROM dbo.ProductCategory AS Source

 OUTER APPLY dbo.ExternalSystem

 OUTER APPLY (SELECT(
              SELECT ProductCategory.Id AS 'id',
                     ExternalSystem.Code AS 'externalSystemCode',
                     UPPER(ProductCategory.Code) AS 'code',
                     (SELECT TOP 1 ExternalRecordId 
                        FROM dbo.Coupling 
                       WHERE Coupling.RecordId = ProductCategory.Id AND Coupling.ExternalSystemId = ExternalSystem.Id) AS 'externalCode',
                     ProductCategory.[Name] AS 'name'

                FROM dbo.ProductCategory

                WHERE ProductCategory.Id = Source.Id

                  FOR JSON PATH, WITHOUT_ARRAY_WRAPPER
                      ) AS JSON
                      ) AS t1