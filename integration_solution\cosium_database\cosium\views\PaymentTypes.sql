﻿CREATE VIEW cosium.PaymentTypes AS
SELECT typereglement.codetypereglement AS Code,
       typereglement.id AS ExternalCode,
       typereglement.description AS [Name],
       CASE typereglement.typerg
            WHEN 7 THEN 'cb'
            WHEN 8 THEN 'chq'
            WHEN 9 THEN 'esp'
            WHEN 10 THEN 'virement'
            WHEN 11 THEN 'avoir'
            WHEN 12 THEN 'cash3'
            WHEN 13 THEN 'tp_secu_chq'
            WHEN 14 THEN 'tp_secu_vir'
            WHEN 15 THEN 'tp_mut_chq'
            WHEN 16 THEN 'tp_mut_vir'
            WHEN 22 THEN 'pnfcredit'
            WHEN 23 THEN 'confinoga'
            WHEN 24 THEN 'amex'
            WHEN 30 THEN 'OD facture irrecouvrable'
            WHEN 31 THEN 'OD crediter compte client'
            WHEN 32 THEN 'OD debiter compte client'
            WHEN 33 THEN 'OD solde echeance client'
            WHEN 34 THEN 'OD solde facture fournisseur irrecouvrable'
            ELSE typereglement.typerg
       END AS [Type],
       typereglement.ModifiedOn

  FROM cosium.typereglement