﻿CREATE TABLE [manage].[OrderEvent]
(
    [Id]                UNIQUE<PERSON>ENTIFIER CONSTRAINT [DF_OrderEvent_Id] DEFAULT NEWID() NOT NULL,
    [EventType]         NVARCHAR(100),
    [MessageId]         UNIQUEIDENTIFIER,
    [OrderId]           UNIQUEIDENTIFIER,
    [ExternalNumber]    NVARCHAR(50),
    [LocationId]        UNIQUEIDENTIFIER,
    [SupplierId]        UNIQUEIDENTIFIER,
    [Timestamp]         DATETIME2,
    [LocalTimestamp]    DATETIME2,
    [BlobPath]          NVARCHAR(255),
    [Status]            INT,
    [ProcessedOn]       DATETIME2,
    [CreatedOn]         DATETIME2 CONSTRAINT [DF_OrderEvent_CreatedOn] DEFAULT (sysutcdatetime()) NOT NULL,
    [ModifiedOn]        DATETIME2 CONSTRAINT [DF_OrderEvent_ModifiedOn] DEFAULT (sysutcdatetime()) NOT NULL,
    CONSTRAINT          [PK_OrderEvent_Id] PRIMARY KEY CLUSTERED ([Id] ASC)
)
GO

CREATE UNIQUE INDEX IX_OrderEvent_MessageId
ON [manage].[OrderEvent] ([MessageId])
GO

CREATE INDEX IX_OrderEvent_Status
ON [manage].[OrderEvent] ([Status], [MessageId])
