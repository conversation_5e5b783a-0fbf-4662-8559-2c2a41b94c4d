namespace WSA.Integration;

using Microsoft.Inventory.Item.Catalog;

codeunit 50138 "Manufacturer Events"
{
    [EventSubscriber(ObjectType::Table, Database::Manufacturer, OnAfterInsertEvent, '', true, true)]
    local procedure SendEventOnAfterInsertEvent(
        var Rec: Record Manufacturer;
        RunTrigger: Boolean)

    begin
        if Rec.IsTemporary then
            exit;

        if RunTrigger then
            SendManufacturerToIntegrationAPI(Rec);
    end;


    [EventSubscriber(ObjectType::Table, Database::Manufacturer, OnAfterModifyEvent, '', true, true)]
    local procedure SendEventOnAfterModifyEvent(
        var Rec: Record Manufacturer;
        var xRec: Record Manufacturer;
        RunTrigger: Boolean)

    begin
        if Rec.IsTemporary then
            exit;

        if RunTrigger then
            SendManufacturerToIntegrationAPI(Rec);
    end;


    local procedure SendManufacturerToIntegrationAPI(Manufacturer: Record Manufacturer)
    var
        RetailIntegrationSetup: Record "Retail Integration Setup";
        IntegrationManagement: Codeunit "Integration Management";
        Common: Codeunit "WSA Common";
        JObject: JsonObject;

    begin
        RetailIntegrationSetup.SafeGet();
        if not RetailIntegrationSetup.Enabled then
            exit;

        if Manufacturer.Code = '' then
            exit;

        if Manufacturer.Name = '' then
            exit;

        if IsNullGuid(Manufacturer.SystemId) then
            exit;

        JObject := Common.ManufacturerToJson(Manufacturer);

        if not IntegrationManagement.TryRaiseEvent(JObject, 'manufacturers', Format(Manufacturer.SystemId).TrimStart('{').TrimEnd('}')) then
            exit;
    end;
}