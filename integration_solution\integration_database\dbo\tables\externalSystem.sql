﻿CREATE TABLE [dbo].[ExternalSystem]
(
    [Id]                    UNIQUEIDENTIFIER NOT NULL CONSTRAINT [DF_ExternalSystem_Id] DEFAULT NEWID(),
    [Code]                  NVARCHAR(255) NOT NULL,
    [Name]                  NVARCHAR(100) NULL,
    [CreatedOn]             DATETIME2 CONSTRAINT [DF_ExternalSystem_CreatedOn] DEFAULT (sysutcdatetime()) NOT NULL,
    [ModifiedOn]            DATETIME2 CONSTRAINT [DF_ExternalSystem_ModifiedOn] DEFAULT (sysutcdatetime()) NOT NULL,
    CONSTRAINT              [PK_ExternalSystem_Id] PRIMARY KEY CLUSTERED ([Id] ASC)
)
GO

CREATE UNIQUE INDEX IX_ExternalSystem_Code
ON [dbo].[ExternalSystem] ([Code])
GO

CREATE INDEX IX_ExternalSystem_Name
ON [dbo].[ExternalSystem] ([Name])
GO