﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace WSA.Retail.Integration.Cosium
{
    [Table("client", Schema = "cosium")]
    public class Client
    {
        [Key]
        public string? Id { get; set; }
        public string? Numeroclient { get; set; }
        public string? Nom { get; set; }
        public string? Prenom { get; set; }
        public string? Secunumero { get; set; }
        public bool? IntegrationRequired { get; set; }
        public DateTime? IntegrationDate { get; set; }
        public DateTime ModifiedOn { get; set; }
    }
}
