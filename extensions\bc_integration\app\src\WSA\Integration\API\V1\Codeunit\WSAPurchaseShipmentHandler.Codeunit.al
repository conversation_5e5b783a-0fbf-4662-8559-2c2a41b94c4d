namespace WSA.Integration.API.V1;

using Microsoft.Purchases.Document;
using Microsoft.Purchases.History;
using Microsoft.Purchases.Posting;
using Microsoft.Inventory.Location;
using Microsoft.Purchases.Vendor;
using Microsoft.Finance.Dimension;
using Microsoft.Inventory.Item;
using Microsoft.Inventory.Tracking;
using WSA.Integration;

codeunit 50129 "WSA Purchase Shipment Handler" implements "WSA Integration Request"
{
    TableNo = "WSA Integration Request Log";

    trigger OnRun()
    begin
        Code(Rec);
    end;


    procedure HandleRequest(var Request: Record "WSA Integration Request Log")
    begin
        if not Codeunit.Run(Codeunit::"WSA Purchase Shipment Handler", Request) then begin
            Common.SetErrorResponse(Request, '');
        end;
    end;


    local procedure Code(var Request: Record "WSA Integration Request Log")
    var
        json: JsonObject;

    begin
        case Request.Method of
            Request.Method::post:
                HandlePost(Request);
        end;
    end;


    local procedure HandlePost(var Request: Record "WSA Integration Request Log")
    var
        purchaseReturn: Record "Purchase Header";
        purchaseLine: Record "Purchase Line";
        reservationEntry: Record "Reservation Entry";
        json: JsonObject;

    begin
        if not TryHandlePost(Request, purchaseReturn) then begin
            json := Common.GetJsonFromBlob(Request);
            CleanupAfterFailure(Common.GetJsonValue(json, '$.documentNumber').AsCode());
            Common.SetErrorResponse(Request, '');
        end;

    end;


    [TryFunction]
    local procedure TryHandlePost(
        var Request: Record "WSA Integration Request Log";
        var PurchaseReturn: Record "Purchase Header")

    var
        returnShipmentHeader: Record "Return Shipment Header";
        json: JsonObject;

    begin
        json := Common.GetJsonFromBlob(Request);
        if not GetPurchaseHeader(json, PurchaseReturn) then begin
            CleanupAfterFailure(Common.GetJsonValue(json, '$.documentNumber').AsCode());
            Common.SetErrorResponse(Request, 'Purchase return order not found');
            exit;
        end;

        if PurchaseReturn.Status <> PurchaseReturn.Status::Open then begin
            PurchaseReturn.Status := PurchaseReturn.Status::Open;
            PurchaseReturn.Modify();
        end;

        UpdateReceivingNo(json, PurchaseReturn);
        UpdateVendorOrderNo(json, PurchaseReturn);
        UpdateDates(json, PurchaseReturn);
        UpdateLines(json, PurchaseReturn);

        if PurchaseReturn.Status <> PurchaseReturn.Status::Released then begin
            PurchaseReturn.Status := PurchaseReturn.Status::Released;
            PurchaseReturn.Modify();
        end;

        if PostReceipt(PurchaseReturn, returnShipmentHeader) then begin
            Request."Purchase Order No." := PurchaseReturn."No.";
            Request."Purchase Receipt No." := returnShipmentHeader."No.";
            Request.Modify();
            Common.SetCreatedResponse(Request, Common.PurchaseShipmentToJson(returnShipmentHeader))
        end else begin
            CleanupAfterFailure(Common.GetJsonValue(json, '$.documentNumber').AsCode());
            Common.SetErrorResponse(Request, '');
        end;
    end;


    local procedure GetPurchaseHeader(
        Json: JsonObject;
        var PurchaseHeader: Record "Purchase Header") Ok: boolean

    var
        PurchaseLine: Record "Purchase Line";
        IntegrationManagement: Codeunit "Integration Management";
        jValue: JsonValue;
        vendorNo: Code[20];
        clinicCode: Code[10];

    begin
        jValue := Common.GetJsonValue(Json, '$.purchaseReturn.documentNumber');
        if not jValue.IsNull() then begin
            if (PurchaseHeader.Get(PurchaseHeader."Document Type"::"Return Order", jValue.AsCode())) then
                exit(true);

            if (IntegrationManagement.GetPurchaseOrder(jValue.AsCode(), PurchaseHeader)) then
                exit(true);

            exit(false);
        end;

        jValue := Common.GetJsonValue(Json, '$.vendor.code');
        if jValue.IsNull() then
            exit(false)
        else
            vendorNo := jValue.AsCode();

        jValue := Common.GetJsonValue(Json, '$.clinic.code');
        if jValue.IsNull() then
            exit(false)
        else
            clinicCode := jValue.AsCode();

        if (PurchaseHeader.Get(PurchaseHeader."Document Type"::"Return Order", Common.GetJsonValue(Json, '$.documentNumber').AsCode())) then begin
            if PurchaseHeader."Buy-from Vendor No." <> vendorNo then
                PurchaseHeader.Validate("Buy-from Vendor No.", vendorNo);
            if PurchaseHeader."Responsibility Center" <> clinicCode then
                PurchaseHeader.Validate("Responsibility Center", clinicCode);
            exit(true);
        end;

        if CreatePurchaseReturn(Json, PurchaseHeader) then
            exit(true);

        exit(false);
    end;


    local procedure CreatePurchaseReturn(
        Json: JsonObject;
        var PurchaseHeader: Record "Purchase Header") Ok: boolean

    var
        PurchaseLine: Record "Purchase Line";
        RecRef: RecordRef;
        lines: JsonToken;
        line: JsonToken;

    begin
        PurchaseHeader.Init();
        PurchaseHeader."Document Type" := PurchaseHeader."Document Type"::"Return Order";
        PurchaseHeader."No." := Common.GetJsonValue(Json, '$.documentNumber').AsCode();
        PurchaseHeader.Validate("Buy-from Vendor No.", Common.GetJsonValue(Json, '$.vendor.code').AsCode());
        PurchaseHeader.Validate("Responsibility Center", Common.GetJsonValue(Json, '$.clinic.code').AsCode());
        PurchaseHeader.Insert();

        RecRef.GetTable(PurchaseHeader);
        Common.ValidateFieldFromJson(json, '$.alternateNumber', recRef, PurchaseHeader.FieldNo("Vendor Order No."), TempFieldSet);
        Common.ValidateFieldFromJson(json, '$.documentDate', recRef, PurchaseHeader.FieldNo("Posting Date"), TempFieldSet);
        Common.ValidateFieldFromJson(json, '$.documentDate', recRef, PurchaseHeader.FieldNo("Document Date"), TempFieldSet);
        RecRef.SetTable(PurchaseHeader);
        PurchaseHeader.Modify();

        PurchaseLine.SetRange("Document Type", PurchaseHeader."Document Type");
        PurchaseLine.SetRange("Document No.", PurchaseHeader."No.");
        if not PurchaseLine.IsEmpty() then
            PurchaseLine.DeleteAll(false);

        if json.SelectToken('$.purchaseReceiptLines', lines) then begin
            if lines.IsArray then begin
                foreach line in lines.AsArray() do begin
                    if not purchaseLine.Get(PurchaseHeader."Document Type", PurchaseHeader."No.",
                            Common.GetJsonValue(line.AsObject(), '$.sequence').AsInteger()) then begin
                        purchaseLine.Init();
                        purchaseLine."Document Type" := PurchaseHeader."Document Type";
                        purchaseLine."Document No." := PurchaseHeader."No.";
                        purchaseLine."Line No." := Common.GetJsonValue(line.AsObject(), '$.sequence').AsInteger();
                        purchaseLine.Insert();
                    end;

                    if (purchaseLine.Type <> purchaseLine.Type::Item) then
                        purchaseLine.Validate(Type, purchaseLine.Type::Item);
                    recRef.GetTable(purchaseLine);
                    Common.ValidateFieldFromJson(line.AsObject(), '$.product.code', recRef, purchaseLine.FieldNo("No."), TempFieldSet);
                    Common.ValidateFieldFromJson(line.AsObject(), '$.description', recRef, purchaseLine.FieldNo(Description), TempFieldSet);
                    Common.ValidateFieldFromJson(line.AsObject(), '$.quantity', recRef, purchaseLine.FieldNo(Quantity), TempFieldSet);
                    recRef.SetTable(purchaseLine);

                    purchaseLine.Validate("Qty. to Receive", 0);
                    purchaseLine.Validate("Qty. to Invoice", 0);
                    purchaseLine.Modify();
                end;
            end;
            exit(true);
        end;
    end;


    local procedure UpdateReceivingNo(
        Json: JsonObject;
        var PurchaseHeader: Record "Purchase Header")

    var
        newReceivingNo: Code[20];

    begin
        newReceivingNo := Common.GetJsonValue(Json, '$.documentNumber').AsCode();
        if newReceivingNo <> '' then
            PurchaseHeader."Receiving No." := newReceivingNo;
    end;

    local procedure UpdateVendorOrderNo(
        Json: JsonObject;
        var PurchaseHeader: Record "Purchase Header")

    var
        jvalue: JsonValue;
        newVendorOrderNo: Code[35];

    begin
        jvalue := Common.GetJsonValue(Json, '$.alternateNumber');
        if not jvalue.IsNull() then
            PurchaseHeader.Validate("Vendor Order No.", jvalue.AsCode());
    end;


    local procedure UpdateDates(
        Json: JsonObject;
        var PurchaseHeader: Record "Purchase Header")

    var
        newDate: Date;

    begin
        newDate := Common.GetJsonValue(Json, '$.documentDate').AsDate();
        if newDate <> 0D then begin
            PurchaseHeader.Validate("Posting Date", newDate);
            PurchaseHeader.Validate("Document Date", newDate);
        end;
    end;


    local procedure UpdateLines(
        Json: JsonObject;
        var PurchaseHeader: Record "Purchase Header")

    var
        PurchaseLine: Record "Purchase Line";
        jToken: JsonToken;
        lineToken: JsonToken;

    begin
        PurchaseLine.SetRange("Document Type", PurchaseHeader."Document Type");
        PurchaseLine.SetRange("Document No.", PurchaseHeader."No.");
        PurchaseLine.ModifyAll("Return Qty. to Ship", 0);

        Json.SelectToken('$.purchaseShipmentLines', jToken);
        foreach lineToken in jToken.AsArray do begin
            UpdateLine(lineToken.AsObject(), PurchaseHeader);
        end;
    end;


    local procedure UpdateLine(
        Json: JsonObject;
        var PurchaseHeader: Record "Purchase Header")

    var
        purchaseLine: Record "Purchase Line";
        tempReservationEntry: Record "Reservation Entry" temporary;
        defaultDimension: Record "Default Dimension";
        ResManagement: Codeunit "Reservation Management";
        CreateReservEntry: Codeunit "Create Reserv. Entry";
        dimMgt: Codeunit DimensionManagement;
        tempTrackingSpecification: Record "Tracking Specification" temporary;
        tempDimSetEntry: Record "Dimension Set Entry" temporary;
        tempDimSetEntry2: Record "Dimension Set Entry" temporary;
        newDimSetId: Integer;
        quantity: Decimal;
        qtyToReceive: Decimal;
        qtyToInvoice: Decimal;
        serialNoValue: JsonValue;
        serialNumber: Code[50];
        newSerialNumberList: Code[1024];
        oldSerialNumberList: List of [Text];

    begin
        if GetPurchaseLine(Json, PurchaseHeader, purchaseLine) then begin
            if purchaseLine."Buy-from Vendor No." <> '' then begin
                defaultDimension.SetRange("Table ID", Database::Vendor);
                defaultDimension.SetRange("No.", purchaseLine."Buy-from Vendor No.");
                if defaultDimension.FindSet() then begin
                    dimMgt.GetDimensionSet(tempDimSetEntry, purchaseLine."Dimension Set ID");
                    if tempDimSetEntry.FindSet() then
                        repeat
                            tempDimSetEntry2.Init();
                            tempDimSetEntry2."Dimension Set ID" := 0;
                            tempDimSetEntry2.Validate("Dimension Code", tempDimSetEntry."Dimension Code");
                            tempDimSetEntry2.Validate("Dimension Value Code", tempDimSetEntry."Dimension Value Code");
                            tempDimSetEntry2.Insert();
                        until tempDimSetEntry.Next() = 0;

                    repeat
                        if not tempDimSetEntry2.Get(0, defaultDimension."Dimension Code") then begin
                            tempDimSetEntry2.Init();
                            tempDimSetEntry2."Dimension Set ID" := 0;
                            tempDimSetEntry2.Validate("Dimension Code", defaultDimension."Dimension Code");
                            tempDimSetEntry2.Validate("Dimension Value Code", defaultDimension."Dimension Value Code");
                            tempDimSetEntry2.Insert();
                        end;
                    until defaultDimension.Next() = 0;
                    newDimSetId := dimMgt.GetDimensionSetID(tempDimSetEntry2);
                    if (newDimSetId <> 0) and (newDimSetId <> purchaseLine."Dimension Set ID") then begin
                        purchaseLine."Dimension Set ID" := newDimSetId;
                        purchaseLine.Modify();
                    end;
                end;
            end;


            if purchaseLine."Outstanding Quantity" <> purchaseLine.Quantity - purchaseLine."Return Qty. Shipped" then
                purchaseLine."Outstanding Quantity" := purchaseLine.Quantity - purchaseLine."Return Qty. Shipped";

            if (purchaseLine.Quantity = purchaseLine."Quantity (Base)") then begin
                if purchaseLine."Qty. Received (Base)" <> purchaseLine."Return Qty. Shipped" then
                    purchaseLine."Qty. Received (Base)" := purchaseLine."Return Qty. Shipped";

                if purchaseLine."Outstanding Qty. (Base)" <> purchaseLine."Outstanding Quantity" then
                    purchaseLine."Outstanding Qty. (Base)" := purchaseLine."Outstanding Quantity";
            end;

            quantity := Common.GetJsonValue(Json, '$.quantity').AsDecimal();
            if purchaseLine."Return Qty. Shipped" + quantity > purchaseLine."Quantity" then begin
                qtyToReceive := purchaseLine."Return Qty. to Ship";
                qtyToInvoice := purchaseLine."Return Qty. to Ship";
                purchaseLine.Validate("Quantity", purchaseLine."Return Qty. Shipped" + quantity);
                purchaseLine.Validate("Return Qty. to Ship", qtyToReceive);
                purchaseLine.Validate("Return Qty. to Ship", qtyToInvoice);
            end;

            purchaseLine.Validate("Return Qty. to Ship", purchaseLine."Return Qty. to Ship" +
                Common.GetJsonValue(Json, '$.quantity').AsDecimal());

            purchaseLine.Validate("Expected Receipt Date", PurchaseHeader."Posting Date");
            purchaseLine.Modify();

            serialNoValue := Common.GetJsonValue(Json, '$.serialNumber');
            if not serialNoValue.IsNull then begin
                serialNumber := CopyStr(serialNoValue.AsCode(), 1, MaxStrLen(serialNumber));
                if serialNumber <> '' then begin
                    tempReservationEntry.Init();
                    tempReservationEntry."Entry No." := 1;
                    tempReservationEntry."Serial No." := serialNumber;
                    tempReservationEntry.Quantity := 1;
                    tempReservationEntry."Expiration Date" := Today();
                    tempReservationEntry.Insert();

                    if tempReservationEntry.FindSet() then
                        repeat
                            CreateReservEntry.SetDates(0D, tempReservationEntry."Expiration Date");
                            CreateReservEntry.CreateReservEntryFor(
                                Database::"Purchase Line",
                                purchaseLine."Document Type".AsInteger(),
                                purchaseLine."Document No.",
                                '',
                                0,
                                purchaseLine."Line No.",
                                purchaseLine."Qty. per Unit of Measure",
                                tempReservationEntry.Quantity,
                                tempReservationEntry.Quantity * purchaseLine."Qty. per Unit of Measure",
                                tempReservationEntry);
                            CreateReservEntry.CreateEntry(
                                purchaseLine."No.",
                                purchaseLine."Variant Code",
                                purchaseLine."Location Code",
                                '',
                                purchaseLine."Expected Receipt Date",
                                0D,
                                0,
                                "Reservation Status"::Surplus);
                        until tempReservationEntry.Next() = 0;
                end;
            end;
            purchaseLine.Modify();
        end;
    end;


    local procedure GetPurchaseLine(
        Json: JsonObject;
        var PurchaseHeader: Record "Purchase Header";
        var PurchaseLine: Record "Purchase Line") Ok: Boolean

    var
        jValue: JsonValue;
        recRef: RecordRef;

    begin
        jValue := Common.GetJsonValue(Json, '$.purchaseReturnLine.sequence');
        if not jValue.IsNull() then
            exit(PurchaseLine.Get(
                PurchaseHeader."Document Type",
                PurchaseHeader."No.",
                jValue.AsInteger()));

        jValue := Common.GetJsonValue(Json, '$.sequence');
        if not jValue.IsNull() then
            exit(PurchaseLine.Get(
                PurchaseHeader."Document Type",
                PurchaseHeader."No.",
                jValue.AsInteger()));

        exit(false);
    end;


    local procedure PostReceipt(
        PurchaseHeader: Record "Purchase Header";
        var ReturnShipmentHeader: Record "Return Shipment Header") Ok: Boolean

    var
        PurchaseLine: Record "Purchase Line";
        OrderNo: Code[20];
        OrderNoSeries: Code[20];

    begin
        ClearLastError();
        OrderNo := PurchaseHeader."No.";
        OrderNoSeries := PurchaseHeader."No. Series";

        PurchaseHeader.Ship := true;
        PurchaseHeader.Invoice := false;

        Commit;
        if PurchaseHeader.SendToPosting(Codeunit::"Purch.-Post") then begin
            Commit();
            PurchaseLine.SetRange("Document Type", PurchaseHeader."Document Type");
            PurchaseLine.SetRange("Document No.", PurchaseHeader."No.");
            if not PurchaseLine.IsEmpty() then
                PurchaseLine.ModifyAll("Qty. to Receive", 0);

            ReturnShipmentHeader.SetCurrentKey("Return Order No.");
            ReturnShipmentHeader.SetRange("Return Order No.", OrderNo);
            ReturnShipmentHeader.SetRange("Return Order No. Series", OrderNoSeries);
            exit(ReturnShipmentHeader.FindLast());
        end else
            exit(false);

    end;

    local procedure CleanupAfterFailure(DocumentNo: Code[20])
    var
        PurchaseHeader: Record "Purchase Header";
        ReservationEntry: Record "Reservation Entry";
        PurchaseLine: Record "Purchase Line";

    begin
        if not PurchaseHeader.Get(PurchaseHeader."Document Type"::"Return Order", DocumentNo) then
            exit;

        ReservationEntry.SetRange("Source Type", Database::"Purchase Line");
        ReservationEntry.SetRange("Source Subtype", PurchaseHeader."Document Type".AsInteger());
        ReservationEntry.SetRange("Source ID", DocumentNo);
        if not ReservationEntry.IsEmpty() then
            ReservationEntry.DeleteAll();

        PurchaseLine.SetRange("Document Type", PurchaseHeader."Document Type");
        PurchaseLine.SetRange("Document No.", DocumentNo);
        if not PurchaseLine.IsEmpty() then
            PurchaseLine.DeleteAll();

        PurchaseHeader."Posting No." := '';
        PurchaseHeader.Delete();
        Commit();
    end;


    var
        TempFieldSet: Record 2000000041 temporary;
        Common: Codeunit "WSA Common";
}
