﻿using Microsoft.EntityFrameworkCore;
using WSA.Retail.Integration.PIM.Models.Attributes;
using WSA.Retail.Integration.PIM.Models.Brands;
using WSA.Retail.Integration.PIM.Models.BundleSets;
using WSA.Retail.Integration.PIM.Models.Categories;
using WSA.Retail.Integration.PIM.Models.Colors;
using WSA.Retail.Integration.PIM.Models.Countries;
using WSA.Retail.Integration.PIM.Models.Products;
using WSA.Retail.Integration.PIM.Models.Images;

namespace WSA.Retail.Integration.PIM.Data;

public partial class PimDbContext(DbContextOptions<PimDbContext> options) : DbContext(options)
{
    public DbSet<PimAttributeEntity> PimAttributes { get; set; }
    public DbSet<PimBrandEntity> PimBrands { get; set; }
    public DbSet<PimBundleSetEntity> PimBundleSets { get; set; }
    public DbSet<PimCategoryEntity> PimCategories { get; set; }
    public DbSet<PimColorEntity> PimColors { get; set; }
    public DbSet<PimCountryEntity> PimCountries { get; set; }
    public DbSet<PimCountryBrandEntity> PimCountryBrands { get; set; }
    public DbSet<PimImageEntity> PimImages { get; set; }
    public DbSet<PimProductEntity> PimProducts { get; set; }
    public DbSet<PimProductAttributeEntity> PimProductAttributes { get; set; }
    public DbSet<PimProductBrandEntity> PimProductBrands { get; set; }
    public DbSet<PimProductBundleSetEntity> PimProductBundleSets { get; set; }
    public DbSet<PimProductCategoryEntity> PimProductCategories { get; set; }
    public DbSet<PimProductParentEntity> PimProductParents { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.HasDefaultSchema("pim");

        modelBuilder.ApplyConfiguration(new PimAttributeConfiguration());
        modelBuilder.ApplyConfiguration(new PimBrandConfiguration());
        modelBuilder.ApplyConfiguration(new PimBundleSetConfiguration());
        modelBuilder.ApplyConfiguration(new PimCategoryConfiguration());
        modelBuilder.ApplyConfiguration(new PimColorConfiguration());
        modelBuilder.ApplyConfiguration(new PimCountryConfiguration());
        modelBuilder.ApplyConfiguration(new PimCountryBrandConfiguration());
        modelBuilder.ApplyConfiguration(new PimImageConfiguration());
        modelBuilder.ApplyConfiguration(new PimProductConfiguration());
        modelBuilder.ApplyConfiguration(new PimProductAttributeConfiguration());
        modelBuilder.ApplyConfiguration(new PimProductBrandConfiguration());
        modelBuilder.ApplyConfiguration(new PimProductBundlesetConfiguration());
        modelBuilder.ApplyConfiguration(new PimProductCategoryConfiguration());
        modelBuilder.ApplyConfiguration(new PimProductParentConfiguration());
    }
}