﻿CREATE TABLE [dbo].[SalesOrder]
(
    [Id]             UNIQUEIDENTIFIER CONSTRAINT [DF_SalesOrder_Id] DEFAULT NEWID() NOT NULL,
    [DocumentNumber] NVARCHAR(20) NOT NULL,
    [AlternateNumber]   NVARCHAR(50) NULL,
    [PatientId]      UNIQUEIDENTIFIER NOT NULL,
    [ClinicId]       UNIQUEIDENTIFIER NOT NULL,
    [DocumentDate]   DATE NULL,
    [IsActive]       BIT CONSTRAINT [DF_SalesOrder_IsActive] DEFAULT((1)) NOT NULL,
    [CreatedOn]      DATETIME2 CONSTRAINT [DF_SalesOrder_CreatedOn] DEFAULT (sysutcdatetime())  NOT NULL,
    [ModifiedOn]     DATETIME2 CONSTRAINT [DF_SalesOrder_ModifiedOn] DEFAULT (sysutcdatetime())  NOT NULL,
    CONSTRAINT       [PK_SalesOrder_Id] PRIMARY KEY CLUSTERED ([Id] ASC),
    CONSTRAINT       [FK_SalesOrder_Patient] FOREIGN KEY (PatientId) REFERENCES [dbo].[Patient](Id),
    CONSTRAINT       [FK_SalesOrder_Clinic] FOREIGN KEY (ClinicId) REFERENCES [dbo].[Clinic](Id)
)
GO

CREATE UNIQUE INDEX IX_SalesOrder_Code
ON [dbo].[SalesOrder] ([DocumentDate])
GO

CREATE TRIGGER SalesOrder_UpdateModified
ON [dbo].[SalesOrder]
AFTER UPDATE 
AS
   UPDATE [dbo].[SalesOrder]
   SET [ModifiedOn] = sysutcdatetime()
   FROM Inserted AS i
   WHERE [dbo].[SalesOrder].[Id] = i.[Id]
GO