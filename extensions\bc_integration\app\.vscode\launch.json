{"version": "0.2.0", "configurations": [{"name": "AU UAT", "request": "launch", "type": "al", "tenant": "1a41b96d-457d-41ac-94ef-22d1901a7556", "environmentType": "Sandbox", "environmentName": "au_uat", "breakOnError": "ExcludeTry", "launchBrowser": true, "enableLongRunningSqlStatements": true, "enableSqlInformationDebugger": true, "schemaUpdateMode": "ForceSync"}, {"name": "AU UAT3", "request": "launch", "type": "al", "tenant": "1a41b96d-457d-41ac-94ef-22d1901a7556", "environmentType": "Sandbox", "environmentName": "au_uat3", "breakOnError": "ExcludeTry", "launchBrowser": true, "enableLongRunningSqlStatements": true, "enableSqlInformationDebugger": true, "schemaUpdateMode": "ForceSync"}, {"name": "DK DEV", "request": "launch", "type": "al", "tenant": "1a41b96d-457d-41ac-94ef-22d1901a7556", "environmentType": "Sandbox", "environmentName": "dk_dev", "breakOnError": "ExcludeTry", "launchBrowser": true, "enableLongRunningSqlStatements": true, "enableSqlInformationDebugger": true, "schemaUpdateMode": "ForceSync"}, {"name": "FR DEV", "request": "launch", "type": "al", "tenant": "1a41b96d-457d-41ac-94ef-22d1901a7556", "environmentType": "Sandbox", "environmentName": "fr_dev", "breakOnError": "ExcludeTry", "launchBrowser": true, "enableLongRunningSqlStatements": true, "enableSqlInformationDebugger": true, "schemaUpdateMode": "ForceSync"}, {"name": "FR UAT", "request": "launch", "type": "al", "tenant": "1a41b96d-457d-41ac-94ef-22d1901a7556", "environmentType": "Sandbox", "environmentName": "fr_uat", "breakOnError": "ExcludeTry", "launchBrowser": true, "enableLongRunningSqlStatements": true, "enableSqlInformationDebugger": true, "schemaUpdateMode": "ForceSync"}, {"name": "NZ UAT3", "request": "launch", "type": "al", "tenant": "1a41b96d-457d-41ac-94ef-22d1901a7556", "environmentType": "Sandbox", "environmentName": "nz_uat3", "breakOnError": "ExcludeTry", "launchBrowser": true, "enableLongRunningSqlStatements": true, "enableSqlInformationDebugger": true, "schemaUpdateMode": "ForceSync"}, {"name": "US TEST", "request": "launch", "type": "al", "tenant": "1a41b96d-457d-41ac-94ef-22d1901a7556", "environmentType": "Sandbox", "environmentName": "us_test", "breakOnError": "ExcludeTry", "launchBrowser": true, "enableLongRunningSqlStatements": true, "enableSqlInformationDebugger": true, "schemaUpdateMode": "ForceSync"}, {"name": "US SNAP", "request": "snapshotInitialize", "type": "al", "tenant": "1a41b96d-457d-41ac-94ef-22d1901a7556", "environmentType": "Production", "environmentName": "us_prod", "userId": "02f5d3df-27cc-4166-9507-2c4ab7c3ecc9", "sessionId": 1581800, "snapshotVerbosity": "Full"}, {"name": "W1 Sandbox", "request": "launch", "type": "al", "environmentType": "OnPrem", "server": "http://w1sandbox", "serverInstance": "BC", "authentication": "UserPassword", "breakOnError": "ExcludeTry", "launchBrowser": true, "enableLongRunningSqlStatements": true, "enableSqlInformationDebugger": true, "tenant": "default", "usePublicURLFromServer": true}]}