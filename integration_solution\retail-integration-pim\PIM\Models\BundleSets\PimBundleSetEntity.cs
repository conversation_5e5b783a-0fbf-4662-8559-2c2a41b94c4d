﻿using WSA.Retail.Integration.PIM.Models.Products;

namespace WSA.Retail.Integration.PIM.Models.BundleSets;

public class PimBundleSetEntity : IPimBundleSetEntity, WSA.Retail.Integration.Core.IIdentifiable
{
    public required Guid Id { get; set; } = Guid.Empty;
    public required string? Name { get; set; }
    public string? Type { get; set; }
    public bool? IsDefault { get; set; }
    public bool? IsMandatory { get; set; }
    public bool? IsMasterData { get; set; }
    public int? Ranking { get; set; }
    public string? State { get; set; }
    public DateTime? CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
    public required DateTime SystemCreatedOn { get; set; }
    public required DateTime SystemModifiedOn { get; set; }

    public virtual ICollection<PimProductEntity> Products { get; set; } = [];
}
