﻿CREATE TABLE [dbo].[Manufacturer]
(
    [Id]                UNIQUEIDENTIFIER CONSTRAINT [DF_Manufacturer_Id] DEFAULT NEWID() NOT NULL,
    [Code]              NVARCHAR(20) NOT NULL,
    [Name]              NVARCHAR(100) NULL,
    [AlternateCode]     NVARCHAR(50) NULL,
    [CreatedOn]         DATETIME2 CONSTRAINT [DF_Manufacturer_CreatedOn] DEFAULT (sysutcdatetime()) NOT NULL,
    [ModifiedOn]        DATETIME2 CONSTRAINT [DF_Manufacturer_ModifiedOn] DEFAULT (sysutcdatetime()) NOT NULL,
    CONSTRAINT          [PK_Manufacturer_Id] PRIMARY KEY CLUSTERED ([Id] ASC)
)
GO

CREATE INDEX IX_Manufacturer_Code
ON [dbo].[Manufacturer] ([Code])
GO

CREATE INDEX IX_Manufacturer_Name
ON [dbo].[Manufacturer] ([Name])
GO