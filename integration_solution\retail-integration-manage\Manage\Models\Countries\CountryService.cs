﻿using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using WSA.Retail.Integration.Logging;
using WSA.Retail.Integration.Manage.API;
using WSA.Retail.Integration.Manage.Configuration;

namespace WSA.Retail.Integration.Manage.Models.Countries;

public class CountryService(
    ILogger<CountryService> logger,
    ManageAPI manageApi
    ) : ICountryService
{
    private readonly ILogger<CountryService> _logger = logger;
    private readonly ManageAPI _manageApi = manageApi;

    public async Task<string?> GetCountryCodeFromManageAsync(Guid countryId)
    {
        _logger.LogMethodStart();

        try
        {
            var countries = await _manageApi.CountriesAsync();
            var country = countries?.Data.Where(c => c.Id == countryId).FirstOrDefault();
            if (country != null)
            {
                return country.Code;
            }
        }
        catch (Exception ex)
        {
            _logger.LogCustomError(ex);
        }
        return null;
    }
}