﻿using Microsoft.Extensions.Options;
using System.Text.Json;
using WSA.Retail.Integration.Core;
using WSA.Retail.Integration.Events;
using WSA.Retail.Integration.Manage.Configuration;
using WSA.Retail.Integration.Models.Configuration;
using WSA.Retail.Integration.Models.Couplings;
using WSA.Retail.Integration.Utilities;

namespace WSA.Retail.Integration.Manage.Core;

public abstract class GenericToEventHandler<
    TEntity,
    TEntitySubscriberService,
    TManageIntegrator,
    TResponseEntity>(
        IOptions<AppSettings> appSettings,
        IEntitySubscriberService entitySubscriberService,
        ICouplingService couplingService,
        TManageIntegrator manageIntegrator,
        EntityType entityType)
    where TEntity : IMasterData
    where TEntitySubscriberService : class
    where TManageIntegrator : IGenericManageIntegrator<TEntity, TResponseEntity>
    where TResponseEntity : class
{
    protected readonly AppSettings _appSettings = appSettings.Value;
    protected readonly IEntitySubscriberService _entitySubscriberService = entitySubscriberService;
    protected readonly ICouplingService _couplingService = couplingService;
    protected readonly TManageIntegrator _manageIntegrator = manageIntegrator;
    protected readonly EntityType _entityType = entityType;

    public async Task<bool> HandleToQueueAsync(Event ev)
    {
        LogMethodStart(callingMethod: nameof(HandleToQueueAsync));

        if (ev.Source == _appSettings.ExternalSystemCode)
        {
            return true;  // Originated in Manage.  Skip recursive update.
        }

        if (ev.Data != null)
        {
            var json = JsonSerializer.Serialize(ev.Data);
            var domainEntity = JsonSerializer.Deserialize<TEntity>(json, Common.GetJsonOptions());


            // Validate entity
            if (domainEntity == null) return false;
            if (domainEntity.Id == Guid.Empty)
                throw new ArgumentOutOfRangeException($"Id for {typeof(TEntity).Name} {domainEntity.Name} is not valid.");

            if (string.IsNullOrWhiteSpace(domainEntity.Code))
                throw new ArgumentOutOfRangeException($"Code for {typeof(TEntity).Name} {domainEntity.Name} is not valid.");

            if (string.IsNullOrWhiteSpace(domainEntity.Name))
                throw new ArgumentOutOfRangeException($"Name for {typeof(TEntity).Name} {domainEntity.Name} is not valid.");


            // Hook for custom validation
            AfterValidateEventEntityProperties(
                domainEntity,
                out bool AfterValidateEventEntityPropertiesOk,
                out bool AfterValidateEventEntityPropertiesIsHandled);

            if (AfterValidateEventEntityPropertiesIsHandled) return AfterValidateEventEntityPropertiesOk;

            if (!AfterValidateEventEntityPropertiesOk) return false;


            // Get subscriber
            var subscriber = await _entitySubscriberService.GetAsync(_appSettings.ExternalSystemCode, _entityType.GetEntityCode());
            if (subscriber == null)
            {
                LogCustomError(
                    message: $"Unable to retrieve Entity Subscriber record for ExternalSystemCode:{_appSettings.ExternalSystemCode}, "
                        + $"EntityCode:{_entityType.GetEntityCode()}", 
                    callingMethod: nameof(HandleToQueueAsync));
                return false;
            }

            if (subscriber.ToExternalSystem ?? false)  // Manage can receive updates - send to Manage
            {
                BeforeUpdateManageAsync(
                    domainEntity, 
                    out bool BeforeUpdateManageAsyncOk,
                    out bool BeforeUpdateManageAsyncIsHandled);

                if (BeforeUpdateManageAsyncIsHandled) return BeforeUpdateManageAsyncOk;

                if (!BeforeUpdateManageAsyncOk) return false;

                var responseObject = await _manageIntegrator.UpdateManageAsync(domainEntity);

                return responseObject != null;
            }

            if (!(subscriber.ToExternalSystem ?? false)) // Manage cannot receive updates -- do nothing
            {
                return true;
            }
        }

        return false;
    }
    
    protected virtual void AfterValidateEventEntityProperties(TEntity entity, out bool ok, out bool isHandled)
    {
        ok = true;
        isHandled = false;
        return;
    }

    protected virtual void BeforeUpdateManageAsync(TEntity entity, out bool ok, out bool isHandled)
    {
        ok = true;
        isHandled = false;
        return;
    }

    protected virtual Task AfterConvertToEntity(TEntity entity)
    {
        return Task.CompletedTask;
    }


    protected virtual void LogMethodStart(string? callingMethod = null) { }
    protected virtual void LogCustomInformation(string message, string? callingMethod = null) { }
    protected virtual void LogCustomWarning(string message, string? callingMethod = null) { }
    protected virtual void LogCustomError(Exception ex, string message, string? callingMethod = null) { }
    protected virtual void LogCustomError(Exception ex, string? callingMethod = null) { }
    protected virtual void LogCustomError(string message, string? callingMethod = null) { }
}