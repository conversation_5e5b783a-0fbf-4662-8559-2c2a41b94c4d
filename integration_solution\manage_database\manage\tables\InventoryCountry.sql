CREATE TABLE [manage].[InventoryCountry]
(
    [Id]                UNIQUEIDENTIFIER CONSTRAINT [DF_InventoryCountry_Id] DEFAULT NEWID() NOT NULL,
    [Name]              NVARCHAR(100) NOT NULL,
    [Code]              NVARCHAR(20) NOT NULL,
    [CreatedOn]         DATETIME2 CONSTRAINT [DF_InventoryCountry_CreatedOn] DEFAULT (sysutcdatetime()) NOT NULL,
    [ModifiedOn]        DATETIME2 CONSTRAINT [DF_InventoryCountry_ModifiedOn] DEFAULT (sysutcdatetime()) NOT NULL,
    CONSTRAINT          [PK_InventoryCountry_Id] PRIMARY KEY CLUSTERED ([Id] ASC)
)
GO

CREATE UNIQUE INDEX IX_InventoryCountry_Name
ON [manage].[InventoryCountry] ([Name])
GO
