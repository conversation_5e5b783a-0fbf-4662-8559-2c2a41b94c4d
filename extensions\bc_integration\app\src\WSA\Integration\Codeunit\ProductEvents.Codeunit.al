namespace WSA.Integration;

using Microsoft.Inventory.Item;
using Microsoft.Purchases.Vendor;
using Microsoft.Inventory.Item.Catalog;
using Microsoft.Finance.VAT.Setup;

codeunit 50108 "Product Events"
{
    TableNo = Item;

    trigger OnRun()
    begin
        RaiseEvent(Rec);
    end;


    [EventSubscriber(ObjectType::Table, Database::Item, OnAfterInsertEvent, '', true, true)]
    local procedure SendEventOnAfterInsertEvent(
        var Rec: Record Item;
        RunTrigger: Boolean)

    begin
        if Rec.IsTemporary then
            exit;

        if RunTrigger then
            RaiseEventAsync(Rec);
    end;


    [EventSubscriber(ObjectType::Table, Database::Item, OnAfterModifyEvent, '', true, true)]
    local procedure SendEventOnAfterModifyEvent(
        var Rec: Record Item;
        var xRec: Record Item;
        RunTrigger: Boolean)

    begin
        if Rec.IsTemporary then
            exit;

        if RunTrigger then
            if HasChanged(Rec, xRec) then
                RaiseEventAsync(Rec);
    end;


    local procedure RaiseEventAsync(Item: Record Item)
    var
        SessionId: Integer;

    begin
        Session.StartSession(SessionId, Codeunit::"Product Events", CompanyName, Item);
    end;

    local procedure RaiseEvent(Item: Record Item)
    var
        RetailIntegrationSetup: Record "Retail Integration Setup";
        IntegrationManagement: Codeunit "Integration Management";
        Common: Codeunit "WSA Common";
        JObject: JsonObject;

    begin
        RetailIntegrationSetup.SafeGet();
        if not RetailIntegrationSetup.Enabled then
            exit;

        if Item."No." = '' then
            exit;

        if Item.Description = '' then
            exit;

        if IsNullGuid(Item.SystemId) then
            exit;

        JObject := Common.ProductToJson(Item);

        if not IntegrationManagement.TryRaiseEvent(JObject, 'products', Format(Item.SystemId).TrimStart('{').TrimEnd('}')) then
            exit;
    end;


    local procedure HasChanged(
         Rec: Record "Item";
         xRec: Record "Item"): Boolean

    begin
        if xRec."No." = '' then
            exit(false);

        if Rec.Description <> xRec.Description then
            exit(true);

        if Rec."Item Category Code" <> xRec."Item Category Code" then
            exit(true);

        if Rec."WSA PIM Product No." <> xRec."WSA PIM Product No." then
            exit(true);

        if Rec."Vendor No." <> xRec."Vendor No." then
            exit(true);

        if Rec."Manufacturer Code" <> xRec."Manufacturer Code" then
            exit(true);

        if Rec."WSA Color" <> xRec."WSA Color" then
            exit(true);

        if Rec."VAT Prod. Posting Group" <> xRec."VAT Prod. Posting Group" then
            exit(true);

        if Rec."Vendor Item No." <> xRec."Vendor Item No." then
            exit(true);

        if Rec."GTIN" <> xRec."GTIN" then
            exit(true);

        if Rec."WSA Image URI" <> xRec."WSA Image URI" then
            exit(true);
    end;
}
