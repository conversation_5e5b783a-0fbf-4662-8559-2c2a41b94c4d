﻿CREATE TABLE [dbo].[TaxGroup]
(
    [Id]                UNIQUEIDENTIFIER CONSTRAINT [DF_TaxGroup_Id] DEFAULT NEWID() NOT NULL,
    [Code]              NVARCHAR(20) NOT NULL,
    [Name]              NVARCHAR(100) NULL,
    [AlternateCode]     NVARCHAR(50) NULL,
    [Rate]              MONEY NULL,
    [CreatedOn]         DATETIME2 CONSTRAINT [DF_TaxGroup_CreatedOn] DEFAULT (sysutcdatetime()) NOT NULL,
    [ModifiedOn]        DATETIME2 CONSTRAINT [DF_TaxGroup_ModifiedOn] DEFAULT (sysutcdatetime()) NOT NULL,
    CONSTRAINT          [PK_TaxGroup_Id] PRIMARY KEY CLUSTERED ([Id] ASC)
)
GO

CREATE UNIQUE INDEX IX_TaxGroup_Code
ON [dbo].[TaxGroup] ([Code])
GO

CREATE INDEX IX_TaxGroup_Name
ON [dbo].[TaxGroup] ([Name])
GO