namespace WSA.Integration.API.V1;

using Microsoft.Purchases.Document;
using Microsoft.Purchases.History;
using Microsoft.Purchases.Posting;
using Microsoft.Inventory.Tracking;
using System.Security.User;

using WSA.Integration;

codeunit 50109 "WSA Purchase Receipts Handler" implements "WSA Integration Request"
{
    TableNo = "WSA Integration Request Log";

    trigger OnRun()
    begin
        Code(Rec);
    end;


    procedure HandleRequest(var Request: Record "WSA Integration Request Log")
    begin
        if not Codeunit.Run(Codeunit::"WSA Purchase Receipts Handler", Request) then begin
            Common.SetErrorResponse(Request, '');
        end;
    end;


    local procedure Code(var Request: Record "WSA Integration Request Log")
    var
        json: JsonObject;

    begin
        case Request.Method of
            Request.Method::post:
                HandlePost(Request);
        end;
    end;


    local procedure HandlePost(var Request: Record "WSA Integration Request Log")
    var
        purchaseOrder: Record "Purchase Header";
        purchaseLine: Record "Purchase Line";
        reservationEntry: Record "Reservation Entry";
        json: JsonObject;

    begin
        if not TryHandlePost(Request, purchaseOrder) then begin
            json := Common.GetJsonFromBlob(Request);
            CleanupAfterFailure(Common.GetJsonValue(json, '$.documentNumber').AsCode());
            Common.SetErrorResponse(Request, '');
        end;

    end;


    [TryFunction]
    local procedure TryHandlePost(
        var Request: Record "WSA Integration Request Log";
        var PurchaseOrder: Record "Purchase Header")

    var
        PurchRcptHeader: Record "Purch. Rcpt. Header";
        json: JsonObject;

    begin
        json := Common.GetJsonFromBlob(Request);

        if not PreValidatePurchaseReceipt(json, Request) then begin
            CleanupAfterFailure(Common.GetJsonValue(json, '$.documentNumber').AsCode());
            Common.SetErrorResponse(Request, 'Purchase order not valid for receipt');
            exit;
        end;

        if not GetPurchaseHeader(json, PurchaseOrder) then begin
            CleanupAfterFailure(Common.GetJsonValue(json, '$.documentNumber').AsCode());
            Common.SetErrorResponse(Request, 'Purchase order not found');
            exit;
        end;

        if PurchaseOrder.Status <> PurchaseOrder.Status::Open then begin
            PurchaseOrder.Status := PurchaseOrder.Status::Open;
            PurchaseOrder.Modify();
        end;

        UpdateReceivingNo(json, PurchaseOrder);
        UpdateVendorOrderNo(json, PurchaseOrder);
        UpdateDates(json, PurchaseOrder);
        UpdateLines(json, PurchaseOrder);

        if PurchaseOrder.Status <> PurchaseOrder.Status::Released then begin
            PurchaseOrder.Status := PurchaseOrder.Status::Released;
            PurchaseOrder.Modify();
        end;

        if PostReceipt(PurchaseOrder, PurchRcptHeader) then begin
            Request."Purchase Order No." := PurchaseOrder."No.";
            Request."Purchase Receipt No." := PurchRcptHeader."No.";
            Request.Modify();
            Common.SetCreatedResponse(Request, Common.PurchaseReceiptToJson(PurchRcptHeader))
        end else begin
            CleanupAfterFailure(Common.GetJsonValue(json, '$.documentNumber').AsCode());
            Common.SetErrorResponse(Request, '');
        end;
    end;


    local procedure PreValidatePurchaseReceipt(
        Json: JsonObject;
        var Request: Record "WSA Integration Request Log") Ok: boolean

    var
        PurchRcptHeader: Record "Purch. Rcpt. Header";
        UserSetupManagement: Codeunit "User Setup Management";
        DocumentNumber: Code[20];
        DocumentDate: Date;

    begin
        DocumentNumber := Common.GetJsonValue(Json, '$.documentNumber').AsCode();
        if DocumentNumber = '' then begin
            Common.SetErrorResponse(Request, 'Document number not found in JSON');
            exit(false);
        end;

        if PurchRcptHeader.Get(DocumentNumber) then begin
            Common.SetErrorResponse(Request, 'Duplicate receipt');
            exit(false);
        end;

        DocumentDate := Common.GetJsonValue(Json, '$.documentDate').AsDate();
        if DocumentDate = 0D then begin
            Common.SetErrorResponse(Request, 'Document date not found in JSON');
            exit(false);
        end;

        if not (UserSetupManagement.IsPostingDateValid(DocumentDate)) then begin
            Common.SetErrorResponse(Request, 'Posting date not allowed');
            exit(false);
        end;

        exit(true);
    end;

    local procedure GetPurchaseHeader(
        Json: JsonObject;
        var PurchaseHeader: Record "Purchase Header") Ok: boolean

    var
        PurchaseLine: Record "Purchase Line";
        IntegrationManagement: Codeunit "Integration Management";
        jValue: JsonValue;
        vendorNo: Code[20];
        clinicCode: Code[10];

    begin
        jValue := Common.GetJsonValue(Json, '$.purchaseOrder.documentNumber');
        if not jValue.IsNull() then begin
            if (PurchaseHeader.Get(PurchaseHeader."Document Type"::Order, jValue.AsCode())) then
                exit(true);

            if (IntegrationManagement.GetPurchaseOrder(jValue.AsCode(), PurchaseHeader)) then
                exit(true);

            exit(false);
        end;

        jValue := Common.GetJsonValue(Json, '$.vendor.code');
        if jValue.IsNull() then
            exit(false)
        else
            vendorNo := jValue.AsCode();

        jValue := Common.GetJsonValue(Json, '$.clinic.code');
        if jValue.IsNull() then
            exit(false)
        else
            clinicCode := jValue.AsCode();

        if (PurchaseHeader.Get(PurchaseHeader."Document Type"::Order, Common.GetJsonValue(Json, '$.documentNumber').AsCode())) then begin
            if PurchaseHeader."Buy-from Vendor No." <> vendorNo then
                PurchaseHeader.Validate("Buy-from Vendor No.", vendorNo);
            if PurchaseHeader."Responsibility Center" <> clinicCode then
                PurchaseHeader.Validate("Responsibility Center", clinicCode);
            exit(true);
        end;

        if CreatePurchaseOrder(Json, PurchaseHeader) then
            exit(true);

        exit(false);
    end;


    local procedure CreatePurchaseOrder(
        Json: JsonObject;
        var PurchaseHeader: Record "Purchase Header") Ok: boolean

    var
        PurchaseLine: Record "Purchase Line";
        RecRef: RecordRef;
        lines: JsonToken;
        line: JsonToken;

    begin
        PurchaseHeader.Init();
        PurchaseHeader."Document Type" := PurchaseHeader."Document Type"::Order;
        PurchaseHeader."No." := Common.GetJsonValue(Json, '$.documentNumber').AsCode();
        PurchaseHeader.Validate("Buy-from Vendor No.", Common.GetJsonValue(Json, '$.vendor.code').AsCode());
        PurchaseHeader.Validate("Responsibility Center", Common.GetJsonValue(Json, '$.clinic.code').AsCode());
        PurchaseHeader.Insert();

        RecRef.GetTable(PurchaseHeader);
        Common.ValidateFieldFromJson(json, '$.alternateNumber', recRef, PurchaseHeader.FieldNo("Vendor Order No."), TempFieldSet);
        Common.ValidateFieldFromJson(json, '$.documentDate', recRef, PurchaseHeader.FieldNo("Posting Date"), TempFieldSet);
        Common.ValidateFieldFromJson(json, '$.documentDate', recRef, PurchaseHeader.FieldNo("Document Date"), TempFieldSet);
        RecRef.SetTable(PurchaseHeader);
        PurchaseHeader.Modify();

        PurchaseLine.SetRange("Document Type", PurchaseHeader."Document Type");
        PurchaseLine.SetRange("Document No.", PurchaseHeader."No.");
        if not PurchaseLine.IsEmpty() then
            PurchaseLine.DeleteAll(false);

        if json.SelectToken('$.purchaseReceiptLines', lines) then begin
            if lines.IsArray then begin
                foreach line in lines.AsArray() do begin
                    if not purchaseLine.Get(PurchaseHeader."Document Type", PurchaseHeader."No.",
                            Common.GetJsonValue(line.AsObject(), '$.sequence').AsInteger()) then begin
                        purchaseLine.Init();
                        purchaseLine."Document Type" := PurchaseHeader."Document Type";
                        purchaseLine."Document No." := PurchaseHeader."No.";
                        purchaseLine."Line No." := Common.GetJsonValue(line.AsObject(), '$.sequence').AsInteger();
                        purchaseLine.Insert();
                    end;

                    if (purchaseLine.Type <> purchaseLine.Type::Item) then
                        purchaseLine.Validate(Type, purchaseLine.Type::Item);
                    recRef.GetTable(purchaseLine);
                    Common.ValidateFieldFromJson(line.AsObject(), '$.product.code', recRef, purchaseLine.FieldNo("No."), TempFieldSet);
                    Common.ValidateFieldFromJson(line.AsObject(), '$.description', recRef, purchaseLine.FieldNo(Description), TempFieldSet);
                    Common.ValidateFieldFromJson(line.AsObject(), '$.quantity', recRef, purchaseLine.FieldNo(Quantity), TempFieldSet);
                    recRef.SetTable(purchaseLine);

                    purchaseLine.Validate("Qty. to Receive", 0);
                    purchaseLine.Validate("Qty. to Invoice", 0);
                    purchaseLine.Modify();
                end;
            end;
            exit(true);
        end;
    end;


    local procedure UpdateReceivingNo(
        Json: JsonObject;
        var PurchaseHeader: Record "Purchase Header")

    var
        newReceivingNo: Code[20];

    begin
        newReceivingNo := Common.GetJsonValue(Json, '$.documentNumber').AsCode();
        if newReceivingNo <> '' then
            PurchaseHeader."Receiving No." := newReceivingNo;
    end;

    local procedure UpdateVendorOrderNo(
        Json: JsonObject;
        var PurchaseHeader: Record "Purchase Header")

    var
        jvalue: JsonValue;
        newVendorOrderNo: Code[35];

    begin
        jvalue := Common.GetJsonValue(Json, '$.alternateNumber');
        if not jvalue.IsNull() then
            PurchaseHeader.Validate("Vendor Order No.", jvalue.AsCode());
    end;


    local procedure UpdateDates(
        Json: JsonObject;
        var PurchaseHeader: Record "Purchase Header")

    var
        newDate: Date;

    begin
        newDate := Common.GetJsonValue(Json, '$.documentDate').AsDate();
        if newDate <> 0D then begin
            PurchaseHeader.Validate("Posting Date", newDate);
            PurchaseHeader.Validate("Document Date", newDate);
        end;
    end;


    local procedure UpdateLines(
        Json: JsonObject;
        var PurchaseHeader: Record "Purchase Header")

    var
        PurchaseLine: Record "Purchase Line";
        jToken: JsonToken;
        lineToken: JsonToken;

    begin
        PurchaseLine.SetRange("Document Type", PurchaseHeader."Document Type");
        PurchaseLine.SetRange("Document No.", PurchaseHeader."No.");
        PurchaseLine.ModifyAll("Qty. to Receive", 0);

        Json.SelectToken('$.purchaseReceiptLines', jToken);
        foreach lineToken in jToken.AsArray do begin
            UpdateLine(lineToken.AsObject(), PurchaseHeader);
        end;
    end;


    local procedure UpdateLine(
        Json: JsonObject;
        var PurchaseHeader: Record "Purchase Header")

    var
        purchaseLine: Record "Purchase Line";
        quantity: Decimal;
        qtyToReceive: Decimal;
        qtyToInvoice: Decimal;
        serialNumberValue: JsonValue;
        serialNumber: Code[50];
        newSerialNumberList: Code[1024];
        oldSerialNumberList: List of [Text];

    begin
        if GetPurchaseLine(Json, PurchaseHeader, purchaseLine) then begin

            if purchaseLine."Outstanding Quantity" <> purchaseLine.Quantity - purchaseLine."Quantity Received" then
                purchaseLine."Outstanding Quantity" := purchaseLine.Quantity - purchaseLine."Quantity Received";

            if (purchaseLine.Quantity = purchaseLine."Quantity (Base)") then begin
                if purchaseLine."Qty. Received (Base)" <> purchaseLine."Quantity Received" then
                    purchaseLine."Qty. Received (Base)" := purchaseLine."Quantity Received";

                if purchaseLine."Outstanding Qty. (Base)" <> purchaseLine."Outstanding Quantity" then
                    purchaseLine."Outstanding Qty. (Base)" := purchaseLine."Outstanding Quantity";
            end;

            quantity := Common.GetJsonValue(Json, '$.quantity').AsDecimal();
            if purchaseLine."Quantity Received" + quantity > purchaseLine."Quantity" then begin
                qtyToReceive := purchaseLine."Qty. to Receive";
                qtyToInvoice := purchaseLine."Qty. to Invoice";
                purchaseLine.Validate("Quantity", purchaseLine."Quantity Received" + quantity);
                purchaseLine.Validate("Qty. to Receive", qtyToReceive);
                purchaseLine.Validate("Qty. to Invoice", qtyToInvoice);
            end;

            purchaseLine.Validate("Qty. to Receive", purchaseLine."Qty. to Receive" +
                Common.GetJsonValue(Json, '$.quantity').AsDecimal());

            Clear(serialNumber);
            serialNumberValue := Common.GetJsonValue(Json, '$.serialNumber');
            if not serialNumberValue.IsNull() then
                serialNumber := serialNumberValue.AsText();
            if serialNumber <> '' then
                purchaseLine.Validate("WSA Serial No.", serialNumber);
            purchaseLine.Modify();
        end;
    end;


    local procedure GetPurchaseLine(
        Json: JsonObject;
        var PurchaseHeader: Record "Purchase Header";
        var PurchaseLine: Record "Purchase Line") Ok: Boolean

    var
        jValue: JsonValue;
        recRef: RecordRef;

    begin
        jValue := Common.GetJsonValue(Json, '$.purchaseOrderLine.sequence');
        if not jValue.IsNull() then
            exit(PurchaseLine.Get(
                PurchaseHeader."Document Type",
                PurchaseHeader."No.",
                jValue.AsInteger()));

        jValue := Common.GetJsonValue(Json, '$.sequence');
        if not jValue.IsNull() then
            exit(PurchaseLine.Get(
                PurchaseHeader."Document Type",
                PurchaseHeader."No.",
                jValue.AsInteger()));

        exit(false);
    end;


    local procedure PostReceipt(
        PurchaseHeader: Record "Purchase Header";
        var PurchRcptHeader: Record "Purch. Rcpt. Header") Ok: Boolean

    var
        PurchaseLine: Record "Purchase Line";
        OrderNo: Code[20];
        OrderNoSeries: Code[20];

    begin
        ClearLastError();
        OrderNo := PurchaseHeader."No.";
        OrderNoSeries := PurchaseHeader."No. Series";

        PurchaseHeader.Receive := true;
        PurchaseHeader.Invoice := false;

        Commit;
        if PurchaseHeader.SendToPosting(Codeunit::"Purch.-Post") then begin
            Commit();
            PurchaseLine.SetRange("Document Type", PurchaseHeader."Document Type");
            PurchaseLine.SetRange("Document No.", PurchaseHeader."No.");
            if not PurchaseLine.IsEmpty() then
                PurchaseLine.ModifyAll("Qty. to Receive", 0);

            PurchRcptHeader.SetCurrentKey("Order No.");
            PurchRcptHeader.SetRange("Order No.", OrderNo);
            PurchRcptHeader.SetRange("Order No. Series", OrderNoSeries);
            exit(PurchRcptHeader.FindLast());
        end else
            exit(false);

    end;

    local procedure CleanupAfterFailure(DocumentNo: Code[20])
    var
        PurchaseHeader: Record "Purchase Header";
        ReservationEntry: Record "Reservation Entry";
        PurchaseLine: Record "Purchase Line";

    begin
        if not PurchaseHeader.Get(PurchaseHeader."Document Type"::Order, DocumentNo) then
            exit;

        ReservationEntry.SetRange("Source Type", Database::"Purchase Line");
        ReservationEntry.SetRange("Source Subtype", PurchaseHeader."Document Type".AsInteger());
        ReservationEntry.SetRange("Source ID", DocumentNo);
        if not ReservationEntry.IsEmpty() then
            ReservationEntry.DeleteAll();

        PurchaseLine.SetRange("Document Type", PurchaseHeader."Document Type");
        PurchaseLine.SetRange("Document No.", DocumentNo);
        if not PurchaseLine.IsEmpty() then
            PurchaseLine.DeleteAll();

        PurchaseHeader."Posting No." := '';
        PurchaseHeader.Delete();
        Commit();
    end;


    var
        TempFieldSet: Record 2000000041 temporary;
        Common: Codeunit "WSA Common";
}
