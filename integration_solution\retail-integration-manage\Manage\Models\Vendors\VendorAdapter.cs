﻿using WSA.Retail.Integration.Manage.API;
using WSA.Retail.Integration.Manage.Core;
using WSA.Retail.Integration.Manage.EventProcessing;
using WSA.Retail.Integration.Models.Vendors;

namespace WSA.Retail.Integration.Manage.Models.Vendors;

public class VendorAdapter : 
    IEventHubEntityAdapter<VendorEventHubEvent, Vendor>, 
    IManageRequestAdapter<AuSupplierRequest, Vendor>,
    IManageResponseAdapter<AuSupplierResponse, Vendor>
{
    public Vendor ToDomainModel(
        VendorEventHubEvent source,
        string externalSystemCode)

    {
        string? payorCode = source.FaxNumber;
        if (string.IsNullOrWhiteSpace(payorCode))
        {
            payorCode = source.Name;
            if (payorCode?.Length > 20) payorCode = payorCode[0..20];
        }
        if (string.IsNullOrWhiteSpace(payorCode))
        {
            throw new ArgumentNullException(nameof(source),
                $"{source.GetType().Name} {nameof(source.Id)}: {source.Id} " +
                $"contains a null or empty code.");
        }

        return new Vendor()
        {
            ExternalSystemCode = externalSystemCode,
            ExternalCode = source.Id?.ToString(),
            Code = payorCode,
            Name = source.Name,
            Address = source.Address1,
            Address2 = source.Address2,
            City = source.City,
            Region = source.State,
            PostalCode = source.PostalCode,
            Phone = source.PhoneNumber,
            IntegrateWithPOS = true
        };
    }

    public Vendor ToDomainModel(
        AuSupplierResponse source,
        string externalSystemCode)
    {
        return new Vendor()
        {
            ExternalSystemCode = externalSystemCode,
            ExternalCode = source.Id.ToString(),
            Code = source.FaxNumber,
            Name = source.Name,
            Address = source.Address1,
            Address2 = source.Address2,
            City = source.City,
            //Region = source.,
            Country = source.Country,
            PostalCode = source.PostalCode,
            Phone = source.PhoneNumber,
            IntegrateWithPOS = true
        };
    }

    public AuSupplierRequest ToManageRequest(Vendor source)
    {
        return new AuSupplierRequest
        {
            Name = source.Name,
            Address1 = source.Address,
            Address2 = source.Address2,
            City = source.City,
            //State = source.Region,
            Country = source.Country,
            PostalCode = source.PostalCode,
            EmailAddress = source.Email,
            PhoneNumber = source.Phone,
            FaxNumber = source.Code,
            IsActive = true
        };
    }
}