﻿CREATE TABLE [pim].[Attribute]
(
    [Id]                    UNIQUEIDENTIFIER CONSTRAINT [DF_Attribute_Id] DEFAULT NEWID() NOT NULL,
    [Code]                  NVARCHAR(30) NOT NULL,
    [Name]                  NVARCHAR(100) NULL,
    [DataType]              NVARCHAR(100) NULL,
    [SystemIsActive]        BIT CONSTRAINT [DF_Attribute_SystemIsActive] DEFAULT((0)) NOT NULL,
    [SystemCreatedOn]       DATETIME2 CONSTRAINT [DF_Attribute_SystemCreatedOn] DEFAULT (sysutcdatetime()) NOT NULL,
    [SystemModifiedOn]      DATETIME2 CONSTRAINT [DF_Attribute_SystemModifiedOn] DEFAULT (sysutcdatetime()) NOT NULL,
    CONSTRAINT              [PK_Attribute_Id] PRIMARY KEY CLUSTERED ([Id] ASC)
)
GO

CREATE UNIQUE INDEX IX_Attribute_Code
ON [pim].[Attribute] ([Code])
GO