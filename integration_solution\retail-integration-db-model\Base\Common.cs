﻿using Azure.Storage.Blobs;
using Microsoft.AspNetCore.Http;
using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Primitives;
using System.Data;
using System.Diagnostics.Metrics;
using System.Net;
using System.Numerics;
using System.Text;
using System.Text.Json;
using System.Text.Json.Nodes;
using System.Text.Json.Serialization;


namespace WSA.Retail.Integration.Base;

public class Common
{
    public static string SerializeErrorResponse(System.Enum errorCode, string errorMessage)
    {
        var obj = ErrorResponseObject(errorCode, errorMessage);
        String jString = JsonSerializer.Serialize(obj);
        ArgumentNullException.ThrowIfNull(jString);
        return jString;
    }

    public static Object ErrorResponseObject(System.Enum errorCode, string errorMessage)
    {
        var obj = new
        {
            error = new[]
            {
                new
                {
                    code = errorCode.ToString(),
                    message = errorMessage
                }
            }
        };
        ArgumentNullException.ThrowIfNull(obj);
        return obj;
    }

    public static ValueTask<T?> GetRawHttpRequest<T>(
        HttpRequestData requestData,
        ref string? rawRequest,
        JsonSerializerOptions jsonOptions,
        ref RequestResponse reqRes)

    {
        ArgumentNullException.ThrowIfNull(requestData);

        reqRes = new()
        {
            Id = requestData.FunctionContext.TraceContext.TraceParent,
            Method = requestData.Method,
            Url = requestData.Url.OriginalString
        };

        rawRequest = requestData.Method + ' ' + requestData.Url.OriginalString;
        foreach (var header in requestData.Headers)
        {
            rawRequest = rawRequest + "\r\n" + header.Key + ": " + string.Join(", ", header.Value);
        }

        string? requestBody = requestData.ReadAsStringAsync().Result;
        ArgumentNullException.ThrowIfNull(requestBody);

        rawRequest += "\r\n\r\n";
        rawRequest += requestBody;

        MemoryStream stream = new(Encoding.UTF8.GetBytes(requestBody));
        ValueTask<T?> reqJson = JsonSerializer.DeserializeAsync<T>(stream, jsonOptions);
        if (reqJson.IsCompleted)
        { reqRes.ReqeustBody = reqJson.Result; }

        return reqJson;
    }

    public static string SetFileName(HttpRequestData requestData)
    {
        string fileName = requestData.FunctionContext.TraceContext.TraceParent;
        ArgumentNullException.ThrowIfNull(fileName);

        string extension;
        if (requestData.Headers.TryGetValues("Content-Type", out IEnumerable<string>? headerValues))
        {
            if (headerValues.FirstOrDefault() == "application/json")
            {
                extension = ".json";
            }
            else { extension = ".txt"; }
        }
        else { extension = ".txt"; }
        fileName += extension;

        return fileName;
    }

    public static void UploadBlob(string fileName, string? folder, string? content)
    {
        ArgumentException.ThrowIfNullOrEmpty(fileName, nameof(fileName));
        ArgumentException.ThrowIfNullOrEmpty(content, nameof(content));

        if (folder is not null)
        {
            fileName = folder + "/" + fileName;
        }

        string? connString = Environment.GetEnvironmentVariable("AzureWebJobsStorage");
        ArgumentNullException.ThrowIfNull(connString);

        string? containerName = Environment.GetEnvironmentVariable("ContainerName");
        ArgumentNullException.ThrowIfNull(containerName);

        BlobServiceClient serviceClient = new(connString);
        BlobContainerClient containerClient = serviceClient.GetBlobContainerClient(containerName);
        BlobClient blobClient = containerClient.GetBlobClient(fileName);

        MemoryStream stream = new(Encoding.UTF8.GetBytes(content));
        blobClient.UploadAsync(stream);
    }

    public static JsonObject SerializeSqlCommand(SqlCommand cmd)
    {
        JsonArray jsonArray = [];
        SqlParameterCollection parameters = cmd.Parameters;
        foreach (SqlParameter parameter in parameters)
        {
            jsonArray.Add(new JsonObject()
            {
                {"parameterName", parameter.ParameterName },
                {"parameterValue", parameter.Value.ToString() }
            });
        }

        JsonObject json = new()
        {
            { "commandText", cmd.CommandText },
            { "parameters", jsonArray }
        };
        return json;
    }

    public static JsonSerializerOptions GetJsonOptions()
    {
        return new()
        {
            DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull,
            PropertyNameCaseInsensitive = true,
            WriteIndented = true            
        };
    }

    public class RequestResponse
    {
        public string? Id { get; set; }
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
        public string? Method { get; set; }
        public string? Url { get; set; }
        public Object? ReqeustBody { get; set; }
        public string? StatusCode { get; set; }
        public Object? ResponseBody { get; set; }
    }

    public enum ErrorCode
    {
        Undefined,
        ArgumentNullException,
        ArgumentEmptyException,
        ArgumentOutOfRangeException
    }

    public static ExternalSystem? GetExternalSystemByCode(string externalSystemCode)
    {
        string? connString = Environment.GetEnvironmentVariable("SqlConnectionString");
        ArgumentNullException.ThrowIfNull(connString);

        using SqlConnection conn = new(connString);
        {
            conn.Open();
            using SqlCommand cmd = conn.CreateCommand();
            {
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandText = "dbo.ExternalSystemGet";
                cmd.Parameters.AddWithValue("@externalSystemCode", externalSystemCode);
                SqlDataReader reader = cmd.ExecuteReader();

                if (reader.HasRows)
                {
                    if (reader.Read())
                    {
                        ExternalSystem externalSystem = new()
                        {
                            Id = reader.GetGuid(reader.GetOrdinal("Id")),
                            Code = reader.GetString(reader.GetOrdinal("Code")),
                            Name = reader.IsDBNull(reader.GetOrdinal("Name")) ? null : reader.GetString(reader.GetOrdinal("Name")),
                            ExternalSystemType = reader.IsDBNull(reader.GetOrdinal("ExternalSystemType")) ? null : reader.GetString(reader.GetOrdinal("ExternalSystemType"))
                        };
                        reader.Dispose();
                        return(externalSystem);
                    }
                }
                conn.Close();
            }
        }
        return null;
    }

    public static async Task<string> GetRequestBodyAsString(HttpRequest request)
    {
        using var sr = new StreamReader(request.Body);
        return await sr.ReadToEndAsync();
    }


    public static string? GetExternalSystemCode(HttpRequest req)
    {
        string? externalSystemCode = null;
        if (req.Headers.TryGetValue("X-ExternalSystemCode", out StringValues values))
        {
            externalSystemCode = values.First();
        }
        return externalSystemCode;
    }


    public static string? GetQueryValue(IQueryCollection query, string name)
    {
        string? returnValue = null;
        if (query.TryGetValue(name, out StringValues values))
        {
            returnValue = values.First();
        }
        return returnValue;
    }


    public static HttpClient GetHttpClient(string apiBaseUrl, string subscriptionKey, string externalSystemCode)
    {
        var client = new HttpClient
        {
            BaseAddress = new Uri(apiBaseUrl.TrimEnd('/') + '/')
        };
        client.DefaultRequestHeaders.Add("Ocp-Apim-Subscription-Key", subscriptionKey);
        client.DefaultRequestHeaders.Add("X-ExternalSystemCode", externalSystemCode);
        return client;
    }
}

