﻿using System.Text.Json.Serialization;

namespace WSA.Retail.Integration.Model
{
    public class Coupling
    {
        [JsonPropertyName("id")]
        public Guid? Id { get; set; }
        [JsonPropertyName("externalSystemCode")]
        public string? ExternalSystemCode { get; set; }
        [JsonPropertyName("externalCode")]
        public string? ExternalCode { get; set; }
        [JsonPropertyName("entityCode")]
        public string? EntityCode { get; set; }


        public Coupling() { }
    }
}
