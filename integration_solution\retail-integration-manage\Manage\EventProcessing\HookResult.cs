﻿using WSA.Retail.Integration.Core;

namespace WSA.Retail.Integration.Manage.EventProcessing;

public class HookResult<T>(T? value, bool isHandled = false)
    where T : class, IIdentifiable
{
    public T? Value { get; set; } = value;
    public bool IsHandled { get; set; } = isHandled;

    public static HookResult<T> ContinueWith(T? value) =>
        new(value, isHandled: false);

    public static HookResult<T> Handled(T value) =>
        new(value, isHandled: true);
}
