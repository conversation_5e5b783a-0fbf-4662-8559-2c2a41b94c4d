CREATE TABLE [dbo].[PaymentMethod]
(
    [Id]                UNIQUEIDENTIFIER CONSTRAINT [DF_PaymentMethod_Id] DEFAULT NEWID() NOT NULL,
    [Code]              NVARCHAR(20) NOT NULL,
    [Name]              NVARCHAR(100) NOT NULL,
    [CreatedOn]         DATETIME2 CONSTRAINT [DF_PaymentMethod_CreatedOn] DEFAULT (sysutcdatetime()) NOT NULL,
    [ModifiedOn]        DATETIME2 CONSTRAINT [DF_PaymentMethod_ModifiedOn] DEFAULT (sysutcdatetime()) NOT NULL,
    CONSTRAINT          [PK_PaymentMethod_Id] PRIMARY KEY CLUSTERED ([Id] ASC)
)
GO

CREATE UNIQUE INDEX IX_PaymentMethod_Code
ON [dbo].[PaymentMethod] ([Code])
GO

CREATE INDEX IX_PaymentMethod_Name
ON [dbo].[PaymentMethod] ([Name])
GO