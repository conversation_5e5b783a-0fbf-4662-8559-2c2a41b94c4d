﻿CREATE TABLE [cosium].[agenda](
	[id] [nvarchar](50) NOT NULL,
	[object] [varchar](max) NULL,
	[jour<PERSON><PERSON><PERSON>e] [varchar](max) NULL,
	[remarque] [varchar](max) NULL,
	[utilisateur] [varchar](max) NULL,
	[refclient] [varchar](max) NULL,
	[categorie] [varchar](max) NULL,
	[daterdv] [varchar](max) NULL,
	[datefinrdv] [varchar](max) NULL,
	[supprime] [varchar](max) NULL,
	[rdvmanque] [varchar](max) NULL,
	[rdvprive] [varchar](max) NULL,
	[rdvarrive] [varchar](max) NULL,
	[compterendu] [nvarchar](max) NULL,
	[datemodif] [varchar](max) NULL,
	[creationdate] [varchar](max) NULL,
	[rdvannule] [varchar](max) NULL,
	[creator_id] [varchar](max) NULL,
	[CreatedOn] [datetime2](7) NOT NULL,
	[ModifiedOn] [datetime2](7) NOT NULL,
	[site_id] [varchar](max) NULL,
 CONSTRAINT [PK_agenda] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

ALTER TABLE [cosium].[agenda] ADD  CONSTRAINT [DF_agenda_CreatedOn]  DEFAULT (sysutcdatetime()) FOR [CreatedOn]
GO

ALTER TABLE [cosium].[agenda] ADD  CONSTRAINT [DF_agenda_ModifiedOn]  DEFAULT (sysutcdatetime()) FOR [ModifiedOn]
GO
