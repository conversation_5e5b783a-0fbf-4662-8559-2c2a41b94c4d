namespace WSA.Integration;

using Microsoft.Inventory.Posting;
using Microsoft.Inventory.Ledger;
using Microsoft.Inventory.Journal;

codeunit 50142 "Adjustment Events"
{
    var
        RetailIntegrationSetup: Record "Retail Integration Setup";

    [EventSubscriber(ObjectType::Codeuni<PERSON>, Codeunit::"Item Jnl.-Post Batch", 'OnAfterCode', '', false, false)]
    local procedure RaiseEventOnAfterCode(
        var ItemJournalLine: Record "Item Journal Line";
        ItemJournalBatch: Record "Item Journal Batch";
        ItemRegNo: Integer;
        WhseRegNo: Integer)

    var
        ItemRegister: Record "Item Register";
        ItemLedgerEntry: Record "Item Ledger Entry";
        IsHandled: Boolean;

    begin
        OnBeforeRaiseEventOnAfterCode(
            ItemJournalLine,
            ItemJournalBatch,
            ItemRegNo,
            WhseRegNo,
            IsHandled);

        if IsHandled then
            exit;

        RetailIntegrationSetup.SafeGet();
        if not RetailIntegrationSetup.Enabled then
            exit;

        if not (ItemRegister.Get(ItemRegNo)) then
            exit;

        if not (TryValidateItemRegisterFields(ItemRegister)) then
            exit;

        ItemLedgerEntry.SetFilter("Entry No.", '%1..%2', ItemRegister."From Entry No.", ItemRegister."To Entry No.");
        ItemLedgerEntry.SetFilter("Entry Type", '%1|%2', "Item Ledger Entry Type"::"Positive Adjmt.", "Item Ledger Entry Type"::"Negative Adjmt.");
        if ItemLedgerEntry.IsEmpty() then
            exit;

        if ItemLedgerEntry.FindSet() then
            repeat
                if not TryRaiseEvent(ItemLedgerEntry, ItemJournalBatch."WSA Integrate with POS") then
                ; // Take no action on failure.
            until ItemLedgerEntry.Next() = 0;
    end;


    [TryFunction]
    local procedure TryValidateItemRegisterFields(var ItemRegister: Record "Item Register")
    begin
        ItemRegister.TestField("From Entry No.");
        ItemRegister.TestField("To Entry No.");
    end;


    [TryFunction]
    local procedure TryRaiseEvent(
        ItemLedgerEntry: Record "Item Ledger Entry";
        IntegrateWithPos: Boolean)

    var
        IntegrationManagement: Codeunit "Integration Management";
        Common: Codeunit "WSA Common";
        JObject: JsonObject;
        IsHandled: Boolean;

    begin
        OnBeforeTryRaiseEvent(ItemLedgerEntry, IntegrateWithPos, IsHandled);
        if IsHandled then
            exit;

        RetailIntegrationSetup.SafeGet();
        RetailIntegrationSetup.TestField(Enabled, true);
        JObject := Common.AdjustmentToJson(ItemLedgerEntry, IntegrateWithPos);

        OnBeforeCallTryRaiseEvent(ItemLedgerEntry, JObject, IsHandled);
        if IsHandled then
            exit;

        IntegrationManagement.TryRaiseEvent(JObject, 'adjustments', Format(ItemLedgerEntry.SystemId).TrimStart('{').TrimEnd('}'));
    end;


    [IntegrationEvent(false, false)]
    local procedure OnBeforeRaiseEventOnAfterCode(
        var ItemJournalLine: Record "Item Journal Line";
        ItemJournalBatch: Record "Item Journal Batch";
        ItemRegNo: Integer;
        WhseRegNo: Integer;
        var IsHandled: Boolean)
    begin
    end;


    [IntegrationEvent(false, false)]
    local procedure OnBeforeTryRaiseEvent(
        ItemLedgerEntry: Record "Item Ledger Entry";
        IntegrateWithPos: Boolean;
        var IsHandled: Boolean)
    begin
    end;


    [IntegrationEvent(false, false)]
    local procedure OnBeforeCallTryRaiseEvent(
        ItemLedgerEntry: Record "Item Ledger Entry";
        JObject: JsonObject;
        var IsHandled: Boolean)
    begin
    end;
}
