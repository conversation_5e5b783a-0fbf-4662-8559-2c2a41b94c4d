﻿CREATE TYPE [pim].[BundleSetUpsertTableType] AS TABLE(
	[BundleId] [uniqueidentifier] NULL,
	[BundleName] [nvarchar](100) NULL,
	[BundleType] [nvarchar](20) NULL,
	[BundleIsDefault] [bit] NULL,
	[BundleIsMandatory] [bit] NULL,
	[BundleIsMasterData] [bit] NULL,
	[BundleRanking] [int] NULL,
	[BundleState] [nvarchar](20) NULL,
	[BundleCreatedAt] [datetime2](7) NULL,
	[BundleUpdatedAt] [datetime2](7) NULL,
	[ProductId] [uniqueidentifier] NULL,
	[ProductIsAvailable] [bit] NULL,
	[ProductIsPhasedOut] [bit] NULL,
	[ProductIsDefault] [bit] NULL,
	[ProductRanking] [int] NULL,
	[ProductSku] [nvarchar](30) NULL,
	[ProductState] [nvarchar](20) NULL,
	[ProductCreatedAt] [datetime2](7) NULL,
	[ProductUpdatedAt] [datetime2](7) NULL
)
GO