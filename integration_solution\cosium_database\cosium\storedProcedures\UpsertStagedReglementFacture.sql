﻿CREATE PROCEDURE [cosium].[UpsertStagedReglementFacture]
AS

        SET XACT_ABORT ON
        BEGIN TRANSACTION
     
       UPDATE cosium.ReglementFacture
          SET [numcheque] = Source.[numcheque],
              [dateecheance] = Source.[dateecheance],
              [montant] = Source.[montant],
              [dateencaissee] = Source.[dateencaissee],
              [typereglement] = Source.[typereglement],
              [refremisebanque] = Source.[refremisebanque],
              [refbanque] = Source.[refbanque],
              [nomemetteur] = Source.[nomemetteur],
              [datecreation] = Source.[datecreation],
              [refclient] = Source.[refclient],
              [centre] = Source.[centre],
              [typeemetteur] = Source.[typeemetteur],
              [refcoordbancaires] = Source.[refcoordbancaires],
              [refsymetrique] = Source.[refsymetrique],
              [reffournisseur] = Source.[reffournisseur],
              [reftiers] = Source.[reftiers],
              [multiclients] = Source.[multiclients],
              [alerterglt] = Source.[alerterglt],
              [montantorigine] = Source.[montantorigine],
              [numordrevrt] = Source.[numordrevrt],
              [dateexportcompta] = Source.[dateexportcompta],
              [munom] = Source.[munom],
              [csnom] = Source.[csnom],
              [datemodif] = Source.[datemodif]

         FROM cosium.ReglementFacture

              INNER JOIN cosium.staging_ReglementFacture AS Source
                      ON ReglementFacture.id = Source.id

        WHERE ReglementFacture.datemodif <> Source.datemodif;

       INSERT INTO cosium.ReglementFacture (
              [id],
              [numcheque],
              [dateecheance],
              [montant],
              [dateencaissee],
              [typereglement],
              [refremisebanque],
              [refbanque],
              [nomemetteur],
              [datecreation],
              [refclient],
              [centre],
              [typeemetteur],
              [refcoordbancaires],
              [refsymetrique],
              [reffournisseur],
              [reftiers],
              [multiclients],
              [alerterglt],
              [montantorigine],
              [numordrevrt],
              [dateexportcompta],
              [munom],
              [csnom],
              [datemodif]
              )

       SELECT Source.[id],
              Source.[numcheque],
              Source.[dateecheance],
              Source.[montant],
              Source.[dateencaissee],
              Source.[typereglement],
              Source.[refremisebanque],
              Source.[refbanque],
              Source.[nomemetteur],
              Source.[datecreation],
              Source.[refclient],
              Source.[centre],
              Source.[typeemetteur],
              Source.[refcoordbancaires],
              Source.[refsymetrique],
              Source.[reffournisseur],
              Source.[reftiers],
              Source.[multiclients],
              Source.[alerterglt],
              Source.[montantorigine],
              Source.[numordrevrt],
              Source.[dateexportcompta],
              Source.[munom],
              Source.[csnom],
              Source.[datemodif]

         FROM cosium.staging_ReglementFacture AS Source

        WHERE NOT EXISTS (SELECT * FROM cosium.ReglementFacture WHERE id = Source.id)

     TRUNCATE TABLE cosium.staging_ReglementFacture
       
       COMMIT TRANSACTION;