﻿namespace WSA.Retail.Integration.PIM.Models.Products
{
    public class PimProductParentEntity : IPimProductParentEntity, WSA.Retail.Integration.Core.IIdentifiable
    {
        public required Guid Id { get; set; } = Guid.Empty;
        public required Guid ProductId { get; set; }
        public required Guid ParentId { get; set; }
        public required DateTime SystemCreatedOn { get; set; }
        public required DateTime SystemModifiedOn { get; set; }

        public required virtual PimProductEntity PimChildProductEntity { get; set; }
        public required virtual PimProductEntity PimParentProductEntity { get; set; }
    }
}
