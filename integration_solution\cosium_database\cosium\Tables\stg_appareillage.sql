﻿CREATE TABLE [cosium].[stg_appareillage](
	[id] [nvarchar](50) NOT NULL,
	[refappareil] [varchar](max) NULL,
	[hstatut] [varchar](max) NULL,
	[datedebut] [varchar](max) NULL,
	[datefin] [varchar](max) NULL,
	[numpiece] [varchar](max) NULL,
	[hcommentaire] [varchar](max) NULL,
	[hrefclient] [varchar](max) NULL,
	[hcote] [varchar](max) NULL,
	[fingarantie] [varchar](max) NULL,
	[finextgarantie] [varchar](max) NULL,
	[prescripteur] [varchar](max) NULL,
	[refuser] [varchar](max) NULL,
	[dateprescription] [varchar](max) NULL,
	[dateappareillage] [varchar](max) NULL,
	[prixvendu] [varchar](max) NULL,
	[origine] [varchar](max) NULL,
	[datemodif] [varchar](max) NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
