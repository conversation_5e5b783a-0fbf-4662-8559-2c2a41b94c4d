﻿using WSA.Retail.Integration.PIM.Models.Brands;

namespace WSA.Retail.Integration.PIM.Models.Countries;

public class PimCountryEntity : IPimCountryEntity, WSA.Retail.Integration.Core.IIdentifiable
{
    public required Guid Id { get; set; } = Guid.Empty;
    public required string Code { get; set; }
    public string? Name { get; set; }
    public required DateTime SystemCreatedOn { get; set; }
    public required DateTime SystemModifiedOn { get; set; }

    public virtual ICollection<PimBrandEntity> Brands { get; set; } = [];
}