﻿namespace WSA.Retail.Integration.Manage
{
    public class AddressEventHubDto
    {
        public string? Address1 { get; set; }
        public string? Address2 { get; set; }
        public string? Country { get; set; }
        public string? City { get; set; }
        public string? PostCode { get; set; }
        public string? State { get; set; }
        public string? Email { get; set; }
        public string? PhoneNumber { get; set; }
        public string? ContactPerson { get; set; }
    }
}
