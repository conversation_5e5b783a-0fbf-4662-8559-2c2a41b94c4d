﻿CREATE VIEW [dbo].[Clinics]
AS

  WITH Source AS (
SELECT Clinic.Id,
       ExternalSystem.Code AS ExternalSystemCode,
       Clinic.Code,
       LatestCouplings.ExternalCode,
       Clinic.[Name],
       Clinic.[Address],
       Clinic.Address2,
       Clinic.City,
       Clinic.Region,
       Clinic.Country,
       Clinic.PostalCode,
       Clinic.Phone,
       Company.Id AS CompanyId,
       Company.Code AS CompanyCode,
       LatestCompanyCouplings.ExternalCode AS CompanyExternalCode,
       Company.[Name] AS CompanyName,
       Clinic.CreatedOn,
       Clinic.ModifiedOn

  FROM dbo.Clinic

  LEFT JOIN dbo.Company
    ON Clinic.CompanyId = Company.Id

 CROSS JOIN dbo.ExternalSystem

 OUTER APPLY (
       SELECT TOP 1 Coupling.ExternalRecordId AS ExternalCode
         FROM dbo.Coupling
        WHERE Coupling.ExternalSystemId = ExternalSystem.Id
          AND Coupling.RecordId = Clinic.Id
        ORDER BY ModifiedOn DESC) AS LatestCouplings

 OUTER APPLY (
       SELECT TOP 1 Coupling.ExternalRecordId AS ExternalCode
         FROM dbo.Coupling
        WHERE Coupling.ExternalSystemId = ExternalSystem.Id
          AND Coupling.RecordId = Company.Id
        ORDER BY ModifiedOn DESC) AS LatestCompanyCouplings
     )

SELECT Source.Id,
       Source.ExternalSystemCode,
       Source.Code,
       Source.ExternalCode,
       Source.[Name],
       Source.[Address],
       Source.Address2,
       Source.City,
       Source.Region,
       Source.Country,
       Source.PostalCode,
       Source.CompanyId,
       Source.CreatedOn,
       Source.ModifiedOn,
       (
       SELECT Source.Id AS 'id',
              Source.ExternalSystemCode AS 'externalSystemCode',
              Source.Code AS 'code',
              Source.ExternalCode AS 'externalCode',
              Source.[Name] AS 'name',
              Source.[Address] AS 'address',
              Source.Address2 AS 'address2',
              Source.City AS 'city',
              Source.Region AS 'region',
              Source.Country AS 'country',
              Source.PostalCode AS 'postalCode',
              Source.Phone AS 'phone',
              source.CompanyId AS 'company.id',
              source.CompanyCode AS 'company.code',
              source.CompanyExternalCode AS 'company.externalCode',
              source.CompanyName AS 'company.name',
              Source.CreatedOn AS 'createdOn',
              Source.ModifiedOn AS 'modifiedOn'

           FOR JSON PATH, WITHOUT_ARRAY_WRAPPER
       ) AS JSON

  FROM Source