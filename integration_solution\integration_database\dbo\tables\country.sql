﻿CREATE TABLE [dbo].[Country]
(
    [Id]                UNIQUEIDENTIFIER CONSTRAINT [DF_Country_Id] DEFAULT NEWID() NOT NULL,
    [Code]              NVARCHAR(20) NOT NULL,
    [Name]              NVARCHAR(100) NOT NULL,
    [CreatedOn]         DATETIME2 CONSTRAINT [DF_Country_CreatedOn] DEFAULT (sysutcdatetime()) NOT NULL,
    [ModifiedOn]        DATETIME2 CONSTRAINT [DF_Country_ModifiedOn] DEFAULT (sysutcdatetime()) NOT NULL,
    CONSTRAINT          [PK_Country_Id] PRIMARY KEY CLUSTERED ([Id] ASC)
)
GO

CREATE UNIQUE INDEX IX_Country_Code
ON [dbo].[Country] ([Code])
GO

CREATE INDEX IX_Country_Name
ON [dbo].[Country] ([Name])
GO