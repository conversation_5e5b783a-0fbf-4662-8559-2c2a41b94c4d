﻿CREATE VIEW [dbo].[PurchaseOrders]
AS

SELECT Source.Id,
       Source.DocumentNumber,
       c1.ExternalRecordId AS ExternalReference,
       Source.DocumentDate AS DocumentDate,
       ExternalSystem.Id AS ExternalSystemId,
       ExternalSystem.Code As ExternalSystemCode,
       Source.ModifiedOn,
       t1.JSON

  FROM dbo.PurchaseOrder AS Source

 OUTER APPLY dbo.ExternalSystem

 OUTER APPLY (SELECT ExternalRecordId FROM dbo.Coupling WHERE RecordId = Source.Id AND ExternalSystemId = ExternalSystem.Id) AS c1

 OUTER APPLY (SELECT(
              SELECT PurchaseOrder.Id AS id,
                     PurchaseOrder.DocumentNumber AS documentNumber,
                     c1.ExternalRecordId AS externalReference,
                     PurchaseOrder.AlternateNumber AS alternateNumber,
                     PurchaseOrder.DocumentDate AS documentDate,
                     Vendor.Id AS 'vendor.id',
                     Vendor.Code AS 'vendor.code',
                     (SELECT TOP 1 ExternalRecordId FROM dbo.Coupling WHERE RecordId = Vendor.Id AND ExternalSystemId = ExternalSystem.Id) AS 'vendor.externalCode',
                     Vendor.[Name] AS 'vendor.name',
                     Clinic.Id AS 'clinic.id',
                     Clinic.Code AS 'clinic.code',
                     (SELECT TOP 1 ExternalRecordId FROM dbo.Coupling WHERE RecordId = Clinic.Id AND ExternalSystemId = ExternalSystem.Id) AS 'clinic.externalCode',
                     Clinic.[Name] AS 'clinic.name',
                     (
                            SELECT PurchaseOrderLine.[Sequence] AS [sequence],
                                   Product.Id AS 'product.id',
                                   Product.Code AS 'product.code',
                                   (SELECT TOP 1 ExternalRecordId FROM dbo.Coupling WHERE RecordId = Product.Id AND ExternalSystemId = ExternalSystem.Id) AS 'product.externalCode',
                                   Product.[Name] AS 'product.name',
                                   PurchaseOrderLine.[Description] AS [description],
                                   PurchaseOrderLine.[Quantity] AS quantity,
                                   PurchaseOrderLine.[UnitPrice] AS unitPrice,
                                   PurchaseOrderLine.[GrossAmount] AS grossAmount,
                                   PurchaseOrderLine.[DiscountAmount] AS discountAmount,
                                   PurchaseOrderLine.[AmountInclTax] AS amountInclTax,
                                   PurchaseOrderLine.[TaxAmount] AS taxAmount,
                                   PurchaseOrderLine.[AmountExclTax] AS amountExclTax

                              FROM dbo.PurchaseOrderLine

                                   LEFT JOIN dbo.Product
                                          ON PurchaseOrderLine.ProductId = Product.Id

                             WHERE PurchaseOrderLine.PurchaseOrderId = PurchaseOrder.Id

                             ORDER BY PurchaseOrderLine.[Sequence]

                               FOR JSON PATH
                     ) AS purchaseOrderLines

                FROM dbo.PurchaseOrder

                     INNER JOIN dbo.Vendor
                             ON PurchaseOrder.VendorId = Vendor.Id

                     INNER JOIN dbo.Clinic
                             ON PurchaseOrder.ClinicId = Clinic.Id

               WHERE PurchaseOrder.Id = Source.Id

                FOR JSON PATH, WITHOUT_ARRAY_WRAPPER
                ) AS JSON
                ) AS t1